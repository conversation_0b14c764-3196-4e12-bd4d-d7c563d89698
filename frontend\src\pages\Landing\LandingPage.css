.container {
  margin: 0 auto;
  max-width: 1340px;
  padding-left: 20px;
  padding-right: 20px;
  display: flex;
  flex-direction: row;
}

.container_top {
  margin-top: 10px;
  align-items: center;
  justify-content: space-between;
  font-size: 20px;
  font-family: "Montserrat";
}

.left_navbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.inner_navbar {
  display: flex;
  flex-direction: row;
  gap: 0 65px;
  margin-left: 80px;
}

.login_button {
  background-color: #97cb9c;
  width: 200px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30px;
  color: white;
}

.login_button:hover {
  transition-duration: 0.2s;
  transform: scale(1.05);
  cursor: pointer;
}

.main_info {
  justify-content: space-between;
  margin-top: 60px;
}

.main_info_title a {
  font-size: 60px;
  font-weight: 800;
  line-height: 70px;
}

.left_info_text {
  max-width: 650px;
}

.left_info_text p {
  margin-top: 40px;
  font-size: 25px;
  line-height: 30px;
  max-width: 650px;
  font-family: "Montserrat";
  font-weight: 500;
}

.left_info_text button {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #388146;
  width: 275px;
  height: 50px;
  border-radius: 30px;
  color: white;
  font-size: 20px;
  font-family: "Montserrat";
}

.left_info_text button:hover {
  transition-duration: 0.2s;
  transform: scale(1.05);
  cursor: pointer;
}

.main_info_title {
  line-height: 10px;
  font-size: 40px;
  font-family: "Montserrat";
}

.main_info_title span {
  color: #97cb9c;
}

.right_info_pics {
  height: 400px;
}

.right_info_pics img {
  width: 650px;
  height: auto;
  filter: drop-shadow(5px 5px 5px #7ea6ad);
}

.more_info {
  justify-content: space-between;
  margin-top: 30px;
}

.left_cards {
  min-width: 550px;
  display: flex;
  justify-content: space-between;
  height: 200px;
  column-gap: 20px;
}

.black_text {
  color: black;
}

.right_more_info {
  display: flex;
  flex-direction: column;
}

.statistics {
  display: flex;
  flex-direction: row;
}

.quote_block {
  width: 565px;
  display: flex;
  justify-content: center;
  border: solid #354f52 2px;
  border-radius: 60px;
  padding: 5px;
}

.quote {
  height: 50px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
}

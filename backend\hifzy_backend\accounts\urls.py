from django.urls import path
from .views import (
    GoogleLoginView,
    # UserRegistrationView,
    VerifiedUserRegistrationView,
    UserLoginView,
    PasswordChangeView,
    UserProfileCompleteView,
    AddBookmarkAyahView,
    RemoveBookmarkAyahView,
    ListBookmarksView,
    UpdateUserProfileCompleteView,
    DevLoginView,
    EmailAvailabilityView,
    EmailVerificationRequestView,
    EmailVerificationCodeView,
    PasswordResetRequestView,
    PasswordResetVerificationView,
    PasswordResetCompleteView,
    AyahLikeView,
    AyahDislikeView,
    AyahRemoveReactionView,
    AyahReportView,
)

urlpatterns = [
    # Authentication
    path("auth/google/", GoogleLoginView.as_view(), name="google-login"),
    path("auth/dev-login/", DevLoginView.as_view(), name="dev-login"),
    # path("auth/register/", UserRegistrationView.as_view(), name="user-registration"),
    path("auth/register-verified/", VerifiedUserRegistrationView.as_view(), name="verified-user-registration"),
    path("auth/login/", UserLoginView.as_view(), name="user-login"),
    path("auth/change-password/", PasswordChangeView.as_view(), name="change-password"),
    path("auth/check-email/", EmailAvailabilityView.as_view(), name="check-email-availability"),
    # Email Verification for Registration
    path("auth/verify-email/request/", EmailVerificationRequestView.as_view(), name="email-verification-request"),
    path("auth/verify-email/code/", EmailVerificationCodeView.as_view(), name="email-verification-code"),
    # Password Reset with Email Verification
    path("auth/password-reset/request/", PasswordResetRequestView.as_view(), name="password-reset-request"),
    path("auth/password-reset/verify/", PasswordResetVerificationView.as_view(), name="password-reset-verification"),
    path("auth/password-reset/complete/", PasswordResetCompleteView.as_view(), name="password-reset-complete"),
    # User Profile
    path("profile/", UserProfileCompleteView.as_view(), name="user-profile-complete"),
    path("profile/update", UpdateUserProfileCompleteView.as_view(), name="user-profile-update"),
    # Bookmarks
    path("bookmarks/add/", AddBookmarkAyahView.as_view(), name="add-bookmark"),
    path("bookmarks/delete", RemoveBookmarkAyahView.as_view(), name="remove-bookmark"),
    path("bookmarks/list/", ListBookmarksView.as_view(), name="list-bookmarks"),
    # Ayah Interactions
    path("ayah/like/", AyahLikeView.as_view(), name="ayah-like"),
    path("ayah/dislike/", AyahDislikeView.as_view(), name="ayah-dislike"),
    path("ayah/remove-reaction/", AyahRemoveReactionView.as_view(), name="ayah-remove-reaction"),
    path("ayah/report/", AyahReportView.as_view(), name="ayah-report"),
]

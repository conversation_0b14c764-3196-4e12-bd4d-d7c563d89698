"""
Response schema serializers for the progress app.

This module provides response serializers for all progress-related endpoints
to ensure comprehensive API documentation in Swagger/OpenAPI.
"""

from rest_framework import serializers
from core.response_schemas import (
    BaseSuccessResponseSerializer,
    BaseErrorResponseSerializer,
    ExperienceAwardedSerializer,
    ProgressSummarySerializer,
    SurahProgressSerializer,
    UnlockInfoSerializer,
    SurahBasicInfoSerializer,
)


# Step-by-step game completion response schemas
class AyahCompletionInfoSerializer(serializers.Serializer):
    """Serializer for ayah information in completion responses."""

    id = serializers.IntegerField(help_text="Ayah ID")
    ayah_number = serializers.IntegerField(help_text="Ayah number within the surah")
    surah_id = serializers.IntegerField(help_text="Surah ID")
    image = serializers.URLField(allow_null=True, help_text="URL to ayah image")


class SurahInfoSerializer(serializers.Serializer):
    """Serializer for surah information in completion responses."""

    id = serializers.IntegerField(help_text="Surah ID")
    surah_id = serializers.IntegerField(help_text="Surah number")
    ayah_count = serializers.IntegerField(help_text="Total number of ayahs in the surah")
    xp_per_ayah = serializers.IntegerField(help_text="XP awarded per ayah for this surah")


class UserAyahCompletionSerializer(serializers.Serializer):
    """Serializer for user ayah completion information."""

    ayah = AyahCompletionInfoSerializer(help_text="Ayah information")
    ayah_id = serializers.IntegerField(help_text="Ayah ID")
    user_id = serializers.IntegerField(help_text="User ID")
    is_completed = serializers.BooleanField(help_text="Whether the ayah is completed")
    is_bookmarked = serializers.BooleanField(help_text="Whether the ayah is bookmarked")
    created_at = serializers.DateTimeField(help_text="When the completion record was created")


class UserSurahExperienceSerializer(serializers.Serializer):
    """Serializer for user surah experience information."""

    user_id = serializers.IntegerField(help_text="User ID")
    surah = SurahInfoSerializer(help_text="Surah information")
    experience_points = serializers.IntegerField(help_text="Total experience points for this surah")
    is_completed = serializers.BooleanField(help_text="Whether the surah is completed")
    is_bookmarked = serializers.BooleanField(help_text="Whether the surah is bookmarked")
    is_skipped = serializers.BooleanField(help_text="Whether the surah was skipped in pedagogical progression")
    created_at = serializers.DateTimeField(help_text="When the experience tracking started")


class NextUnlockInfoSerializer(serializers.Serializer):
    """Serializer for next unlock information."""

    next_surah_number = serializers.IntegerField(allow_null=True, help_text="Next surah number in pedagogical sequence")
    current_surah_position = serializers.IntegerField(help_text="Current surah position in learning sequence")
    next_surah_position = serializers.IntegerField(
        allow_null=True, help_text="Next surah position in learning sequence"
    )
    current_xp = serializers.IntegerField(help_text="Current XP for the surah")
    threshold_xp = serializers.IntegerField(help_text="XP threshold required to unlock next surah")
    total_possible_xp = serializers.IntegerField(help_text="Total possible XP for the current surah")
    xp_needed = serializers.IntegerField(help_text="XP needed to unlock next surah")
    progress_percentage = serializers.FloatField(help_text="Progress percentage towards unlocking next surah")
    is_unlocked = serializers.BooleanField(help_text="Whether the next surah is already unlocked")


class StepByStepCompletionDataSerializer(serializers.Serializer):
    """Data structure for step-by-step game completion response."""

    experience_awarded = serializers.IntegerField(help_text="XP awarded for this completion (20 XP per ayah)")
    surah_experience = UserSurahExperienceSerializer(allow_null=True, help_text="Updated surah experience data")
    user_progress = ProgressSummarySerializer(help_text="Updated user progress data")
    next_unlock_info = NextUnlockInfoSerializer(allow_null=True, help_text="Information about next surah unlock")


class StepByStepCompletionSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful step-by-step game completion."""

    data = StepByStepCompletionDataSerializer(help_text="Step-by-step completion data")


# Game completion response schemas
class SurahGameCompletionSerializer(serializers.Serializer):
    """Serializer for surah game completion information."""

    id = serializers.IntegerField(help_text="Game completion record ID")
    user_id = serializers.IntegerField(help_text="User ID")
    game_type = serializers.CharField(help_text="Type of game completed (shuffling, matching, memory, audio)")
    progress_ayah_count = serializers.IntegerField(help_text="Number of ayahs in the completed game")
    new_ayahs = serializers.IntegerField(help_text="Number of new ayahs that contributed to XP")
    created_at = serializers.DateTimeField(help_text="When the game was completed")


class GameCompletionDataSerializer(serializers.Serializer):
    """Data structure for game completion response."""

    experience_awarded = serializers.IntegerField(
        help_text="XP awarded for this completion (calculated from new_ayahs * xp_per_ayah)"
    )
    surah_experience = UserSurahExperienceSerializer(allow_null=True, help_text="Updated surah experience data")
    next_unlock_info = NextUnlockInfoSerializer(allow_null=True, help_text="Information about next surah unlock")
    game_completion = SurahGameCompletionSerializer(allow_null=True, help_text="Game completion record")


class GameCompletionSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful game completion."""

    data = GameCompletionDataSerializer(help_text="Game completion data")


# Surah progress response schemas
class SurahProgressDataSerializer(serializers.Serializer):
    """Data structure for surah progress response."""

    surah_id = serializers.IntegerField(help_text="Surah ID")
    name = serializers.CharField(help_text="Surah name")
    experience_points = serializers.IntegerField(help_text="Current experience points for this surah")
    completed_ayahs = serializers.IntegerField(help_text="Number of completed ayahs")
    total_ayahs = serializers.IntegerField(help_text="Total ayahs in the surah")
    next_unlock_info = UnlockInfoSerializer(allow_null=True, help_text="Information about next surah unlock")
    is_unlocked = serializers.BooleanField(help_text="Whether this surah is unlocked")


class SurahProgressSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful surah progress retrieval."""

    data = SurahProgressDataSerializer(help_text="Surah progress data")


# User progression summary response schemas
class ProgressionSummaryDataSerializer(serializers.Serializer):
    """Data structure for user progression summary response."""

    current_surah = serializers.IntegerField(help_text="Current surah user should work on")
    unlocked_surahs = serializers.ListField(child=serializers.IntegerField(), help_text="List of unlocked surah IDs")
    total_unlocked = serializers.IntegerField(help_text="Number of unlocked surahs")
    next_unlock_info = UnlockInfoSerializer(allow_null=True, help_text="Information about next surah unlock")
    completion_percentage = serializers.FloatField(help_text="Overall completion percentage")
    user_progress = ProgressSummarySerializer(help_text="User progress summary")


class ProgressionSummarySuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful user progression summary retrieval."""

    data = ProgressionSummaryDataSerializer(help_text="User progression summary data")


# Error response schemas
class ProgressValidationErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for progress validation errors."""

    pass


class ProgressNotFoundErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for progress not found errors."""

    pass


class ProgressAuthenticationErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for progress authentication errors."""

    pass


class ProgressForbiddenErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for progress forbidden errors."""

    pass


class ProgressInternalServerErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for progress internal server errors."""

    pass


# Endpoint-specific response schema collections
class StepByStepCompletionResponseSchemas:
    """Response schemas for step-by-step game completion endpoint."""

    success_200 = StepByStepCompletionSuccessResponseSerializer
    error_400 = ProgressValidationErrorResponseSerializer
    error_401 = ProgressAuthenticationErrorResponseSerializer
    error_403 = ProgressForbiddenErrorResponseSerializer
    error_404 = ProgressNotFoundErrorResponseSerializer
    error_500 = ProgressInternalServerErrorResponseSerializer


class GameCompletionResponseSchemas:
    """Response schemas for game completion endpoint."""

    success_200 = GameCompletionSuccessResponseSerializer
    error_400 = ProgressValidationErrorResponseSerializer
    error_401 = ProgressAuthenticationErrorResponseSerializer
    error_403 = ProgressForbiddenErrorResponseSerializer
    error_404 = ProgressNotFoundErrorResponseSerializer
    error_500 = ProgressInternalServerErrorResponseSerializer


# Skip surah response schemas
class SkippedSurahInfoSerializer(serializers.Serializer):
    """Serializer for skipped surah information."""

    surah_number = serializers.IntegerField(help_text="Surah number that was skipped")
    learning_position = serializers.IntegerField(help_text="Position in pedagogical learning sequence")
    is_skipped = serializers.BooleanField(help_text="Indicates that the surah was skipped (always True)")


class NextSurahInfoSerializer(serializers.Serializer):
    """Serializer for next surah information."""

    surah_number = serializers.IntegerField(help_text="Next surah number in pedagogical sequence")
    learning_position = serializers.IntegerField(help_text="Position in pedagogical learning sequence")
    is_unlocked = serializers.BooleanField(help_text="Whether the next surah is now unlocked")


class SkipSurahDataSerializer(serializers.Serializer):
    """Data structure for skip surah response."""

    skipped_surah = SkippedSurahInfoSerializer(help_text="Information about the skipped surah")
    next_surah = NextSurahInfoSerializer(help_text="Information about the newly unlocked surah")
    new_current_surah = serializers.IntegerField(help_text="User's new current surah number")
    next_unlock_info = NextUnlockInfoSerializer(
        allow_null=True, help_text="Information about unlocking the surah after the newly unlocked one"
    )


class SkipSurahSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful surah skip."""

    data = SkipSurahDataSerializer(help_text="Skip surah response data")


class SkipSurahResponseSchemas:
    """Response schemas for skip surah endpoint."""

    success_200 = SkipSurahSuccessResponseSerializer
    error_400 = ProgressValidationErrorResponseSerializer
    error_401 = ProgressAuthenticationErrorResponseSerializer
    error_403 = ProgressForbiddenErrorResponseSerializer
    error_404 = ProgressNotFoundErrorResponseSerializer
    error_500 = ProgressInternalServerErrorResponseSerializer


class SurahProgressResponseSchemas:
    """Response schemas for surah progress endpoint."""
    
    success_200 = SurahProgressSuccessResponseSerializer
    error_400 = ProgressValidationErrorResponseSerializer
    error_401 = ProgressAuthenticationErrorResponseSerializer
    error_403 = ProgressForbiddenErrorResponseSerializer
    error_404 = ProgressNotFoundErrorResponseSerializer
    error_500 = ProgressInternalServerErrorResponseSerializer


class ProgressionSummaryResponseSchemas:
    """Response schemas for user progression summary endpoint."""

    success_200 = ProgressionSummarySuccessResponseSerializer
    error_401 = ProgressAuthenticationErrorResponseSerializer
    error_500 = ProgressInternalServerErrorResponseSerializer


# Arabic Letter Progress Response Schemas
class ArabicLetterSerializer(serializers.Serializer):
    """Serializer for Arabic letter information."""

    id = serializers.IntegerField(help_text="Arabic letter ID")
    title = serializers.CharField(help_text="Arabic letter character")
    transcription = serializers.CharField(help_text="Letter transcription/pronunciation")
    audio_url = serializers.URLField(allow_null=True, required=False, help_text="Audio pronunciation URL")


class UserArabicProgressSerializer(serializers.Serializer):
    """Serializer for user Arabic letter progress."""

    id = serializers.IntegerField(help_text="Progress record ID")
    progress_status = serializers.CharField(help_text="Progress status (New, In progress, Completed)")
    user_id = serializers.IntegerField(help_text="User ID")
    arabic = ArabicLetterSerializer(help_text="Arabic letter information")
    created_at = serializers.DateTimeField(help_text="When progress tracking started")


class EnhancedUserArabicProgressSerializer(serializers.Serializer):
    """Serializer for enhanced user Arabic letter progress."""

    id = serializers.IntegerField(help_text="Progress record ID")
    progress_status = serializers.CharField(help_text="Progress status (New, In progress, Completed)")
    user_id = serializers.IntegerField(help_text="User ID")
    arabic = ArabicLetterSerializer(help_text="Arabic letter information")
    created_at = serializers.DateTimeField(help_text="When progress tracking started")
    has_audio = serializers.BooleanField(help_text="Whether the letter has audio pronunciation")
    practice_sessions = serializers.IntegerField(help_text="Number of practice sessions completed")


class ArabicLetterProgressStatsSerializer(serializers.Serializer):
    """Serializer for Arabic letter progress statistics."""

    total_available_letters = serializers.IntegerField(help_text="Total number of Arabic letters available")
    total_tracked_letters = serializers.IntegerField(help_text="Number of letters being tracked for this user")
    new_letters = serializers.IntegerField(help_text="Number of letters with 'New' status")
    in_progress_letters = serializers.IntegerField(help_text="Number of letters with 'In progress' status")
    completed_letters = serializers.IntegerField(help_text="Number of letters with 'Completed' status")
    completion_percentage = serializers.FloatField(help_text="Overall completion percentage")


# Request Serializers
class ArabicLetterProgressRequestSerializer(serializers.Serializer):
    """Serializer for Arabic letter progress requests."""

    letter_id = serializers.IntegerField(help_text="Arabic letter ID")


class UpdateArabicLetterProgressRequestSerializer(serializers.Serializer):
    """Serializer for updating Arabic letter progress."""

    letter_id = serializers.IntegerField(help_text="Arabic letter ID")
    progress_status = serializers.ChoiceField(
        choices=[("New", "New"), ("In progress", "In progress"), ("Completed", "Completed")],
        help_text="New progress status",
    )


# Response Data Serializers
class ArabicLetterProgressDataSerializer(serializers.Serializer):
    """Data structure for Arabic letter progress response."""

    progress = UserArabicProgressSerializer(help_text="User progress for the Arabic letter")


class ArabicLetterProgressListDataSerializer(serializers.Serializer):
    """Data structure for Arabic letter progress list response."""

    progress_list = UserArabicProgressSerializer(many=True, help_text="List of user progress for Arabic letters")


class AllArabicLettersProgressDataSerializer(serializers.Serializer):
    """Data structure for all Arabic letters progress response."""

    letters_progress = UserArabicProgressSerializer(many=True, help_text="Progress for all 28 Arabic letters")
    total_letters = serializers.IntegerField(help_text="Total number of Arabic letters (28)")
    initialized_count = serializers.IntegerField(
        help_text="Number of letters that were auto-initialized with 'New' status"
    )


class ArabicLetterProgressStatsDataSerializer(serializers.Serializer):
    """Data structure for Arabic letter progress statistics response."""

    stats = ArabicLetterProgressStatsSerializer(help_text="Progress statistics")


# Success Response Serializers
class ArabicLetterProgressSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful Arabic letter progress retrieval."""

    data = ArabicLetterProgressDataSerializer(help_text="Arabic letter progress data")


class ArabicLetterProgressListSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful Arabic letter progress list retrieval."""

    data = ArabicLetterProgressListDataSerializer(help_text="Arabic letter progress list data")


class AllArabicLettersProgressSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful all Arabic letters progress retrieval."""

    data = AllArabicLettersProgressDataSerializer(help_text="All Arabic letters progress data")


class ArabicLetterProgressStatsSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful Arabic letter progress statistics retrieval."""

    data = ArabicLetterProgressStatsDataSerializer(help_text="Arabic letter progress statistics data")


class UpdateArabicLetterProgressSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful Arabic letter progress update."""

    data = ArabicLetterProgressDataSerializer(help_text="Updated Arabic letter progress data")


# Response Schema Collections
class ArabicLetterProgressResponseSchemas:
    """Response schemas for Arabic letter progress endpoints."""

    success_200 = ArabicLetterProgressSuccessResponseSerializer
    list_success_200 = ArabicLetterProgressListSuccessResponseSerializer
    all_success_200 = AllArabicLettersProgressSuccessResponseSerializer
    stats_success_200 = ArabicLetterProgressStatsSuccessResponseSerializer
    update_success_200 = UpdateArabicLetterProgressSuccessResponseSerializer
    error_400 = ProgressValidationErrorResponseSerializer
    error_401 = ProgressAuthenticationErrorResponseSerializer
    error_403 = ProgressForbiddenErrorResponseSerializer
    error_404 = ProgressNotFoundErrorResponseSerializer
    error_500 = ProgressInternalServerErrorResponseSerializer

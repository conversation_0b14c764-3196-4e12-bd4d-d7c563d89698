* {
  padding: 0;
  margin: 0;
  /* box-sizing: border-box; */
}
.memory-game {
  margin: auto;
  display: flex;
  flex-wrap: wrap;
  perspective: 1000px;
  justify-content: center;
}

.memory-card {
  width: 250px;
  height: 250px;
  margin: 5px;
  position: relative;
  transform: scale(1);
  transform-style: preserve-3d;
  transition: transform 0.5s;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  border-radius: 20px;
}

.memory-card:active {
  transform: scale(0.97);
  transition: transform 0.2s;
}

.memory-card.flip {
  transform: rotateY(180deg);
}

.front-face,
.back-face {
  width: 100%;
  height: 100%;
  position: absolute;
  border-radius: 20px;
  background: #77987E;
  backface-visibility: hidden;
  
}

.front-face {
  transform: rotateY(180deg);
}

.front-face.front-text {
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
  align-items: center;
  flex-direction: column;
  background-color: white;
}

.ayah-text {
  font-size: 20px;
  text-align: center;
  line-height: 1.2;
  overflow-wrap: break-word;
  white-space: normal;
  display: inline-block;
  max-width: 100%;
  font-weight: 500;
  color: black;
}

.english-text {
  font-size: 16px;
  text-align: center;
  line-height: 1.2;
  overflow-wrap: break-word;
  white-space: normal;
  display: inline-block;
  max-width: 100%;
  font-weight: 500;
  color: #828282;
}
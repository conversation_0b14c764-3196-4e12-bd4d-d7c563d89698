from django.contrib import admin
from .models import ArabicLetter, Surah, SurahTranslation, Ayah, AyahTranslation


@admin.register(ArabicLetter)
class ArabicLetterAdmin(admin.ModelAdmin):
    list_display = ["title", "transcription", "audio_url"]
    list_filter = ["audio_url"]
    search_fields = ["title", "transcription"]
    ordering = ["title"]

    def get_queryset(self, request):
        """Optimize queryset for admin interface."""
        return super().get_queryset(request)


@admin.register(Surah)
class SurahAdmin(admin.ModelAdmin):
    list_display = ["surah_number", "ayah_count", "xp_per_ayah", "created_at"]
    ordering = ["surah_number"]


@admin.register(SurahTranslation)
class SurahTranslationAdmin(admin.ModelAdmin):
    list_display = ["surah", "locale", "title"]
    list_filter = ["locale"]
    ordering = ["surah__surah_number", "locale"]


@admin.register(Ayah)
class AyahAdmin(admin.ModelAdmin):
    list_display = ["surah", "ayah_number", "image"]
    list_filter = ["surah"]
    ordering = ["surah__surah_number", "ayah_number"]


@admin.register(AyahTranslation)
class AyahTranslationAdmin(admin.ModelAdmin):
    list_display = ["ayah", "locale", "text"]
    list_filter = ["locale"]
    ordering = ["ayah__surah__surah_number", "ayah__ayah_number", "locale"]

button {
  all: unset;
}

button:focus {
  outline: revert;
}

a {
  text-decoration: none;
  color: inherit;
}

.grid-container-levels {
  display: grid;
  grid-template-columns: 0.7fr 3fr 1.2fr;
}

.middle-levels {
  grid-row: span 1;
}

.right-menus {
  grid-row: span 1;
}

.left-menu {
  grid-row: span 1;
}

.righ-menu-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.current-level-button {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  background-color: #97cb9c;
  width: 290px;
  border-radius: 40px;
  color: white;
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 20px;
  filter: drop-shadow(5px 12px 5px #94949450);
}

.current-level-button:hover {
  cursor: pointer;
  transform: scale(1.05);
  transition-duration: 0.2s;
}

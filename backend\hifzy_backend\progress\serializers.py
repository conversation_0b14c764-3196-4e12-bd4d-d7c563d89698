from rest_framework import serializers
from accounts.models import HifzyUser
from .models import SurahGameCompletionMM


class UserProgressSerializer(serializers.ModelSerializer):
    """Updated serializer for user progress using new HifzyUser fields."""

    class Meta:
        model = HifzyUser
        fields = ["total_xp", "current_level", "day_streak"]
        read_only_fields = ["total_xp", "current_level", "day_streak"]


class AyahCompletionRequestSerializer(serializers.Serializer):
    surah_id = serializers.IntegerField(required=True, help_text="ID of the Surah")
    ayah_id = serializers.IntegerField(required=True, help_text="ID of the Ayah within the Surah")


class GameCompletionRequestSerializer(serializers.Serializer):
    surah_id = serializers.IntegerField(required=True, help_text="ID of the Surah")
    game_type = serializers.ChoiceField(
        required=True, choices=SurahGameCompletionMM.GAME_TYPES, help_text="Type of game completed"
    )
    ayah_count = serializers.IntegerField(required=True, help_text="Number of ayahs played in the game")


class SurahProgressRequestSerializer(serializers.Serializer):
    surah_id = serializers.IntegerField(required=True, help_text="ID of the Surah")


class SkipSurahRequestSerializer(serializers.Serializer):
    surah_id = serializers.IntegerField(required=True, help_text="ID of the Surah to skip")

    def validate_surah_id(self, value):
        """Validate that surah_id is within valid range."""
        if value < 1 or value > 114:
            raise serializers.ValidationError("Surah ID must be between 1 and 114")
        return value

import "src/pages/Landing/LandingPage.css";

import landingPics from "src/assets/landing_pics.png";
import askImam from "src/assets/ask_imam.png";
import arrow from "src/assets/arrow.svg";
import findFriends from "src/assets/find_friends.png";
import blackArrow from "src/assets/black_arrow.svg";
import LandingNavbarLi from "src/components/LandingNavbarLi";
import LandingInfoCard from "src/components/LandingInfoCard";
import LandingStatBlock from "src/components/LandingStatBlock";
import Logo from "src/components/Logo";
import { Link } from "react-router-dom";
import GoogleLoginComponent from "src/components/GoogleLogin";
import useAuthStore from "src/hooks/authStore.js";
import ProfileNav from "src/components/ProfileNav";
import { useTranslation } from "react-i18next";

function LandingPage() {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuthStore();
  return (
    <>
      <header>
        <nav className="container container_top">
          <div className="left_navbar">
            <Logo />
            <ul className="inner_navbar">
              <LandingNavbarLi title={t("landing_home_btn")} />
              <LandingNavbarLi title={t("landing_methods_btn")} />
              <LandingNavbarLi title={t("landing_about_us_btn")} />
              <LandingNavbarLi title={t("landing_contacts_btn")} />
            </ul>
          </div>
          {/* <Link to="/register"> */}
          {/* <button className="login_button">Log in</button> */}
          {/* <GoogleLogin
            onSuccess={(credentialResponse) => {
              console.log(credentialResponse);
            }}
            onError={() => {
              console.log("Login Failed");
            }}
          /> */}
          {/* </Link> */}
          {isAuthenticated ? (
            <ProfileNav marginNeeded={false} />
          ) : (
            <GoogleLoginComponent />
          )}
        </nav>
      </header>
      <main>
        <section className="container main_info">
          <div className="left_info_text">
            <div className="main_info_title">
              <a
                dangerouslySetInnerHTML={{ __html: t("landing_main_info1") }}
              ></a>
              <a
                dangerouslySetInnerHTML={{ __html: t("landing_main_info2") }}
              ></a>
            </div>
            <p>{t("landing_main_paragraph")}</p>
            <Link to="/levels">
              <button>{t("get_started_btn")}</button>
            </Link>
          </div>
          <div className="right_info_pics">
            <img src={landingPics} alt="" />
          </div>
        </section>
        <section className="container more_info">
          <div className="left_cards">
            <LandingInfoCard
              title={t("ask_imam_title")}
              descrtiption={t("ask_imam_description")}
              backgroundColor="#a3b18a"
              image={askImam}
              arrow={arrow}
              textColor="white"
            />
            <LandingInfoCard
              title={t("find_friends_title")}
              descrtiption={t("find_friends_description")}
              backgroundColor="#faedcd"
              image={findFriends}
              arrow={blackArrow}
              textColor="black"
            />
          </div>
          <div className="right_more_info">
            <div className="statistics">
              <LandingStatBlock
                title="87,5%"
                description={t("demo_landing_statistic")}
              />
              <LandingStatBlock
                title="1,5m"
                description={t("demo_landing_statistic")}
              />
              <LandingStatBlock
                title="100k"
                description={t("demo_landing_statistic")}
              />
            </div>
            <div className="quote_block">
              <div className="quote">
                <a>{t("landing_quote_text")}</a>
                <a>{t("landing_quote_info")}</a>
              </div>
            </div>
          </div>
        </section>
      </main>
    </>
  );
}

export default LandingPage;

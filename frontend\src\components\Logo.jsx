import React from "react";
import logo from "src/assets/logo.png";
import { Link } from "react-router-dom";
function Logo() {
  return (
    <Link to="/">
      <img
        src={logo}
        alt=""
        className="navbar_logo"
        style={{
          width: "90px",
          height: "auto",
          float: "left",
          marginTop: "auto",
        }}
        onMouseEnter={() => {
          document.querySelector(".navbar_logo").style.transform =
            "scale(1.05)";
          document.querySelector(".navbar_logo").style.transitionDuration =
            "0.2s";
          document.querySelector(".navbar_logo").style.cursor = "pointer";
        }}
        onMouseLeave={() => {
          document.querySelector(".navbar_logo").style.transform = "scale(1)";
        }}
      />
    </Link>
  );
}

export default Logo;

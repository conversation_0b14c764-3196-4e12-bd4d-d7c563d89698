import "src/pages/Levels/LevelsPage.css";
import questBookSvg from "src/assets/quest_book.svg";
import questClockSvg from "src/assets/quest_clock.svg";
import LeftMenuPanel from "src/components/LeftMenuPanel/LeftMenuPanel.jsx";
import LevelsNet from "src/components/LevelsNet/LevelsNet";
import ProfileNav from "src/components/ProfileNav.jsx";
import LeaderBoardCard from "src/components/LeaderBoardCard/LeaderBoardCard.jsx";
import DailyQuests from "src/components/DailyQuests/DailyQuests.jsx";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

function LevelsPage() {
  const { t } = useTranslation();
  return (
    <>
      <main className="grid-container-levels">
        <section className="left-menu">
          <LeftMenuPanel activeMenu="study" />
        </section>
        <section className="middle-levels">
          <LevelsNet />
        </section>
        <section className="right-menus">
          <div className="righ-menu-container">
            <ProfileNav marginNeeded={true} />
            <LeaderBoardCard />
            <DailyQuests />
            <Link to="/games/110/1">
              <button className="current-level-button">
                {t("current_level_btn")}
              </button>
            </Link>
          </div>
        </section>
      </main>
    </>
  );
}

export default LevelsPage;

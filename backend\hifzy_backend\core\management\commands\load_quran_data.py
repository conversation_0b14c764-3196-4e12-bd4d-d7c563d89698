"""
Django management command to load Quran data from JSON file into the database.

This command populates the following models:
- Surah: Basic surah information
- Ayah: Individual verse data with optional image URLs
- SurahTranslation: Surah name translations
- AyahTranslation: Ayah text translations
- AudioRecording: Audio recordings for ayahs (optional)

Usage:
    python manage.py load_quran_data
    python manage.py load_quran_data --file custom_quran.json
    python manage.py load_quran_data --xp-per-ayah 50 --force
    python manage.py load_quran_data --images-folder /path/to/images
    python manage.py load_quran_data --load-audio --images-folder ./ayah_images --force

Image Loading:
    The --images-folder option loads ayah images from a directory and associates them
    with ayah records. Supports multiple naming conventions:
    - {surah}_{ayah}.{ext} (e.g., 1_1.jpg, 2_5.png)
    - {surah:03d}_{ayah:03d}.{ext} (e.g., 001_001.jpg, 002_005.png)
    - surah{surah}_ayah{ayah}.{ext} (e.g., surah1_ayah1.jpg)
    - {surah}-{ayah}.{ext} (e.g., 1-1.jpg, 2-5.png)
    - s{surah}a{ayah}.{ext} (e.g., s1a1.jpg, s2a5.png)

    Supported image formats: jpg, jpeg, png, gif, bmp, webp
"""

import json
import os
import glob
from typing import Dict, List, Any, Optional
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings
from quran.models import Surah, Ayah, SurahTranslation, AyahTranslation, AudioRecording


class Command(BaseCommand):
    help = "Load Quran data from JSON file into the database"

    def add_arguments(self, parser):
        parser.add_argument(
            "--file",
            type=str,
            default="quran.json",
            help="Path to the JSON file containing Quran data (default: quran.json)",
        )
        parser.add_argument("--xp-per-ayah", type=int, default=5, help="XP points per ayah (default: 5)")
        parser.add_argument(
            "--force", action="store_true", help="Force reload existing data (will delete and recreate all records)"
        )
        parser.add_argument("--load-audio", action="store_true", help="Load audio data from the quran.json file")
        parser.add_argument(
            "--images-folder",
            type=str,
            help="Path to directory containing Quran ayah images (will load and associate with ayah records)",
        )

    def handle(self, *args, **options):
        file_path = options["file"]
        xp_per_ayah = options["xp_per_ayah"]
        force_reload = options["force"]
        load_audio = options.get("load_audio", False)
        images_folder = options.get("images_folder")

        # Resolve file path
        if not os.path.isabs(file_path):
            # If relative path, look in Django project root
            file_path = os.path.join(settings.BASE_DIR, file_path)

        if not os.path.exists(file_path):
            raise CommandError(f"File not found: {file_path}")

        # Validate images folder if provided
        if images_folder:
            if not os.path.isabs(images_folder):
                # If relative path, resolve relative to Django project root
                images_folder = os.path.join(settings.BASE_DIR, images_folder)

            if not os.path.exists(images_folder):
                raise CommandError(f"Images folder not found: {images_folder}")

            if not os.path.isdir(images_folder):
                raise CommandError(f"Images path is not a directory: {images_folder}")

        self.stdout.write(f"Loading Quran data from: {file_path}")
        self.stdout.write(f"XP per ayah: {xp_per_ayah}")
        if images_folder:
            self.stdout.write(f"Images folder: {images_folder}")

        try:
            # Load and validate JSON data
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            if not self._validate_json_structure(data):
                raise CommandError("Invalid JSON structure")

            # Check for existing data
            existing_surahs = Surah.objects.count()
            existing_ayahs = Ayah.objects.count()

            if existing_surahs > 0 or existing_ayahs > 0:
                if not force_reload:
                    raise CommandError(
                        f"Database already contains {existing_surahs} surahs and {existing_ayahs} ayahs. "
                        "Use --force to reload data."
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f"Deleting existing data: {existing_surahs} surahs, {existing_ayahs} ayahs")
                    )

            # Load data with transaction
            with transaction.atomic():
                if force_reload:
                    self._clear_existing_data()

                stats = self._load_quran_data(data["data"], xp_per_ayah, load_audio, images_folder)

            # Display results
            result_message = "Successfully loaded Quran data:\n"
            result_message += f"  - Surahs: {stats['surahs_created']}\n"
            result_message += f"  - Ayahs: {stats['ayahs_created']}\n"
            result_message += f"  - Surah translations: {stats['surah_translations_created']}\n"
            result_message += f"  - Ayah translations: {stats['ayah_translations_created']}\n"

            if load_audio:
                result_message += f"  - Audio recordings: {stats.get('audio_recordings_created', 0)}\n"

            if images_folder:
                result_message += f"  - Images loaded: {stats.get('images_loaded', 0)}\n"
                result_message += f"  - Images missing: {stats.get('images_missing', 0)}"

            self.stdout.write(self.style.SUCCESS(result_message))

        except json.JSONDecodeError as e:
            raise CommandError(f"Invalid JSON file: {e}")
        except Exception as e:
            raise CommandError(f"Error loading data: {e}")

    def _validate_json_structure(self, data: Dict[str, Any]) -> bool:
        """Validate the JSON structure contains required fields."""
        if "data" not in data:
            self.stdout.write(self.style.ERROR('Missing "data" key in JSON'))
            return False

        if not isinstance(data["data"], list):
            self.stdout.write(self.style.ERROR('"data" must be a list'))
            return False

        if len(data["data"]) == 0:
            self.stdout.write(self.style.ERROR("No surahs found in data"))
            return False

        # Validate first surah structure
        first_surah = data["data"][0]
        required_fields = ["number", "numberOfVerses", "name", "revelation", "verses"]
        for field in required_fields:
            if field not in first_surah:
                self.stdout.write(self.style.ERROR(f'Missing required field "{field}" in surah data'))
                return False

        return True

    def _clear_existing_data(self):
        """Clear existing Quran data from the database."""
        from django.db import connection

        # Delete in correct order to avoid foreign key constraints
        AudioRecording.objects.all().delete()
        AyahTranslation.objects.all().delete()
        SurahTranslation.objects.all().delete()
        Ayah.objects.all().delete()
        Surah.objects.all().delete()

        # Reset auto-increment sequences to ensure clean IDs
        with connection.cursor() as cursor:
            # Get the table names
            surah_table = Surah._meta.db_table
            ayah_table = Ayah._meta.db_table
            surah_translation_table = SurahTranslation._meta.db_table
            ayah_translation_table = AyahTranslation._meta.db_table
            audio_recording_table = AudioRecording._meta.db_table

            # Reset sequences based on database backend
            if connection.vendor == "postgresql":
                cursor.execute(f"ALTER SEQUENCE {surah_table}_id_seq RESTART WITH 1;")
                cursor.execute(f"ALTER SEQUENCE {ayah_table}_id_seq RESTART WITH 1;")
                cursor.execute(f"ALTER SEQUENCE {surah_translation_table}_id_seq RESTART WITH 1;")
                cursor.execute(f"ALTER SEQUENCE {ayah_translation_table}_id_seq RESTART WITH 1;")
                cursor.execute(f"ALTER SEQUENCE {audio_recording_table}_id_seq RESTART WITH 1;")
            elif connection.vendor == "sqlite":
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{surah_table}';")
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{ayah_table}';")
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{surah_translation_table}';")
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{ayah_translation_table}';")
                cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{audio_recording_table}';")
            elif connection.vendor == "mysql":
                cursor.execute(f"ALTER TABLE {surah_table} AUTO_INCREMENT = 1;")
                cursor.execute(f"ALTER TABLE {ayah_table} AUTO_INCREMENT = 1;")
                cursor.execute(f"ALTER TABLE {surah_translation_table} AUTO_INCREMENT = 1;")
                cursor.execute(f"ALTER TABLE {ayah_translation_table} AUTO_INCREMENT = 1;")
                cursor.execute(f"ALTER TABLE {audio_recording_table} AUTO_INCREMENT = 1;")

        self.stdout.write("Cleared existing data and reset auto-increment sequences")

    def _load_quran_data(
        self,
        surahs_data: List[Dict[str, Any]],
        xp_per_ayah: int,
        load_audio: bool = False,
        images_folder: Optional[str] = None,
    ) -> Dict[str, int]:
        """Load Quran data into the database."""
        stats = {
            "surahs_created": 0,
            "ayahs_created": 0,
            "surah_translations_created": 0,
            "ayah_translations_created": 0,
            "audio_recordings_created": 0,
            "images_loaded": 0,
            "images_missing": 0,
        }

        surahs_to_create = []
        surah_translations_to_create = []
        ayahs_to_create = []
        ayah_translations_to_create = []
        audio_recordings_to_create = []

        total_surahs = len(surahs_data)

        for i, surah_data in enumerate(surahs_data, 1):
            # Progress indicator
            if i % 10 == 0 or i == total_surahs:
                self.stdout.write(f"Processing surah {i}/{total_surahs}...")

            # Process revelation data
            revelation_data = surah_data.get("revelation", {})
            is_meccan = revelation_data.get("en") == "Meccan"
            is_medinian = revelation_data.get("en") == "Medinan"

            # Process preBismillah data
            pre_bismillah_data = surah_data.get("preBismillah")
            is_pre_bismillah = pre_bismillah_data is not None and isinstance(pre_bismillah_data, dict)

            # Create Surah object
            surah = Surah(
                surah_number=surah_data["number"],
                ayah_count=surah_data["numberOfVerses"],
                xp_per_ayah=xp_per_ayah,
                is_pre_bismillah=is_pre_bismillah,
                is_meccan=is_meccan,
                is_medinian=is_medinian,
            )
            surahs_to_create.append(surah)

            # Prepare surah translations
            name_data = surah_data.get("name", {})
            if "transliteration" in name_data and "en" in name_data["transliteration"]:
                surah_translations_to_create.append(
                    {
                        "surah_number": surah_data["number"],
                        "locale": "en_transliteration",
                        "title": name_data["transliteration"]["en"],
                    }
                )

            if "translation" in name_data and "en" in name_data["translation"]:
                surah_translations_to_create.append(
                    {"surah_number": surah_data["number"], "locale": "en", "title": name_data["translation"]["en"]}
                )

            if "short" in name_data:
                surah_translations_to_create.append(
                    {"surah_number": surah_data["number"], "locale": "ar", "title": name_data["short"]}
                )

            # Process verses
            verses = surah_data.get("verses", [])
            for verse_data in verses:
                ayah_number = verse_data["number"]["inSurah"]

                # Store ayah data for later creation (after surahs are created)
                ayahs_to_create.append(
                    {
                        "ayah_number": ayah_number,
                        "surah_number": surah_data["number"],
                        "image": None,  # Set to None as per requirements
                    }
                )

                # Prepare ayah translations
                text_data = verse_data.get("text", {})
                translation_data = verse_data.get("translation", {})

                if "arab" in text_data:
                    ayah_translations_to_create.append(
                        {
                            "surah_number": surah_data["number"],
                            "ayah_number": ayah_number,
                            "locale": "ar",
                            "text": text_data["arab"],
                        }
                    )

                if "transliteration" in text_data and "en" in text_data["transliteration"]:
                    ayah_translations_to_create.append(
                        {
                            "surah_number": surah_data["number"],
                            "ayah_number": ayah_number,
                            "locale": "en_transliteration",
                            "text": text_data["transliteration"]["en"],
                        }
                    )

                if "en" in translation_data:
                    ayah_translations_to_create.append(
                        {
                            "surah_number": surah_data["number"],
                            "ayah_number": ayah_number,
                            "locale": "en",
                            "text": translation_data["en"],
                        }
                    )

                # Process audio data if requested
                if load_audio:
                    audio_data = verse_data.get("audio", {})

                    # Process primary audio
                    if "primary" in audio_data and audio_data["primary"]:
                        audio_recordings_to_create.append(
                            {
                                "surah_number": surah_data["number"],
                                "ayah_number": ayah_number,
                                "audio_url": audio_data["primary"],
                                "reader_name": "Al-Afasy",  # Default reader name for primary audio
                                "reader_slug": "alafasy",
                                "audio_format": "mp3",
                            }
                        )

                    # Process secondary audio URLs
                    if "secondary" in audio_data and isinstance(audio_data["secondary"], list):
                        for secondary_url in audio_data["secondary"]:
                            if secondary_url:
                                # Extract quality from URL (128 or 64)
                                quality = (
                                    "128"
                                    if "/128/" in secondary_url
                                    else "64"
                                    if "/64/" in secondary_url
                                    else "unknown"
                                )
                                reader_slug = f"alafasy_{quality}"
                                reader_name = f"Al-Afasy ({quality}kbps)"

                                audio_recordings_to_create.append(
                                    {
                                        "surah_number": surah_data["number"],
                                        "ayah_number": ayah_number,
                                        "audio_url": secondary_url,
                                        "reader_name": reader_name,
                                        "reader_slug": reader_slug,
                                        "audio_format": "mp3",
                                    }
                                )

        # Bulk create surahs
        self.stdout.write("Creating surahs...")
        Surah.objects.bulk_create(surahs_to_create, batch_size=100)
        stats["surahs_created"] = len(surahs_to_create)

        # Validate surah creation
        created_surahs = Surah.objects.all().order_by("surah_number")
        self.stdout.write(f"Validation: Created {created_surahs.count()} surahs")
        if created_surahs.count() > 0:
            first_surah = created_surahs.first()
            last_surah = created_surahs.last()
            self.stdout.write(f"  - First surah: ID={first_surah.id}, surah_number={first_surah.surah_number}")
            self.stdout.write(f"  - Last surah: ID={last_surah.id}, surah_number={last_surah.surah_number}")

            # Check for any gaps or duplicates
            expected_numbers = set(range(1, 115))  # 1-114
            actual_numbers = set(created_surahs.values_list("surah_number", flat=True))
            missing = expected_numbers - actual_numbers
            extra = actual_numbers - expected_numbers

            if missing:
                self.stdout.write(self.style.WARNING(f"  - Missing surah numbers: {sorted(missing)}"))
            if extra:
                self.stdout.write(self.style.WARNING(f"  - Extra surah numbers: {sorted(extra)}"))
            if not missing and not extra:
                self.stdout.write(self.style.SUCCESS("  - All surah numbers 1-114 created correctly"))

        # Create surah translations
        self.stdout.write("Creating surah translations...")
        surah_objects = {s.surah_number: s for s in Surah.objects.all()}

        # Validate surah lookup dictionary
        self.stdout.write(f"Validation: Surah lookup contains {len(surah_objects)} surahs")
        if len(surah_objects) > 0:
            sample_keys = list(surah_objects.keys())[:5]  # First 5 keys
            self.stdout.write(f"  - Sample surah_numbers in lookup: {sample_keys}")
            sample_surah = surah_objects[sample_keys[0]]
            self.stdout.write(f"  - Sample surah: ID={sample_surah.id}, surah_number={sample_surah.surah_number}")

        surah_translation_objects = []
        for st_data in surah_translations_to_create:
            if st_data["surah_number"] not in surah_objects:
                self.stdout.write(
                    self.style.ERROR(f"Surah {st_data['surah_number']} not found in lookup for translation")
                )
                continue
            surah = surah_objects[st_data["surah_number"]]
            surah_translation_objects.append(
                SurahTranslation(surah=surah, locale=st_data["locale"], title=st_data["title"])
            )
        SurahTranslation.objects.bulk_create(surah_translation_objects, batch_size=500)
        stats["surah_translations_created"] = len(surah_translation_objects)

        # Bulk create ayahs with proper ForeignKey references
        self.stdout.write("Creating ayahs...")
        # Refresh surah objects lookup to ensure we have the latest data
        surah_objects = {s.surah_number: s for s in Surah.objects.all()}

        # Validate surah lookup for ayahs
        self.stdout.write(f"Validation: Surah lookup for ayahs contains {len(surah_objects)} surahs")

        # Ensure we have all expected surahs before creating ayahs
        expected_surah_numbers = set(range(1, 115))  # 1-114
        available_surah_numbers = set(surah_objects.keys())
        missing_from_lookup = expected_surah_numbers - available_surah_numbers

        if missing_from_lookup:
            raise CommandError(
                f"Critical error: Missing surahs in lookup before ayah creation: {sorted(missing_from_lookup)}"
            )

        missing_surahs = set()
        ayah_objects = []

        for ayah_data in ayahs_to_create:
            surah_number = ayah_data["surah_number"]
            if surah_number not in surah_objects:
                missing_surahs.add(surah_number)
                self.stdout.write(
                    self.style.WARNING(f"Surah {surah_number} not found for ayah {ayah_data['ayah_number']}")
                )
                continue

            surah = surah_objects[surah_number]
            ayah_obj = Ayah(ayah_number=ayah_data["ayah_number"], surah=surah, image=ayah_data["image"])
            ayah_objects.append(ayah_obj)

        if missing_surahs:
            self.stdout.write(self.style.ERROR(f"Missing surahs for ayah creation: {sorted(missing_surahs)}"))

        # Sample validation before bulk create
        if len(ayah_objects) > 0:
            sample_ayah = ayah_objects[0]
            self.stdout.write(
                f"  - Sample ayah: surah_id={sample_ayah.surah.id}, surah_number={sample_ayah.surah.surah_number}, ayah_number={sample_ayah.ayah_number}"
            )

        Ayah.objects.bulk_create(ayah_objects, batch_size=1000)
        stats["ayahs_created"] = len(ayah_objects)

        # Post-creation validation
        created_ayahs = Ayah.objects.all()
        self.stdout.write(f"Validation: Created {created_ayahs.count()} ayahs")
        if created_ayahs.count() > 0:
            # Check a few sample ayahs
            sample_ayahs = created_ayahs[:3]
            for ayah in sample_ayahs:
                self.stdout.write(
                    f"  - Ayah {ayah.ayah_number} → Surah ID={ayah.surah.id}, surah_number={ayah.surah.surah_number}"
                )

        # Create ayah translations
        self.stdout.write("Creating ayah translations...")
        # Get all ayahs for mapping
        ayah_lookup = {}
        for ayah in Ayah.objects.all():
            key = (ayah.surah.surah_number, ayah.ayah_number)
            ayah_lookup[key] = ayah

        # Validate ayah lookup
        self.stdout.write(f"Validation: Ayah lookup contains {len(ayah_lookup)} ayahs")
        if len(ayah_lookup) > 0:
            sample_keys = list(ayah_lookup.keys())[:3]
            for key in sample_keys:
                ayah = ayah_lookup[key]
                self.stdout.write(f"  - Ayah key {key} → Ayah ID={ayah.id}, Surah ID={ayah.surah.id}")

        ayah_translation_objects = []
        missing_ayahs = set()
        for at_data in ayah_translations_to_create:
            key = (at_data["surah_number"], at_data["ayah_number"])
            if key in ayah_lookup:
                ayah = ayah_lookup[key]
                ayah_translation_objects.append(
                    AyahTranslation(ayah=ayah, locale=at_data["locale"], text=at_data["text"])
                )
            else:
                missing_ayahs.add(key)

        if missing_ayahs:
            sample_missing = list(missing_ayahs)[:5]  # Show first 5 missing
            self.stdout.write(self.style.WARNING(f"Missing ayahs for translation (showing first 5): {sample_missing}"))

        AyahTranslation.objects.bulk_create(ayah_translation_objects, batch_size=1000)
        stats["ayah_translations_created"] = len(ayah_translation_objects)

        # Create audio recordings if requested
        if load_audio and audio_recordings_to_create:
            self.stdout.write("Creating audio recordings...")
            audio_recording_objects = []
            for ar_data in audio_recordings_to_create:
                key = (ar_data["surah_number"], ar_data["ayah_number"])
                if key in ayah_lookup:
                    ayah = ayah_lookup[key]
                    audio_recording_objects.append(
                        AudioRecording(
                            ayah=ayah,
                            audio_url=ar_data["audio_url"],
                            reader_name=ar_data["reader_name"],
                            reader_slug=ar_data["reader_slug"],
                            audio_format=ar_data["audio_format"],
                        )
                    )

            AudioRecording.objects.bulk_create(audio_recording_objects, batch_size=1000, ignore_conflicts=True)
            stats["audio_recordings_created"] = len(audio_recording_objects)

        # Load images if folder provided
        if images_folder:
            image_stats = self._load_images(images_folder, ayah_lookup)
            stats["images_loaded"] = image_stats["images_loaded"]
            stats["images_missing"] = image_stats["images_missing"]

        return stats

    def _get_image_mapping(self, images_folder: str) -> Dict[tuple, str]:
        """
        Create a mapping of (surah_number, ayah_number) to image file paths.

        Supports multiple naming conventions:
        - {surah}_{ayah}.{ext} (e.g., 1_1.jpg, 2_5.png)
        - {surah:03d}_{ayah:03d}.{ext} (e.g., 001_001.jpg, 002_005.png)
        - surah{surah}_ayah{ayah}.{ext} (e.g., surah1_ayah1.jpg)
        - {surah}-{ayah}.{ext} (e.g., 1-1.jpg, 2-5.png)
        """
        image_mapping = {}
        valid_extensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}

        # Get all image files in the directory
        image_files = []
        for ext in valid_extensions:
            pattern = os.path.join(images_folder, f"*{ext}")
            image_files.extend(glob.glob(pattern))
            # Also check uppercase extensions
            pattern = os.path.join(images_folder, f"*{ext.upper()}")
            image_files.extend(glob.glob(pattern))

        self.stdout.write(f"Found {len(image_files)} image files in {images_folder}")

        for image_path in image_files:
            filename = os.path.basename(image_path)
            name_without_ext = os.path.splitext(filename)[0]

            # Try different naming patterns
            surah_ayah_pair = self._parse_image_filename(name_without_ext)
            if surah_ayah_pair:
                surah_number, ayah_number = surah_ayah_pair
                image_mapping[(surah_number, ayah_number)] = image_path

        self.stdout.write(f"Successfully mapped {len(image_mapping)} images to ayah records")
        return image_mapping

    def _parse_image_filename(self, filename: str) -> Optional[tuple]:
        """
        Parse image filename to extract surah and ayah numbers.
        Returns (surah_number, ayah_number) tuple or None if parsing fails.
        """
        import re

        # Pattern 1: {surah}_{ayah} (e.g., 1_1, 2_5, 001_001)
        pattern1 = re.compile(r"^(\d+)_(\d+)$")
        match = pattern1.match(filename)
        if match:
            return (int(match.group(1)), int(match.group(2)))

        # Pattern 2: surah{surah}_ayah{ayah} (e.g., surah1_ayah1)
        pattern2 = re.compile(r"^surah(\d+)_ayah(\d+)$", re.IGNORECASE)
        match = pattern2.match(filename)
        if match:
            return (int(match.group(1)), int(match.group(2)))

        # Pattern 3: {surah}-{ayah} (e.g., 1-1, 2-5)
        pattern3 = re.compile(r"^(\d+)-(\d+)$")
        match = pattern3.match(filename)
        if match:
            return (int(match.group(1)), int(match.group(2)))

        # Pattern 4: s{surah}a{ayah} (e.g., s1a1, s2a5)
        pattern4 = re.compile(r"^s(\d+)a(\d+)$", re.IGNORECASE)
        match = pattern4.match(filename)
        if match:
            return (int(match.group(1)), int(match.group(2)))
        # Pattern : {surah}.{ayah} (e.g., 1.1, 2.5)
        pattern5 = re.compile(r"^(\d+)\.(\d+)$")
        match = pattern5.match(filename)
        if match:
            return (int(match.group(1)), int(match.group(2)))
        return None

    def _validate_image_file(self, image_path: str) -> bool:
        """
        Validate that the file is a valid image.
        Basic validation by checking file extension and existence.
        """
        if not os.path.exists(image_path):
            return False

        if not os.path.isfile(image_path):
            return False

        # Check file size (avoid empty files)
        if os.path.getsize(image_path) == 0:
            return False

        # Check extension
        valid_extensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}
        _, ext = os.path.splitext(image_path.lower())
        return ext in valid_extensions

    def _load_images(self, images_folder: str, ayah_lookup: Dict[tuple, Ayah]) -> Dict[str, int]:
        """
        Load images from the specified folder and update ayah records.

        Args:
            images_folder: Path to the directory containing images
            ayah_lookup: Dictionary mapping (surah_number, ayah_number) to Ayah objects

        Returns:
            Dictionary with statistics about image loading
        """
        self.stdout.write("Loading ayah images...")

        # Get image mapping
        image_mapping = self._get_image_mapping(images_folder)

        images_loaded = 0
        images_missing = 0
        ayahs_to_update = []

        # Process each ayah and try to find corresponding image
        total_ayahs = len(ayah_lookup)
        processed = 0

        for (surah_number, ayah_number), ayah in ayah_lookup.items():
            processed += 1

            # Progress reporting
            if processed % 500 == 0 or processed == total_ayahs:
                self.stdout.write(f"Processing images: {processed}/{total_ayahs} ayahs...")

            # Check if image exists for this ayah
            if (surah_number, ayah_number) in image_mapping:
                image_path = image_mapping[(surah_number, ayah_number)]

                # Validate the image file
                if self._validate_image_file(image_path):
                    # Convert absolute path to relative path or URL as needed
                    # For now, we'll store the relative path from the images folder
                    relative_path = os.path.relpath(image_path, images_folder)
                    image_url = f"ayah_images/{relative_path}"  # Adjust this based on your URL structure

                    ayah.image = image_url
                    ayahs_to_update.append(ayah)
                    images_loaded += 1
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f"Invalid image file for Surah {surah_number}, Ayah {ayah_number}: {image_path}"
                        )
                    )
                    images_missing += 1
            else:
                images_missing += 1

        # Bulk update ayahs with image URLs
        if ayahs_to_update:
            self.stdout.write(f"Updating {len(ayahs_to_update)} ayah records with image URLs...")
            Ayah.objects.bulk_update(ayahs_to_update, ["image"], batch_size=1000)

        # Log some examples of loaded images
        if images_loaded > 0:
            sample_size = min(5, len(ayahs_to_update))
            self.stdout.write(f"Sample of loaded images (showing first {sample_size}):")
            for ayah in ayahs_to_update[:sample_size]:
                self.stdout.write(f"  - Surah {ayah.surah.surah_number}, Ayah {ayah.ayah_number}: {ayah.image}")

        return {
            "images_loaded": images_loaded,
            "images_missing": images_missing,
        }

.result-container {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.3s ease, transform 0.3s ease;
  width: 100%;
  border-radius: 40px;
  border: thin solid #bfbfbf;
  padding: 15px;
  font-size: 18px;
  text-align: center;
  font-weight: 500;
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: sticky;
  bottom: 0;
}

.result-container.correct,
.result-container.incorrect {
  opacity: 1;
  transform: scale(1);
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Добавляем анимацию для появления result-container */
.result-container.correct,
.result-container.incorrect {
  animation: fadeInScale 0.4s ease forwards;
}

.result-container.correct {
  background-color: #d4edda; /* Светло-зелёный цвет для правильного ответа */
  color: #155724;
}

.result-container.incorrect {
  background-color: #f8d7da; /* Светло-красный цвет для неправильного ответа */
  color: #721c24;
}

/* Стили для текста */
.result-container p {
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0;
}

/* Стили кнопки */
.result-container button {
  padding: 7px 20px;
  width: 200px;
  /* background-color: #ffffff; */
  color: #333;
  font-size: 1rem;
  font-weight: bold;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.result-container button:hover {
  background-color: #dddddd;
}
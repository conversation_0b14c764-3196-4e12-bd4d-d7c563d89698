"""
Clean architecture views
These views follow the best practices outlined in the article:
- Views only handle HTTP request/response concerns
- Business logic is delegated to the service layer
- Data access is handled through repositories
- Transport objects are used for API responses
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework import status
from rest_framework.exceptions import ValidationError, NotFound
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.conf import settings
from social_django.utils import load_strategy, load_backend
from social_core.exceptions import MissingBackend

from . import services
from . import serializers
from .models import HifzyUser
from core.utils import serialize_dataclass, create_success_response, create_error_response, serialize_dataclass_list
from .response_schemas import (
    GoogleLoginResponseSchemas,
    DevLoginResponseSchemas,
    VerifiedUserRegistrationResponseSchemas,
    UserLoginResponseSchemas,
    PasswordChangeResponseSchemas,
    UserProfileCompleteResponseSchemas,
    BookmarkResponseSchemas,
    EmailAvailabilityResponseSchemas,
    EmailVerificationResponseSchemas,
    PasswordResetResponseSchemas,
    AyahInteractionResponseSchemas,
)

# class BookmarkView(APIView):
#     """View for bookmark operations."""

#     permission_classes = [IsAuthenticated]

#     @swagger_auto_schema(
#         operation_description="Add a bookmark for an Ayah",
#         request_body=serializers.BookmarkAyahSerializer,
#         responses={
#             201: openapi.Response("Bookmark added successfully"),
#             400: openapi.Response("Invalid request data"),
#             401: openapi.Response("Authentication credentials not provided"),
#         },
#     )
#     def post(self, request):
#         """Add a bookmark."""
#         serializer = serializers.BookmarkAyahSerializer(data=request.data)
#         if not serializer.is_valid():
#             return Response(
#                 create_error_response("Invalid data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
#             )

#         try:
#             bookmark = services.BookmarkService.add_bookmark(
#                 user_id=request.user.id,
#                 surah_id=serializer.validated_data["surah_id"],
#                 ayah_id=serializer.validated_data["ayah_id"],
#             )

#             return Response(
#                 create_success_response(bookmark, "Bookmark added successfully"), status=status.HTTP_201_CREATED
#             )
#         except ValidationError as e:
#             return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
#         except NotFound as e:
#             return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
#         except Exception as e:
#             return Response(
#                 create_error_response(f"Failed to add bookmark: {str(e)}"), status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

#     @swagger_auto_schema(
#         operation_description="Get all bookmarks for the current user",
#         responses={
#             200: openapi.Response("Bookmarks retrieved successfully"),
#             401: openapi.Response("Authentication credentials not provided"),
#         },
#     )
#     def get(self, request):
#         """Get all bookmarks for the current user."""
#         try:
#             bookmarks = services.BookmarkService.get_user_bookmarks(request.user.id)

#             return Response(
#                 create_success_response(bookmarks, "Bookmarks retrieved successfully"), status=status.HTTP_200_OK
#             )
#         except Exception as e:
#             return Response(
#                 create_error_response(f"Failed to retrieve bookmarks: {str(e)}"),
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             )

#     @swagger_auto_schema(
#         operation_description="Remove a bookmark",
#         manual_parameters=[
#             openapi.Parameter("surah_id", openapi.IN_QUERY, description="Surah ID", type=openapi.TYPE_INTEGER),
#             openapi.Parameter("ayah_id", openapi.IN_QUERY, description="Ayah ID", type=openapi.TYPE_INTEGER),
#         ],
#         responses={
#             200: openapi.Response("Bookmark removed successfully"),
#             400: openapi.Response("Invalid request parameters"),
#             401: openapi.Response("Authentication credentials not provided"),
#             404: openapi.Response("Bookmark not found"),
#         },
#     )
#     def delete(self, request):
#         """Remove a bookmark."""
#         surah_id = request.query_params.get("surah_id")
#         ayah_id = request.query_params.get("ayah_id")

#         if not surah_id or not ayah_id:
#             return Response(
#                 create_error_response("surah_id and ayah_id are required"), status=status.HTTP_400_BAD_REQUEST
#             )

#         try:
#             surah_id = int(surah_id)
#             ayah_id = int(ayah_id)
#         except ValueError:
#             return Response(
#                 create_error_response("surah_id and ayah_id must be integers"), status=status.HTTP_400_BAD_REQUEST
#             )

#         try:
#             removed = services.BookmarkService.remove_bookmark(
#                 user_id=request.user.id, surah_id=surah_id, ayah_id=ayah_id
#             )

#             if not removed:
#                 return Response(create_error_response("Bookmark not found"), status=status.HTTP_404_NOT_FOUND)

#             return Response(create_success_response(None, "Bookmark removed successfully"), status=status.HTTP_200_OK)
#         except Exception as e:
#             return Response(
#                 create_error_response(f"Failed to remove bookmark: {str(e)}"),
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             )


class GoogleLoginView(APIView):
    """View for Google OAuth2 authentication."""

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Authenticate user with Google OAuth2",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["access_token"],
            properties={
                "access_token": openapi.Schema(type=openapi.TYPE_STRING, description="Google OAuth2 access token"),
            },
        ),
        responses={
            200: openapi.Response(
                "Login successful, returns authentication token", GoogleLoginResponseSchemas.success_200
            ),
            400: openapi.Response("Invalid access token", GoogleLoginResponseSchemas.error_400),
            500: openapi.Response("Internal server error", GoogleLoginResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Authenticate user with Google OAuth2."""
        access_token = request.data.get("access_token")

        if not access_token:
            return Response(create_error_response("Access token is required"), status=status.HTTP_400_BAD_REQUEST)

        try:
            strategy = load_strategy(request)
            backend = load_backend(strategy=strategy, name="google-oauth2", redirect_uri=None)
            user = backend.do_auth(access_token)

            if user and user.is_active:
                # Check if user has already set their own name or photo and don't overwrite it
                user_data = backend.user_data(access_token)
                if not user.first_name or not user.last_name:
                    user.first_name = user_data.get("given_name", user.first_name)
                    user.last_name = user_data.get("family_name", user.last_name)
                if not user.profile_photo:
                    profile_photo_url = user_data.get("picture")
                    if profile_photo_url:
                        try:
                            services.UserService.download_and_save_profile_picture(user.id, profile_photo_url)
                        except Exception:
                            # Log error but don't fail authentication
                            pass

                user.save()

                # Create authentication token
                auth_token = services.AuthenticationService.create_auth_token(user.id)

                return Response(
                    create_success_response(
                        {"token": auth_token.token, "user": serialize_dataclass(auth_token.user)}, "Login successful"
                    ),
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(create_error_response("Invalid user"), status=status.HTTP_400_BAD_REQUEST)
        except MissingBackend:
            return Response(
                create_error_response("Authentication backend not found"), status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                create_error_response(f"Authentication failed: {str(e)}"), status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DevLoginView(APIView):
    """Development-only view for generating authentication tokens without OAuth."""

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Development-only endpoint to get authentication token for testing",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["username"],
            properties={
                "username": openapi.Schema(type=openapi.TYPE_STRING, description="Username to authenticate as"),
            },
        ),
        responses={
            200: openapi.Response(
                "Login successful, returns authentication token", DevLoginResponseSchemas.success_200
            ),
            400: openapi.Response("Invalid username", DevLoginResponseSchemas.error_400),
            403: openapi.Response("Endpoint disabled in production", DevLoginResponseSchemas.error_403),
            500: openapi.Response("Internal server error", DevLoginResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Development-only authentication endpoint."""
        # Only allow this endpoint in development mode
        if not settings.DEBUG:
            return Response(
                create_error_response("This endpoint is only available in development mode"),
                status=status.HTTP_403_FORBIDDEN,
            )

        username = request.data.get("username")
        if not username:
            return Response(create_error_response("Username is required"), status=status.HTTP_400_BAD_REQUEST)

        try:
            # Get or create a user with the provided username
            user, created = HifzyUser.objects.get_or_create(
                username=username,
                defaults={"email": f"{username}@example.com", "first_name": "Test", "last_name": "User"},
            )

            # Generate token
            auth_token = services.AuthenticationService.create_auth_token(user.id)

            return Response(
                create_success_response(
                    {
                        "token": auth_token.token,
                        "user": serialize_dataclass(auth_token.user),
                        "note": "This token can be used in the Swagger UI by clicking the 'Authorize' button and entering 'Token YOUR_TOKEN_HERE'",
                    },
                    "Development login successful",
                ),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Failed to create development login: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

# TODO clean up unused code
# class UserRegistrationView(APIView):
#     """View for user registration with email, username, and password."""

#     permission_classes = [AllowAny]

#     @swagger_auto_schema(
#         operation_description="Register a new user with email and password. Username is optional - if not provided, it will be automatically generated from the email address.",
#         request_body=serializers.UserRegistrationSerializer,
#         responses={
#             201: openapi.Response(
#                 "Registration successful, returns authentication token", UserRegistrationResponseSchemas.success_201
#             ),
#             400: openapi.Response("Invalid registration data", UserRegistrationResponseSchemas.error_400),
#             500: openapi.Response("Internal server error", UserRegistrationResponseSchemas.error_500),
#         },
#     )
#     def post(self, request):
#         """Register a new user."""
#         serializer = serializers.UserRegistrationSerializer(data=request.data)
#         if not serializer.is_valid():
#             return Response(
#                 create_error_response("Invalid registration data", serializer.errors),
#                 status=status.HTTP_400_BAD_REQUEST,
#             )

#         try:
#             # Create user using the serializer
#             user = serializer.save()

#             # Create authentication token
#             auth_token = services.AuthenticationService.create_auth_token(user.id)

#             return Response(
#                 create_success_response(
#                     {"token": auth_token.token, "user": serialize_dataclass(auth_token.user)}, "Registration successful"
#                 ),
#                 status=status.HTTP_201_CREATED,
#             )
#         except ValidationError as e:
#             return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
#         except Exception as e:
#             return Response(
#                 create_error_response(f"Registration failed: {str(e)}"),
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             )


class VerifiedUserRegistrationView(APIView):
    """View for user registration with email verification."""

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Register a new user with email verification. Email must be verified first using the email verification endpoints. Use '0000' as verification code for development.",
        request_body=serializers.VerifiedUserRegistrationSerializer,
        responses={
            201: openapi.Response(
                "Registration successful with email verification", VerifiedUserRegistrationResponseSchemas.success_201
            ),
            400: openapi.Response(
                "Invalid registration data or verification code", VerifiedUserRegistrationResponseSchemas.error_400
            ),
            500: openapi.Response("Internal server error", VerifiedUserRegistrationResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Register a new user with email verification."""
        serializer = serializers.VerifiedUserRegistrationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid registration data", serializer.errors),
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Create user using the serializer (verification is done in serializer validation)
            user = serializer.save()

            # Create authentication token
            auth_token = services.AuthenticationService.create_auth_token(user.id)

            return Response(
                create_success_response(
                    {"token": auth_token.token, "user": serialize_dataclass(auth_token.user)},
                    "Registration successful with email verification",
                ),
                status=status.HTTP_201_CREATED,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Verified registration failed: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class UserLoginView(APIView):
    """View for user login with email/username and password."""

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Login user with email/username and password",
        request_body=serializers.UserLoginSerializer,
        responses={
            200: openapi.Response(
                "Login successful, returns authentication token", UserLoginResponseSchemas.success_200
            ),
            400: openapi.Response("Invalid login credentials", UserLoginResponseSchemas.error_400),
            500: openapi.Response("Internal server error", UserLoginResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Login user with email/username and password."""
        serializer = serializers.UserLoginSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid login data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get validated user from serializer
            user = serializer.validated_data["user"]

            # Create authentication token
            auth_token = services.AuthenticationService.create_auth_token(user.id)

            return Response(
                create_success_response(
                    {"token": auth_token.token, "user": serialize_dataclass(auth_token.user)}, "Login successful"
                ),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Login failed: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PasswordChangeView(APIView):
    """View for changing user password."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Change user password",
        request_body=serializers.PasswordChangeSerializer,
        responses={
            200: openapi.Response("Password changed successfully", PasswordChangeResponseSchemas.success_200),
            400: openapi.Response("Invalid password data", PasswordChangeResponseSchemas.error_400),
            401: openapi.Response("Authentication credentials not provided", PasswordChangeResponseSchemas.error_401),
            404: openapi.Response("User not found", PasswordChangeResponseSchemas.error_404),
            500: openapi.Response("Internal server error", PasswordChangeResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Change user password."""
        serializer = serializers.PasswordChangeSerializer(data=request.data, context={"request": request})
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid password data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Change password using the service
            services.AuthenticationService.change_password(
                user_id=request.user.id,
                current_password=serializer.validated_data["current_password"],
                new_password=serializer.validated_data["new_password"],
            )

            return Response(
                create_success_response(None, "Password changed successfully"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Password change failed: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class UserProfileCompleteView(APIView):
    """Consolidated view for complete user profile operations."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get enhanced user profile information including social data, league position, and bookmark summary",
        manual_parameters=[
            openapi.Parameter(
                "social_limit",
                openapi.IN_QUERY,
                description="Limit for followers/following lists (default: 10)",
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
            openapi.Parameter(
                "bookmark_limit",
                openapi.IN_QUERY,
                description="Limit for recent bookmarks list (default: 5)",
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                "Enhanced user profile retrieved successfully", UserProfileCompleteResponseSchemas.get_success_200
            ),
            401: openapi.Response(
                "Authentication credentials not provided", UserProfileCompleteResponseSchemas.error_401
            ),
            404: openapi.Response("User not found", UserProfileCompleteResponseSchemas.error_404),
            500: openapi.Response("Internal server error", UserProfileCompleteResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get current user's enhanced profile with social, league, and bookmark information."""
        try:
            # Get query parameters for limits
            social_limit = int(request.query_params.get("social_limit", 10))
            bookmark_limit = int(request.query_params.get("bookmark_limit", 5))

            # Validate limits
            social_limit = max(1, min(social_limit, 50))  # Between 1 and 50
            bookmark_limit = max(1, min(bookmark_limit, 20))  # Between 1 and 20

            enhanced_profile = services.UserService.get_enhanced_user_profile(
                request.user.id, social_limit, bookmark_limit
            )
            if not enhanced_profile:
                return Response(create_error_response("User not found"), status=status.HTTP_404_NOT_FOUND)

            return Response(
                create_success_response(
                    serialize_dataclass(enhanced_profile), "Enhanced profile retrieved successfully"
                ),
                status=status.HTTP_200_OK,
            )
        except ValueError as e:
            return Response(
                create_error_response(f"Invalid parameter values: {str(e)}"),
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve profile: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class UpdateUserProfileCompleteView(APIView):
    """Consolidated view for complete user profile operations."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Update complete user profile information. All fields are optional and will only be updated if provided. Username field is optional - if provided, it must be 3-30 characters long and contain only letters, numbers, and underscores.",
        request_body=serializers.UserProfileCompleteSerializer,
        responses={
            200: openapi.Response(
                "Profile updated successfully", UserProfileCompleteResponseSchemas.update_success_200
            ),
            400: openapi.Response("Invalid request data", UserProfileCompleteResponseSchemas.error_400),
            401: openapi.Response(
                "Authentication credentials not provided", UserProfileCompleteResponseSchemas.error_401
            ),
            404: openapi.Response("User not found", UserProfileCompleteResponseSchemas.error_404),
            500: openapi.Response("Internal server error", UserProfileCompleteResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Update user's complete profile."""
        serializer = serializers.UserProfileCompleteSerializer(
            data=request.data, partial=True, context={"request": request}
        )
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Extract profile photo data if provided
            profile_photo = serializer.validated_data.get("profile_photo")
            profile_photo_data = None
            profile_photo_filename = None

            if profile_photo:
                profile_photo_data = profile_photo.read()
                profile_photo_filename = profile_photo.name

            user_profile = services.UserService.update_complete_profile(
                user_id=request.user.id,
                username=serializer.validated_data.get("username"),
                email=serializer.validated_data.get("email"),
                first_name=serializer.validated_data.get("first_name"),
                last_name=serializer.validated_data.get("last_name"),
                gender=serializer.validated_data.get("gender"),
                profile_photo_data=profile_photo_data,
                profile_photo_filename=profile_photo_filename,
            )

            return Response(
                create_success_response(serialize_dataclass(user_profile), "Profile updated successfully"),
                status=status.HTTP_200_OK,
            )
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to update profile: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AddBookmarkAyahView(APIView):
    """View for bookmark operations."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Add a bookmark for a specific Ayah. Use the list bookmarks endpoint to retrieve bookmarks with multi-language content including translations, transliterations, and images.",
        request_body=serializers.BookmarkAyahSerializer,
        responses={
            201: openapi.Response("Bookmark successfully added", BookmarkResponseSchemas.add_success_201),
            200: openapi.Response("Bookmark already exists", BookmarkResponseSchemas.add_success_200),
            400: openapi.Response("Invalid request data", BookmarkResponseSchemas.error_400),
            401: openapi.Response("Authentication credentials not provided", BookmarkResponseSchemas.error_401),
            404: openapi.Response("Surah or Ayah not found", BookmarkResponseSchemas.error_404),
            500: openapi.Response("Internal server error", BookmarkResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Add a bookmark."""
        serializer = serializers.BookmarkAyahSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            bookmark = services.BookmarkService.add_bookmark(
                user_id=request.user.id,
                surah_id=str(serializer.validated_data["surah_id"]),
                ayah_number=serializer.validated_data["ayah_id"],
            )

            if bookmark:
                return Response(
                    create_success_response(serialize_dataclass(bookmark), "Bookmark added successfully"),
                    status=status.HTTP_201_CREATED,
                )
            else:
                # Bookmark already exists, get existing one
                existing_bookmarks = services.BookmarkService.get_user_bookmarks(request.user.id)
                for bm in existing_bookmarks:
                    if (
                        bm.ayah.surah.surah_number == serializer.validated_data["surah_id"]
                        and bm.ayah.ayah_number == serializer.validated_data["ayah_id"]
                    ):
                        return Response(
                            create_success_response(serialize_dataclass(bm), "Bookmark already exists"),
                            status=status.HTTP_200_OK,
                        )

                return Response(create_error_response("Bookmark already exists"), status=status.HTTP_200_OK)
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to add bookmark: {str(e)}"), status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class RemoveBookmarkAyahView(APIView):
    """View for bookmark operations."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Remove a bookmark for a specific Ayah. Use the list bookmarks endpoint to retrieve remaining bookmarks with multi-language content.",
        request_body=serializers.BookmarkAyahSerializer,
        responses={
            200: openapi.Response("Bookmark successfully removed", BookmarkResponseSchemas.remove_success_200),
            400: openapi.Response("Invalid request data", BookmarkResponseSchemas.error_400),
            401: openapi.Response("Authentication credentials not provided", BookmarkResponseSchemas.error_401),
            404: openapi.Response("Bookmark not found", BookmarkResponseSchemas.error_404),
            500: openapi.Response("Internal server error", BookmarkResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Remove a bookmark."""
        serializer = serializers.BookmarkAyahSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            removed = services.BookmarkService.remove_bookmark(
                user_id=request.user.id,
                surah_id=str(serializer.validated_data["surah_id"]),
                ayah_number=serializer.validated_data["ayah_id"],
            )

            if not removed:
                return Response(create_error_response("Bookmark does not exist"), status=status.HTTP_404_NOT_FOUND)

            return Response(create_success_response(None, "Bookmark removed successfully"), status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to remove bookmark: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ListBookmarksView(APIView):
    """View for listing all bookmarks for the authenticated user."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="List all bookmarks for the authenticated user with multi-language support",
        manual_parameters=[
            openapi.Parameter(
                "Language-Code",
                openapi.IN_HEADER,
                description="Preferred language for translations and transliterations (e.g., 'en', 'id', 'ar'). Defaults to 'en' if not provided.",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response("Successfully retrieved bookmarks", BookmarkResponseSchemas.list_success_200),
            401: openapi.Response("Authentication credentials not provided", BookmarkResponseSchemas.error_401),
            500: openapi.Response("Internal server error", BookmarkResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get all bookmarks for the current user with multi-language support."""
        try:
            # Get language preference from headers
            from quran.services import LanguageService

            requested_language = LanguageService.get_language_from_header(request)

            # Get enhanced bookmarks with language support
            enhanced_bookmarks, language_metadata = services.BookmarkService.get_user_bookmarks_enhanced(
                request.user.id, requested_language
            )

            return Response(
                create_success_response(
                    {
                        "bookmarks": serialize_dataclass_list(enhanced_bookmarks),
                        "count": len(enhanced_bookmarks),
                        "language_info": serialize_dataclass(language_metadata),
                    },
                    "User bookmarks retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve bookmarks: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class EmailAvailabilityView(APIView):
    """View for checking email availability for registration."""

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Check if an email address is available for registration. Returns whether the email is already in use by an existing user.",
        request_body=serializers.EmailAvailabilitySerializer,
        responses={
            200: openapi.Response("Email availability check successful", EmailAvailabilityResponseSchemas.success_200),
            400: openapi.Response("Invalid email format", EmailAvailabilityResponseSchemas.error_400),
            500: openapi.Response("Internal server error", EmailAvailabilityResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Check email availability."""
        serializer = serializers.EmailAvailabilitySerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid email format", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            email = serializer.validated_data["email"]

            # Check email availability using the service
            availability = services.AuthenticationService.check_email_availability(email)

            return Response(
                create_success_response(serialize_dataclass(availability), "Email availability check completed"),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Email availability check failed: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class EmailVerificationRequestView(APIView):
    """View for requesting email verification for registration."""

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Request email verification for user registration. This will send a verification code to the provided email address (currently uses hardcoded '0000' for development).",
        request_body=serializers.EmailVerificationRequestSerializer,
        responses={
            200: openapi.Response(
                "Email verification request successful", EmailVerificationResponseSchemas.request_success_200
            ),
            400: openapi.Response("Invalid email or email already in use", EmailVerificationResponseSchemas.error_400),
            500: openapi.Response("Internal server error", EmailVerificationResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Request email verification for registration."""
        serializer = serializers.EmailVerificationRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid email format", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            email = serializer.validated_data["email"]

            # Request email verification using the service
            verification_request = services.AuthenticationService.request_email_verification_for_registration(email)

            return Response(
                create_success_response(serialize_dataclass(verification_request), "Email verification requested"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Email verification request failed: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class EmailVerificationCodeView(APIView):
    """View for verifying email verification code for registration."""

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Verify email verification code for registration. Use '0000' as the verification code for development.",
        request_body=serializers.EmailVerificationCodeSerializer,
        responses={
            200: openapi.Response(
                "Email verification code validation completed", EmailVerificationResponseSchemas.verify_success_200
            ),
            400: openapi.Response("Invalid verification code or email", EmailVerificationResponseSchemas.error_400),
            500: openapi.Response("Internal server error", EmailVerificationResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Verify email verification code for registration."""
        serializer = serializers.EmailVerificationCodeSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            email = serializer.validated_data["email"]
            verification_code = serializer.validated_data["verification_code"]

            # Verify email code using the service
            verification_result = services.AuthenticationService.verify_email_code_for_registration(
                email, verification_code
            )

            return Response(
                create_success_response(serialize_dataclass(verification_result), "Email verification completed"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Email verification failed: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PasswordResetRequestView(APIView):
    """View for requesting password reset."""

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Request password reset for an existing user account. This will send a verification code to the provided email address (currently uses hardcoded '0000' for development).",
        request_body=serializers.PasswordResetRequestSerializer,
        responses={
            200: openapi.Response(
                "Password reset request successful", PasswordResetResponseSchemas.request_success_200
            ),
            400: openapi.Response("Invalid email or user not found", PasswordResetResponseSchemas.error_400),
            500: openapi.Response("Internal server error", PasswordResetResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Request password reset."""
        serializer = serializers.PasswordResetRequestSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid email format", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            email = serializer.validated_data["email"]

            # Request password reset using the service
            reset_request = services.AuthenticationService.request_password_reset(email)

            return Response(
                create_success_response(serialize_dataclass(reset_request), "Password reset requested"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Password reset request failed: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PasswordResetVerificationView(APIView):
    """View for verifying password reset code."""

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Verify password reset verification code. Use '0000' as the verification code for development.",
        request_body=serializers.PasswordResetVerificationSerializer,
        responses={
            200: openapi.Response(
                "Password reset verification completed", PasswordResetResponseSchemas.verify_success_200
            ),
            400: openapi.Response("Invalid verification code or email", PasswordResetResponseSchemas.error_400),
            500: openapi.Response("Internal server error", PasswordResetResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Verify password reset code."""
        serializer = serializers.PasswordResetVerificationSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            email = serializer.validated_data["email"]
            verification_code = serializer.validated_data["verification_code"]

            # Verify password reset code using the service
            verification_result = services.AuthenticationService.verify_password_reset_code(email, verification_code)
            if not verification_result.is_verified:
                return Response(create_error_response("Invalid verification code"), status=status.HTTP_400_BAD_REQUEST)

            return Response(
                create_success_response(
                    serialize_dataclass(verification_result), "Password reset verification completed"
                ),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Password reset verification failed: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class PasswordResetCompleteView(APIView):
    """View for completing password reset with verification."""

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Complete password reset with verification code and new password. Use '0000' as the verification code for development.",
        request_body=serializers.PasswordResetCompleteSerializer,
        responses={
            200: openapi.Response(
                "Password reset completed successfully", PasswordResetResponseSchemas.complete_success_200
            ),
            400: openapi.Response(
                "Invalid verification code, email, or password", PasswordResetResponseSchemas.error_400
            ),
            500: openapi.Response("Internal server error", PasswordResetResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Complete password reset with verification."""
        serializer = serializers.PasswordResetCompleteSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            email = serializer.validated_data["email"]
            verification_code = serializer.validated_data["verification_code"]
            new_password = serializer.validated_data["new_password"]

            # Complete password reset using the service
            reset_result = services.AuthenticationService.reset_password_with_verification(
                email, verification_code, new_password
            )

            return Response(
                create_success_response(serialize_dataclass(reset_result), "Password reset completed"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Password reset completion failed: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AyahLikeView(APIView):
    """View for liking ayahs."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Like a specific ayah. If the user has already disliked this ayah, it will change to a like. If already liked, it will update the like timestamp.",
        request_body=serializers.AyahReactionSerializer,
        responses={
            200: openapi.Response("Ayah liked successfully", AyahInteractionResponseSchemas.reaction_success_200),
            400: openapi.Response("Invalid request data", AyahInteractionResponseSchemas.error_400),
            401: openapi.Response("Authentication required", AyahInteractionResponseSchemas.error_401),
            500: openapi.Response("Internal server error", AyahInteractionResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Like an ayah."""
        serializer = serializers.AyahReactionSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]
            ayah_number = serializer.validated_data["ayah_number"]

            # Use the service to like the ayah
            reaction_result = services.AyahInteractionService.react_to_ayah(
                user_id=request.user.id, surah_id=surah_id, ayah_number=ayah_number, reaction_type="like"
            )

            return Response(
                create_success_response(serialize_dataclass(reaction_result), "Ayah liked successfully"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to like ayah: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AyahDislikeView(APIView):
    """View for disliking ayahs."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Dislike a specific ayah. If the user has already liked this ayah, it will change to a dislike. If already disliked, it will update the dislike timestamp.",
        request_body=serializers.AyahReactionSerializer,
        responses={
            200: openapi.Response("Ayah disliked successfully", AyahInteractionResponseSchemas.reaction_success_200),
            400: openapi.Response("Invalid request data", AyahInteractionResponseSchemas.error_400),
            401: openapi.Response("Authentication required", AyahInteractionResponseSchemas.error_401),
            500: openapi.Response("Internal server error", AyahInteractionResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Dislike an ayah."""
        serializer = serializers.AyahReactionSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]
            ayah_number = serializer.validated_data["ayah_number"]

            # Use the service to dislike the ayah
            reaction_result = services.AyahInteractionService.react_to_ayah(
                user_id=request.user.id, surah_id=surah_id, ayah_number=ayah_number, reaction_type="dislike"
            )

            return Response(
                create_success_response(serialize_dataclass(reaction_result), "Ayah disliked successfully"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to dislike ayah: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AyahRemoveReactionView(APIView):
    """View for removing reactions from ayahs."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Remove user's reaction (like or dislike) from a specific ayah.",
        request_body=serializers.AyahReactionRemoveSerializer,
        responses={
            200: openapi.Response("Reaction removed successfully", AyahInteractionResponseSchemas.reaction_success_200),
            400: openapi.Response("Invalid request data", AyahInteractionResponseSchemas.error_400),
            401: openapi.Response("Authentication required", AyahInteractionResponseSchemas.error_401),
            500: openapi.Response("Internal server error", AyahInteractionResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Remove reaction from an ayah."""
        serializer = serializers.AyahReactionRemoveSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]
            ayah_number = serializer.validated_data["ayah_number"]

            # Use the service to remove reaction from the ayah
            reaction_result = services.AyahInteractionService.remove_reaction_from_ayah(
                user_id=request.user.id, surah_id=surah_id, ayah_number=ayah_number
            )

            return Response(
                create_success_response(serialize_dataclass(reaction_result), "Reaction removed successfully"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to remove reaction: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AyahReportView(APIView):
    """View for reporting ayahs."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Report a specific ayah with a complaint. Users can only report each ayah once.",
        request_body=serializers.AyahReportSerializer,
        responses={
            200: openapi.Response("Ayah reported successfully", AyahInteractionResponseSchemas.report_success_200),
            400: openapi.Response("Invalid request data or already reported", AyahInteractionResponseSchemas.error_400),
            401: openapi.Response("Authentication required", AyahInteractionResponseSchemas.error_401),
            500: openapi.Response("Internal server error", AyahInteractionResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Report an ayah."""
        serializer = serializers.AyahReportSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]
            ayah_number = serializer.validated_data["ayah_number"]
            complaint_type = serializer.validated_data["complaint_type"]
            description = serializer.validated_data.get("description", "")

            # Use the service to report the ayah
            report_result = services.AyahInteractionService.report_ayah(
                user_id=request.user.id,
                surah_id=surah_id,
                ayah_number=ayah_number,
                complaint_type=complaint_type,
                description=description,
            )

            return Response(
                create_success_response(serialize_dataclass(report_result), "Ayah reported successfully"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to report ayah: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

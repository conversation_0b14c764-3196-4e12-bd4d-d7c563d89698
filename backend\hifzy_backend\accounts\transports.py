from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List
from core.transports import League, CompactUser


@dataclass(frozen=True)
class UserProfile:
    """Full user profile information."""

    id: int
    username: str
    first_name: str
    last_name: str
    email: str
    profile_photo: Optional[str]
    date_joined: datetime
    total_xp: int = 0
    current_level: int = 1
    day_streak: int = 0
    gender: Optional[str] = None
    league: Optional[League] = None


@dataclass(frozen=True)
class AuthToken:
    """Authentication token information."""

    token: str
    user: UserProfile


@dataclass(frozen=True)
class SocialSummary:
    """Social information summary for user profile."""

    followers_count: int
    following_count: int
    followers: List[CompactUser]
    following: List[CompactUser]


@dataclass(frozen=True)
class RecentBookmark:
    """Recent bookmark information for user profile."""

    id: int
    surah_id: int
    ayah_number: int
    surah_title: Optional[str]
    arabic_text: Optional[str]
    created_at: datetime


@dataclass(frozen=True)
class BookmarkSummary:
    """Bookmark summary information for user profile."""

    bookmarks_count: int
    recent_bookmarks: List[RecentBookmark]


@dataclass(frozen=True)
class LeaguePosition:
    """User's position within their current league."""

    league: Optional[League]
    user_rank: Optional[int]
    total_participants: Optional[int]


@dataclass(frozen=True)
class EmailAvailability:
    """Email availability check result."""

    email: str
    is_available: bool


@dataclass(frozen=True)
class EmailVerificationRequest:
    """Email verification request result."""

    email: str
    verification_required: bool
    message: str


@dataclass(frozen=True)
class EmailVerificationResult:
    """Email verification code validation result."""

    email: str
    is_verified: bool
    message: str


@dataclass(frozen=True)
class PasswordResetRequest:
    """Password reset request result."""

    email: str
    verification_required: bool
    message: str


@dataclass(frozen=True)
class PasswordResetResult:
    """Password reset completion result."""

    email: str
    is_reset: bool
    message: str


@dataclass(frozen=True)
class AyahReactionResult:
    """Ayah reaction operation result."""

    surah_id: int
    ayah_number: int
    user_reaction: Optional[str]  # 'like', 'dislike', or None
    like_count: int
    dislike_count: int
    message: str


@dataclass(frozen=True)
class AyahReportResult:
    """Ayah report submission result."""

    surah_id: int
    ayah_number: int
    complaint_type: str
    is_reported: bool
    message: str


@dataclass(frozen=True)
class EnhancedUserProfile:
    """Enhanced user profile with social, league, and bookmark information."""

    # Basic profile information
    id: int
    username: str
    first_name: str
    last_name: str
    email: str
    profile_photo: Optional[str]
    date_joined: datetime
    # Enhanced information
    social_summary: SocialSummary
    league_position: LeaguePosition
    bookmark_summary: BookmarkSummary
    gender: Optional[str] = None
    total_xp: int = 0
    current_level: int = 1
    day_streak: int = 0

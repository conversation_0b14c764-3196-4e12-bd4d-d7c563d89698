from rest_framework import serializers
from django.db.models import Sum
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
import re
from .models import UserFollowMM, League


class UserFollowSerializer(serializers.ModelSerializer):
    """Updated serializer for UserFollowMM model."""

    class Meta:
        model = UserFollowMM
        fields = ["id", "follower", "following", "created_at"]
        read_only_fields = ["id", "created_at"]


class UserFollowerSerializer(serializers.ModelSerializer):
    """Serializer for displaying follower information."""

    follower_username = serializers.CharField(source="follower.username", read_only=True)
    follower_first_name = serializers.CharField(source="follower.first_name", read_only=True)
    follower_last_name = serializers.CharField(source="follower.last_name", read_only=True)
    follower_profile_photo = serializers.ImageField(source="follower.profile_photo", read_only=True)

    class Meta:
        model = UserFollowMM
        fields = [
            "id",
            "follower",
            "follower_username",
            "follower_first_name",
            "follower_last_name",
            "follower_profile_photo",
            "created_at",
        ]
        read_only_fields = [
            "id",
            "follower",
            "follower_username",
            "follower_first_name",
            "follower_last_name",
            "follower_profile_photo",
            "created_at",
        ]


class UserFollowingSerializer(serializers.ModelSerializer):
    """Serializer for displaying following information."""

    following_username = serializers.CharField(source="following.username", read_only=True)
    following_first_name = serializers.CharField(source="following.first_name", read_only=True)
    following_last_name = serializers.CharField(source="following.last_name", read_only=True)
    following_profile_photo = serializers.ImageField(source="following.profile_photo", read_only=True)

    class Meta:
        model = UserFollowMM
        fields = [
            "id",
            "following",
            "following_username",
            "following_first_name",
            "following_last_name",
            "following_profile_photo",
            "created_at",
        ]
        read_only_fields = [
            "id",
            "following",
            "following_username",
            "following_first_name",
            "following_last_name",
            "following_profile_photo",
            "created_at",
        ]


class LeagueSerializer(serializers.ModelSerializer):
    """Serializer for League model."""

    class Meta:
        model = League
        fields = ["id", "name", "rank"]
        read_only_fields = ["id", "name", "rank"]

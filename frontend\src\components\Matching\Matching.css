.canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none; /* Prevent canvas from blocking clicks */
}

/* Адаптивность списков */
.text-list,
.image-list {
  display: flex;
  /* flex-wrap: wrap; */
  justify-content: space-between;
  flex-direction: column;
   /* Уменьшил процентное соотношение для лучшей адаптивности */
  gap: 20px;
  width: 300px;
}

.matching-title{
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
}

/* Адаптивные элементы карточек */

.image-item {
  /* flex: 1 1 100%; */
  margin: 10px 0;
  background-color: #f9f9f9;
  list-style-type: none;
  cursor: pointer;
  transition: border 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
  border-radius: 15px;
  width: 200px;
  height: 200px;
}

.text-item {
  margin: 10px 0;
  background-color: #f9f9f9;
  list-style-type: none;
  cursor: pointer;
  transition: border 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
  border-radius: 15px;
  width: 300px;
  height: 200px;
  flex-direction: column;
  padding: 20px;
}

.ayah-text {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30px;
  font-weight: 600;
  text-align: center;
}

.translation-text {
  display: flex;
  justify-content: center;
  font-size: 15px;
  align-items: center;
  color: #828282;
  text-align: center;
}

/* Изменения для изображений */
.image-item img {
  object-fit: contain;
  width: 100%;
  border-radius: 15px;
  border: 2px solid transparent;
}

/* Выбранные элементы */
.text-item.selected,
.image-item.selected {
  border: 2px solid green;
}

.submit-container {
  display: flex;
  justify-content: center;
  position: sticky; /* Липкое позиционирование */
  bottom: 0; /* Фиксируем у нижнего края контейнера */
  /* padding: 10px; */
  background-color: white; /* Фон для кнопки, чтобы она выделялась */
}
/* Кнопка отправки */
.submit-matching {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 300px;
  padding: 10px 5px;
  background-color: #97cb9c;
  filter: drop-shadow(12px 5px 5px #9a999925);
  border-radius: 30px;
  cursor: pointer;
}

.submit-matching:hover {
  background-color: #218838;
}

/* Результат */
.result {
  margin-top: 10px;
  font-weight: bold;
}


/* Родительский контейнер с фиксированной высотой и вертикальной прокруткой */
.game-container {
  height: 100%;
  overflow-y: auto; /* Вертикальная прокрутка */
  overflow-x: hidden; /* Отключаем горизонтальную прокрутку */
  padding-right: 10px; /* Дополнительное пространство для скроллбара */
}

/* Общий стиль для адаптивности */
.game-grid {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  gap: 300px;
  flex-wrap: wrap;
  max-width: 100%; /* Ограничение ширины контейнера */
  box-sizing: border-box;
  padding-bottom: 20px; /* Пространство для кнопки отправки */
}

/* Прочие стили остаются такими же, как и были ранее */

.game-container::-webkit-scrollbar {
  width: 0.5em; 
}

.game-container::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px; 
}

.game-container::-webkit-scrollbar-thumb {
  background-color: #97CB9C; 
  outline: 1px solid slategrey; 
  border-radius: 10px; 
}


from rest_framework import serializers
from .models import ArabicLetter


class ArabicLetterSerializer(serializers.ModelSerializer):
    """Serializer for ArabicLetter model."""

    class Meta:
        model = ArabicLetter
        fields = ["id", "title", "transcription", "audio_url"]
        read_only_fields = ["id", "title", "transcription", "audio_url"]


class SurahInfoRequestSerializer(serializers.Serializer):
    """
    Serializer for surah information request.

    Validates the input data for retrieving information about a specific Surah.
    """

    surah_id = serializers.IntegerField(required=True, help_text="ID of the Surah")


class ReaderInfoResponseSerializer(serializers.Serializer):
    """Serializer for reader information in API responses."""

    reader_name = serializers.CharField(help_text="Name of the Quran reader/reciter")
    reader_slug = serializers.CharField(help_text="URL-friendly reader identifier")
    recording_count = serializers.IntegerField(help_text="Number of audio recordings available for this reader")


class SurahInfoResponseSerializer(serializers.Serializer):
    """Serializer for surah information in API responses."""

    surah_id = serializers.IntegerField(help_text="ID of the Surah")
    name_simple = serializers.CharField(help_text="Simple name of the Surah")
    arabic_name = serializers.CharField(help_text="Original Arabic name of the surah")
    verses_count = serializers.IntegerField(help_text="Number of verses in the Surah")
    is_pre_bismillah = serializers.BooleanField(help_text="Whether the surah has a pre-bismillah")
    is_meccan = serializers.BooleanField(help_text="Whether the surah was revealed in Mecca")
    is_medinian = serializers.BooleanField(help_text="Whether the surah was revealed in Medina")


class AyahTextResponseSerializer(serializers.Serializer):
    """Serializer for ayah text in API responses."""

    surah_id = serializers.IntegerField(help_text="ID of the Surah")
    ayah_id = serializers.IntegerField(help_text="ID of the Ayah within the Surah")
    arabic_text = serializers.CharField(help_text="Arabic text of the Ayah")
    transliteration = serializers.CharField(help_text="English transliteration of the Ayah")
    translation = serializers.CharField(help_text="English translation of the Ayah")
    audio_url = serializers.URLField(
        allow_null=True, help_text="Audio URL for the specified reader, null if not available"
    )
    image_url = serializers.URLField(
        allow_null=True, help_text="Absolute URL to the ayah image for visual display, null if not available"
    )


class LanguageMetadataResponseSerializer(serializers.Serializer):
    """Serializer for language metadata in API responses."""

    requested_language = serializers.CharField(allow_null=True, help_text="Language code that was requested")
    used_language = serializers.CharField(help_text="Language code that was actually used")
    fallback_applied = serializers.BooleanField(help_text="Whether fallback to another language was applied")


class AyahTextDetailResponseSerializer(serializers.Serializer):
    """Serializer for detailed ayah text response."""

    surah_id = serializers.IntegerField(help_text="ID of the Surah")
    ayah_id = serializers.IntegerField(help_text="ID of the Ayah within the Surah")
    arabic_text = serializers.CharField(help_text="Arabic text of the Ayah")
    transliteration = serializers.CharField(help_text="English transliteration of the Ayah")
    translation = serializers.CharField(help_text="English translation of the Ayah")
    globalNumber = serializers.IntegerField(help_text="Global number of the Ayah in the Quran")
    language_info = LanguageMetadataResponseSerializer(help_text="Language selection metadata", required=False)


class SurahInfoFullResponseSerializer(serializers.Serializer):
    """Serializer for complete surah information response."""

    surah_info = SurahInfoResponseSerializer(help_text="Basic surah information")
    ayahs = AyahTextResponseSerializer(
        many=True, help_text="List of ayahs in the surah with audio URLs and image URLs for the specified reader"
    )
    language_info = LanguageMetadataResponseSerializer(help_text="Language selection metadata", required=False)


class AvailableReadersResponseSerializer(serializers.Serializer):
    """Serializer for available readers response."""

    readers = ReaderInfoResponseSerializer(many=True, help_text="List of all available audio readers in the system")


class LanguageInfoResponseSerializer(serializers.Serializer):
    """Serializer for language information in API responses."""

    language_code = serializers.CharField(help_text="Language locale identifier (e.g., 'en', 'id', 'ar')")
    language_name = serializers.CharField(help_text="Human-readable language name (e.g., 'English', 'Indonesian')")
    has_translation = serializers.BooleanField(help_text="Whether translation is available for this language")
    has_transliteration = serializers.BooleanField(help_text="Whether transliteration is available for this language")
class SurahPreviewRequestSerializer(serializers.Serializer):
    """Request serializer for surah preview."""

    # surah_id = serializers.IntegerField(help_text="Surah ID to get preview for")
    ordering = serializers.ChoiceField(
        choices=[("traditional", "Traditional Quran order (1-114)"), ("pedagogical", "Pedagogical learning sequence")],
        default="pedagogical",
        required=False,
        help_text="Ordering method for surah preview ('traditional' or 'pedagogical')",
    )


class AvailableLanguagesResponseSerializer(serializers.Serializer):
    """Serializer for available languages response."""

    languages = LanguageInfoResponseSerializer(
        many=True, help_text="List of all available languages with translation/transliteration support"
    )

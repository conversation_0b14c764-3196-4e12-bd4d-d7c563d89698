"""
Service layer.

This module contains business logic for game-related operations.
Services handle game generation, validation, and coordination.
"""

from typing import List, <PERSON>ple
from rest_framework.exceptions import ValidationError, NotFound

from . import transports
from . import repositories
from .repositories import (
    AyahRepository,
    SurahRepository,
    AudioRecordingRepository,
    LanguageRepository,
    ArabicLetterRepository,
)
from .models import Ayah, AyahTranslation, Surah, SurahTranslation
from core.transports import LanguageMetadata


class QuranService:
    """Service for Quran data operations."""

    # @staticmethod
    # def get_surah_ayahs(surah_id: int) -> List[transports.AyahText]:
    #     """Get all Ayahs for a specific Surah."""
    #     ayahs = AyahRepository.get_ayahs_by_surah(surah_id)

    #     ayah_texts = []
    #     for ayah in ayahs:
    #         ayah_obj = Ayah.objects.get(surah__surah_number=surah_id, ayah_number=ayah.ayah_number)
    #         ayah_translation = AyahTranslation.objects.filter(ayah=ayah_obj, locale="en").first()
    #         ayah_text = AyahTranslation.objects.filter(ayah=ayah_obj, locale="ar").first()
    #         ayah_transliteration = AyahTranslation.objects.filter(ayah=ayah_obj, locale="en_transliteration").first()

    #         ayah_texts.append(
    #             transports.AyahText(
    #                 surah_id=ayah.surah_id,
    #                 ayah_id=ayah.ayah_number,
    #                 arabic_text=ayah_text.text,
    #                 transliteration=ayah_transliteration.text,
    #                 translation=ayah_translation.text,
    #             )
    #         )
    #     return ayah_texts

    @staticmethod
    def get_surah_info(surah_id: int) -> transports.SurahInfo:
        """Get information about a specific Surah."""
        try:
            surah = Surah.objects.get(surah_number=surah_id)
        except Surah.DoesNotExist:
            raise NotFound(f"Surah with ID {surah_id} not found")

        # Get English translation
        surah_translations = SurahTranslation.objects.get(surah=surah, locale="en")

        # Get Arabic name
        try:
            arabic_translation = SurahTranslation.objects.get(surah=surah, locale="ar")
            arabic_name = arabic_translation.title
        except SurahTranslation.DoesNotExist:
            arabic_name = ""  # Fallback to empty string if Arabic name not found

        return transports.SurahInfo(
            surah_id=surah.surah_number,
            name_simple=surah_translations.title,
            arabic_name=arabic_name,
            verses_count=surah.ayah_count,
            is_pre_bismillah=surah.is_pre_bismillah,
            is_meccan=surah.is_meccan,
            is_medinian=surah.is_medinian,
        )

    @staticmethod
    def get_ayah_text(surah_id: int, ayah_id: int) -> transports.AyahText:
        """Get text for a specific Ayah."""
        try:
            ayah = Ayah.objects.get(surah__surah_number=surah_id, ayah_number=ayah_id)
        except Ayah.DoesNotExist:
            raise NotFound(f"Ayah {ayah_id} in Surah {surah_id} not found")
        ayah_text = AyahTranslation.objects.filter(ayah=ayah, locale="ar").first()
        ayah_transliteration = AyahTranslation.objects.filter(ayah=ayah, locale="en_transliteration").first()
        ayah_translation = AyahTranslation.objects.filter(ayah=ayah, locale="en").first()
        return transports.AyahText(
            surah_id=surah_id,
            ayah_id=ayah_id,
            arabic_text=ayah_text.text,
            transliteration=ayah_transliteration.text,
            translation=ayah_translation.text,
        )

    @staticmethod
    def get_ayahs_range(surah_id: int, start_ayah: int, count: int) -> List[transports.AyahText]:
        """Get a range of Ayahs from a Surah."""
        # Validate surah exists
        # surah_info = QuranService.get_surah_info(surah_id)
        surah = SurahRepository.get_surah_by_number(surah_id)
        if not surah:
            raise NotFound(f"Surah with ID {surah_id} not found")

        if start_ayah < 1 or start_ayah > surah.ayah_count:
            raise ValidationError(f"Invalid start_ayah: {start_ayah}")

        if count < 1:
            raise ValidationError("Count must be at least 1")

        # Adjust count if it exceeds available ayahs
        max_count = surah.ayah_count - start_ayah + 1
        actual_count = min(count, max_count)
        # TODO change json repo
        return repositories.QuranData.get_ayahs_range(surah_id, start_ayah, actual_count)

    # @staticmethod
    # def get_surah_preview(
    #     surah_id: int, language: str = "en", ordering: str = "pedagogical"
    # ) -> Tuple[transports.SurahPreview, LanguageMetadata]:
    #     """Get surah preview with first 3 ayahs and multi-language support."""
    #     # Validate surah exists
    #     surah = SurahRepository.get_surah_by_number(surah_id)
    #     if not surah:
    #         raise NotFound(f"Surah with ID {surah_id} not found")

    #     # Get surah title with language support
    #     surah_title, language_metadata = LanguageService.get_surah_title_with_language(surah_id, language)

    #     # Get first 3 ayahs
    #     ayahs = AyahRepository.get_ayahs_by_surah(str(surah_id))
    #     first_three_ayahs = ayahs[:3]  # Get first 3 ayahs (or fewer if surah has less than 3)

    #     # Create ayah previews
    #     ayah_previews = []
    #     for ayah in first_three_ayahs:
    #         ayah_preview = transports.AyahPreview(ayah_number=ayah.ayah_number, image_url=ayah.image)
    #         ayah_previews.append(ayah_preview)

    #     # Calculate position based on ordering
    #     position_in_order = None
    #     if ordering == "traditional":
    #         position_in_order = surah_id  # Traditional order is 1-114
    #     elif ordering == "pedagogical":
    #         # Get pedagogical position
    #         from progress.services import PedagogicalProgressionService

    #         pedagogical_order = PedagogicalProgressionService.get_all_surahs_in_learning_order()
    #         try:
    #             position_in_order = pedagogical_order.index(surah_id) + 1  # 1-based index
    #         except ValueError:
    #             position_in_order = None  # Surah not found in pedagogical order

    #     # Create surah preview
    #     surah_preview = transports.SurahPreview(
    #         surah_id=surah.surah_id,
    #         surah_title=surah_title,
    #         ayah_previews=ayah_previews,
    #         total_ayahs_in_surah=surah.ayah_count,
    #         returned_ayahs_count=len(ayah_previews),
    #         is_pre_bismillah=surah.is_pre_bismillah,
    #         is_meccan=surah.is_meccan,
    #         is_medinian=surah.is_medinian,
    #         position_in_order=position_in_order,
    #     )

    #     return surah_preview, language_metadata

    @staticmethod
    def get_all_surahs_preview(
        language: str = "en", use_pedagogical_order: bool = True
    ) -> Tuple[List[transports.SurahPreview], LanguageMetadata]:
        """Get preview data for all 114 surahs with multi-language support."""
        # Import here to avoid circular imports
        from progress.services import PedagogicalProgressionService

        if use_pedagogical_order:
            # Get surahs in pedagogical learning order
            surah_numbers = PedagogicalProgressionService.get_all_surahs_in_learning_order()
            all_surahs = []
            for surah_number in surah_numbers:
                surah = SurahRepository.get_surah_by_number(surah_number)
                if surah:
                    all_surahs.append(surah)
        else:
            # Get all surahs in traditional order
            all_surahs = SurahRepository.get_all_surahs()

        if not all_surahs:
            return [], LanguageMetadata(requested_language=language, used_language="en", fallback_applied=False)

        surah_previews = []
        overall_used_language = language
        overall_fallback_applied = False

        for i, surah in enumerate(all_surahs):
            try:
                # Get surah title with language support
                surah_title, title_language_metadata = LanguageService.get_surah_title_with_language(
                    surah.surah_id, language
                )

                # Get Arabic name
                arabic_name, _ = LanguageService.get_surah_title_with_language(surah.surah_id, "ar")

                # Track if any fallback was applied
                if title_language_metadata.fallback_applied:
                    overall_fallback_applied = True
                    if overall_used_language == language:
                        overall_used_language = title_language_metadata.used_language

                # Get first 3 ayahs for this surah
                ayahs = AyahRepository.get_ayahs_by_surah(str(surah.surah_id))
                first_three_ayahs = ayahs[:3]  # Get first 3 ayahs (or fewer if surah has less than 3)

                # Create ayah previews
                ayah_previews = []
                for ayah in first_three_ayahs:
                    ayah_preview = transports.AyahPreview(ayah_number=ayah.ayah_number, image_url=ayah.image)
                    ayah_previews.append(ayah_preview)

                # Calculate position in the current ordering
                if use_pedagogical_order:
                    position_in_order = i + 1  # 1-based index in pedagogical order
                else:
                    position_in_order = surah.surah_id  # Traditional order

                # Create surah preview
                surah_preview = transports.SurahPreview(
                    surah_id=surah.surah_id,
                    surah_title=surah_title,
                    arabic_name=arabic_name,
                    ayah_previews=ayah_previews,
                    total_ayahs_in_surah=surah.ayah_count,
                    returned_ayahs_count=len(ayah_previews),
                    is_pre_bismillah=surah.is_pre_bismillah,
                    is_meccan=surah.is_meccan,
                    is_medinian=surah.is_medinian,
                    position_in_order=position_in_order,
                )

                surah_previews.append(surah_preview)

            except Exception as e:
                # If there's an error with a specific surah, log it but continue with others
                print(f"Error processing surah {surah.surah_id}: {str(e)}")
                continue

        # Create overall language metadata
        language_metadata = LanguageMetadata(
            requested_language=language, used_language=overall_used_language, fallback_applied=overall_fallback_applied
        )

        return surah_previews, language_metadata


class AudioRecordingService:
    """Service for audio recording operations."""

    @staticmethod
    def get_ayah_audio_recordings(surah_id: int, ayah_number: int) -> List[transports.AudioRecording]:
        """Get all audio recordings for a specific ayah."""
        try:
            ayah = Ayah.objects.get(surah__surah_number=surah_id, ayah_number=ayah_number)
        except Ayah.DoesNotExist:
            raise NotFound(f"Ayah {ayah_number} in Surah {surah_id} not found")

        return AudioRecordingRepository.get_audio_recordings_by_ayah(ayah.id)

    @staticmethod
    def get_ayah_audio_by_reader(surah_id: int, ayah_number: int, reader_slug: str) -> transports.AudioRecording:
        """Get audio recording for a specific ayah and reader."""
        try:
            ayah = Ayah.objects.get(surah__surah_number=surah_id, ayah_number=ayah_number)
        except Ayah.DoesNotExist:
            raise NotFound(f"Ayah {ayah_number} in Surah {surah_id} not found")

        audio_recording = AudioRecordingRepository.get_audio_recording_by_ayah_and_reader(ayah.id, reader_slug)
        if not audio_recording:
            raise NotFound(
                f"Audio recording not found for reader '{reader_slug}' and ayah {ayah_number} in surah {surah_id}"
            )

        return audio_recording

    @staticmethod
    def get_reader_audio_recordings(reader_slug: str) -> List[transports.AudioRecording]:
        """Get all audio recordings for a specific reader."""
        return AudioRecordingRepository.get_audio_recordings_by_reader(reader_slug)

    @staticmethod
    def get_surah_readers(surah_id: int) -> List[transports.ReaderInfo]:
        """Get all readers who have audio recordings for a specific surah."""
        return AudioRecordingRepository.get_readers_for_surah(surah_id)

    @staticmethod
    def get_all_available_readers() -> List[transports.ReaderInfo]:
        """Get all readers who have audio recordings in the system."""
        return AudioRecordingRepository.get_all_available_readers()


class LanguageService:
    """Service for language operations."""

    @staticmethod
    def get_available_languages() -> List[transports.LanguageInfo]:
        """Get all available languages with translation and transliteration support."""
        return LanguageRepository.get_available_languages()

    @staticmethod
    def get_language_from_header(request) -> str:
        """Extract language preference from request headers."""
        # Get language from Language-Code header
        language = request.headers.get("Language-Code")

        # Default to English if no header provided
        return language or "en"

    @staticmethod
    def get_ayah_text_with_language(
        surah_id: int, ayah_id: int, requested_language: str
    ) -> Tuple[transports.AyahText, LanguageMetadata]:
        """Get ayah text with language selection and fallback logic."""
        try:
            ayah = Ayah.objects.get(surah__surah_number=surah_id, ayah_number=ayah_id)
        except Ayah.DoesNotExist:
            raise NotFound(f"Ayah {ayah_id} in Surah {surah_id} not found")

        # Always get Arabic text
        arabic_text = LanguageRepository.get_ayah_translation_by_locale(ayah.id, "ar")

        # Try to get translation in requested language
        translation = LanguageRepository.get_ayah_translation_by_locale(ayah.id, requested_language)
        used_language = requested_language
        fallback_applied = False

        # Fallback to English if requested language not available
        if translation is None and requested_language != "en":
            translation = LanguageRepository.get_ayah_translation_by_locale(ayah.id, "en")
            if translation is not None:
                used_language = "en"
                fallback_applied = True

        # If still no translation, set to None
        if translation is None:
            used_language = "en"  # Default to en even if not available
            fallback_applied = True

        # Try to get transliteration in requested language
        transliteration = LanguageRepository.get_ayah_transliteration_by_locale(ayah.id, requested_language)

        # Fallback to English transliteration if requested language not available
        if transliteration is None and requested_language != "en":
            transliteration = LanguageRepository.get_ayah_transliteration_by_locale(ayah.id, "en")
            if transliteration is not None and not fallback_applied:
                used_language = "en"
                fallback_applied = True

        # Create language metadata
        language_metadata = LanguageMetadata(
            requested_language=requested_language, used_language=used_language, fallback_applied=fallback_applied
        )

        # Create ayah text transport
        ayah_text = transports.AyahText(
            surah_id=surah_id,
            ayah_id=ayah_id,
            arabic_text=arabic_text or "",
            transliteration=transliteration or "",
            translation=translation or "",
        )

        return ayah_text, language_metadata

    @staticmethod
    def get_surah_ayahs_with_language(
        surah_id: int, requested_language: str
    ) -> Tuple[List[transports.AyahText], LanguageMetadata]:
        """Get all ayahs for a surah with language selection and fallback logic."""
        ayahs = AyahRepository.get_ayahs_by_surah(str(surah_id))

        if not ayahs:
            return [], LanguageMetadata(
                requested_language=requested_language, used_language="en", fallback_applied=False
            )

        ayah_texts = []
        used_language = requested_language
        fallback_applied = False

        for ayah in ayahs:
            ayah_obj = Ayah.objects.get(surah__surah_number=surah_id, ayah_number=ayah.ayah_number)

            # Always get Arabic text
            arabic_text = LanguageRepository.get_ayah_translation_by_locale(ayah_obj.id, "ar")

            # Try to get translation in requested language
            translation = LanguageRepository.get_ayah_translation_by_locale(ayah_obj.id, requested_language)

            # Fallback to English if requested language not available
            if translation is None and requested_language != "en":
                translation = LanguageRepository.get_ayah_translation_by_locale(ayah_obj.id, "en")
                if translation is not None:
                    used_language = "en"
                    fallback_applied = True

            # Try to get transliteration in requested language
            transliteration = LanguageRepository.get_ayah_transliteration_by_locale(ayah_obj.id, requested_language)

            # Fallback to English transliteration if requested language not available
            if transliteration is None and requested_language != "en":
                transliteration = LanguageRepository.get_ayah_transliteration_by_locale(ayah_obj.id, "en")
                if transliteration is not None and not fallback_applied:
                    used_language = "en"
                    fallback_applied = True

            ayah_texts.append(
                transports.AyahText(
                    surah_id=ayah.surah_id,
                    ayah_id=ayah.ayah_number,
                    arabic_text=arabic_text or "",
                    transliteration=transliteration or "",
                    translation=translation or "",
                )
            )

        # Create language metadata
        language_metadata = LanguageMetadata(
            requested_language=requested_language, used_language=used_language, fallback_applied=fallback_applied
        )

        return ayah_texts, language_metadata

    @staticmethod
    def get_surah_title_with_language(surah_id: int, requested_language: str) -> Tuple[str, LanguageMetadata]:
        """Get surah title with language selection and fallback logic."""
        try:
            surah = Surah.objects.get(surah_number=surah_id)
        except Surah.DoesNotExist:
            raise NotFound(f"Surah with ID {surah_id} not found")

        # Try to get title in requested language
        title = LanguageRepository.get_surah_title_by_locale(surah_id, requested_language)
        used_language = requested_language
        fallback_applied = False

        # Fallback to English if requested language not available
        if title is None and requested_language != "en":
            title = LanguageRepository.get_surah_title_by_locale(surah_id, "en")
            if title is not None:
                used_language = "en"
                fallback_applied = True

        # Create language metadata
        language_metadata = LanguageMetadata(
            requested_language=requested_language, used_language=used_language, fallback_applied=fallback_applied
        )

        return title, language_metadata


class ArabicLetterService:
    """Service for Arabic letter operations."""

    @staticmethod
    def get_all_letters() -> List[transports.ArabicLetter]:
        """Get all Arabic letters."""
        return ArabicLetterRepository.get_all_letters()

    @staticmethod
    def get_letter_by_id(letter_id: int) -> transports.ArabicLetter:
        """Get Arabic letter by ID."""
        letter = ArabicLetterRepository.get_letter_by_id(letter_id)
        if not letter:
            raise NotFound(f"Arabic letter with ID {letter_id} not found")
        return letter

    @staticmethod
    def get_letters_with_audio() -> List[transports.ArabicLetter]:
        """Get all Arabic letters that have audio pronunciation."""
        return ArabicLetterRepository.get_letters_with_audio()

    @staticmethod
    def has_audio_pronunciation(letter_id: int) -> bool:
        """Check if an Arabic letter has audio pronunciation."""
        letter = ArabicLetterRepository.get_letter_by_id(letter_id)
        return letter is not None and letter.audio_url is not None and letter.audio_url.strip() != ""

import React, { useState } from "react";
import useLanguageStore from "src/hooks/languageStore";

const LanguageSwitcher = () => {
  const { language, setLanguage } = useLanguageStore();
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = (lang) => {
    setLanguage(lang);
    setIsOpen(false);
  };

  // Стили для компонентов
  const styles = {
    button: {
      position: "relative",
      display: "inline-block",
      padding: "10px 20px",
      //   backgroundColor: "#007bff",
      backgroundColor: "#78787878",
      color: "#fff",
      border: "none",
      borderRadius: "25px",
      cursor: "pointer",
      fontSize: "16px",
      fontWeight: "bold",
      textTransform: "uppercase",
      boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
      transition: "background-color 0.3s ease, transform 0.2s ease",
    },
    buttonHover: {
      backgroundColor: "#a5dea0",
    },
    dropdown: {
      position: "absolute",
      top: "50px",
      left: "0",
      right: "0",
      zIndex: 1000,
      backgroundColor: "#fff",
      boxShadow: "0 8px 16px rgba(0, 0, 0, 0.2)",
      borderRadius: "8px",
      overflow: "hidden",
      transform: isOpen ? "scale(1)" : "scale(0.95)",
      transformOrigin: "top",
      opacity: isOpen ? 1 : 0,
      pointerEvents: isOpen ? "auto" : "none",
      transition: "opacity 0.3s ease, transform 0.3s ease",
    },
    dropdownItem: {
      padding: "10px 20px",
      fontSize: "14px",
      color: "#333",
      cursor: "pointer",
      transition: "background-color 0.2s ease",
    },
    dropdownItemHover: {
      backgroundColor: "#f1f1f1",
    },
  };

  return (
    <div
      style={{
        position: "relative",
        display: "inline-block",
        marginRight: "10px",
      }}
    >
      <button
        style={{
          ...styles.button,
          ...(isOpen ? styles.buttonHover : {}),
        }}
        onClick={() => setIsOpen(!isOpen)}
      >
        {language.toUpperCase()} ▼
      </button>
      <div style={styles.dropdown}>
        {["en", "ru"].map((lang) => (
          <div
            key={lang}
            style={styles.dropdownItem}
            onClick={() => handleLanguageChange(lang)}
            onMouseEnter={(e) => (e.target.style.backgroundColor = "#f1f1f1")}
            onMouseLeave={(e) =>
              (e.target.style.backgroundColor = "transparent")
            }
          >
            {lang.toUpperCase()}
          </div>
        ))}
      </div>
    </div>
  );
};

export default LanguageSwitcher;

from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from quran.transports import Ayah, Surah, ArabicLetter



@dataclass(frozen=True)
class UserAyahRelation:
    """User-Ayah relationship information."""

    ayah: Ayah
    ayah_id: int
    user_id: int
    is_completed: bool
    is_bookmarked: bool
    created_at: datetime


@dataclass(frozen=True)
class UserSurahRelation:
    """User-Surah relationship information."""

    user_id: int
    surah: Surah
    experience_points: int
    is_completed: bool
    is_bookmarked: bool
    is_skipped: bool
    created_at: datetime


@dataclass(frozen=True)
class SurahGameCompletion:
    """Surah game completion information."""

    id: int
    user_id: int
    game_type: str
    progress_ayah_count: int
    new_ayahs: int
    created_at: datetime


@dataclass(frozen=True)
class UserArabicProgress:
    """User Arabic progress information."""

    id: int
    progress_status: str
    user_id: int
    arabic: ArabicLetter
    created_at: datetime


@dataclass(frozen=True)
class SurahProgress:
    """Progress information for a specific Surah."""

    surah_id: int
    completed_ayahs: int
    total_ayahs: int
    experience_points: int
    completion_percentage: float


@dataclass(frozen=True)
class EnhancedBookmark:
    """Enhanced bookmark information with multi-language content."""

    user_id: int
    ayah: Ayah
    surah_title: Optional[str]
    arabic_text: Optional[str]
    translation: Optional[str]
    transliteration: Optional[str]
    image_url: Optional[str]
    is_completed: bool
    is_bookmarked: bool
    created_at: datetime


@dataclass(frozen=True)
class ArabicLetterProgressStats:
    """Arabic letter progress statistics."""

    total_available_letters: int
    total_tracked_letters: int
    new_letters: int
    in_progress_letters: int
    completed_letters: int
    completion_percentage: float


@dataclass(frozen=True)
class EnhancedUserArabicProgress:
    """Enhanced user Arabic progress with letter details."""

    id: int
    progress_status: str
    user_id: int
    arabic: ArabicLetter
    created_at: datetime
    has_audio: bool
    practice_sessions: int

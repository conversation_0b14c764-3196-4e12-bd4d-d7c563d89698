import LeftMenuPanel from "src/components/LeftMenuPanel/LeftMenuPanel";
import "src/pages/Games/Games.css";
import ProfileNav from "src/components/ProfileNav";
import LearnCard from "src/components/LearnCard/LearnCard";
import GameCards from "src/components/GameCards/GameCards";
import { useParams } from "react-router-dom";
import { useState, useEffect, useRef } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import LoadingSpinner from "src/components/LoadingSpinner";
import arrowBackGame from "src/assets/arrow_back_game.svg";
import axiosInstance from "src/utils/axios";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useTranslation } from "react-i18next";

function Games() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { id, ayahId } = useParams();
  const [maxAyahId, setMaxAyahId] = useState(0);
  const [activeComponent, setActiveComponent] = useState("learn");
  const [activeGame, setActiveGame] = useState(null);
  const [ayahIdInt, setAyahIdInt] = useState(parseInt(ayahId) || 1);
  const [selectedAyah, setSelectedAyah] = useState(ayahIdInt);
  const [surahName, setSurahName] = useState("");
  // const [ayahProgress, setAyahProgress] = useState(0);
  const ayahProgressRef = useRef(0);

  useEffect(() => {
    const parsedAyahId = parseInt(ayahId) || 1;
    setAyahIdInt(parsedAyahId);
    setSelectedAyah(parsedAyahId);

    axios
      .get(`https://api.quran.com/api/v4/chapters/${id}`)
      .then((response) => {
        const versesCount = response.data.chapter.verses_count;
        setMaxAyahId(versesCount);
        setSurahName(response.data.chapter.name_simple);
        if (parsedAyahId > versesCount) {
          navigate(`/games/${id}/${versesCount}`);
        } else if (parsedAyahId < 1) {
          navigate(`/games/${id}/1`);
        }
      })
      .catch((error) => console.log(error));
    setLoading(false);
  }, [id, ayahId, navigate]);

  useEffect(() => {
    const fetchPgData = async () => {
      setLoading(true);
      try {
        const response = await axiosInstance.post(
          "/auth/profile/progress_info/",
          {
            surah_id: id,
            ayah_id: ayahId,
          }
        );
        ayahProgressRef.current = Math.min(
          response.data.ayah_progress.progress,
          100
        );
        setLoading(false);
      } catch (error) {
        toast.error("Error fetching game data", error);
        setLoading(false);
      }
    };

    fetchPgData();
  }, [id, ayahId]);

  const updateProgress = async () => {
    setTimeout(async () => {
      // setLoading(true);
      try {
        const response = await axiosInstance.post("/auth/profile/progress/", {
          surah_id: id,
          ayah_id: ayahId,
        });
        ayahProgressRef.current = Math.min(
          response.data.ayah_progress.progress,
          100
        );
        const progressBarElement = document.querySelector(".done-pg-bar");
        if (progressBarElement) {
          progressBarElement.style.width = `${ayahProgressRef.current}%`;
        }
        // setLoading(false);
      } catch (error) {
        toast.error("Error updating progress", error);
        // setLoading(false);
      }
      // setActiveComponent("learn");
      // setActiveGame(null);
      // requestAnimationFrame(() => {
      //   window.scrollTo({
      //     top: document.body.scrollHeight,
      //     behavior: "smooth",
      //   });
      // });
    }, 500);
  };

  const nextAyah = () => {
    if (ayahIdInt < maxAyahId) {
      navigate(`/games/${id}/${ayahIdInt + 1}`);
    } else if (parseInt(id) === 103) {
      navigate(`/games/107/1`);
    } else if (parseInt(id) === 107) {
      navigate(`/games/108/1`);
    } else if (parseInt(id) === 108) {
      navigate(`/games/110/1`);
    } else if (parseInt(id) < 114) {
      navigate(`/games/${parseInt(id) + 1}/1`);
    }
  };

  const prevAyah = () => {
    if (ayahIdInt > 1) {
      navigate(`/games/${id}/${ayahIdInt - 1}`);
    } else if (parseInt(id) === 110) {
      navigate(`/games/108/1`);
    } else if (parseInt(id) === 108) {
      navigate(`/games/107/1`);
    } else if (parseInt(id) === 107) {
      navigate(`/games/103/1`);
    } else if (parseInt(id) > 1) {
      navigate(`/games/${parseInt(id) - 1}/${maxAyahId}`);
    }
  };

  const startGame = (gameType) => {
    setActiveGame(gameType);
    setActiveComponent("game");
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const backToLearn = () => {
    setActiveComponent("learn");
    setActiveGame(null);
    requestAnimationFrame(() => {
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: "smooth",
      });
    });
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  // Определяем описание для каждой игры
  const gameDescriptions = {
    shuffling: t("shuffling_description"),
    memory: t("memory_description"),
    audio: t("audio_description"),
    matching: t("matching_description"),
  };

  return (
    <section
      className={`grid-container ${
        activeComponent === "game" ? "game-mode" : ""
      }`}
    >
      <ToastContainer
        position="top-center"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />
      <div className="left-menu-games">
        <LeftMenuPanel activeMenu="study" />
      </div>
      <div className="unmovable-header">
        <div className="top-main-container">
          <ProfileNav marginNeeded={false} />
        </div>
        <a className="title-pg">{surahName}</a>

        <div className="main-pg-bar">
          <div
            className="done-pg-bar"
            style={{ width: `${ayahProgressRef.current}%` }}
          ></div>
        </div>

        {activeComponent === "game" && (
          <>
            <div className="back-to-learn-btn-area">
              <button className="back-to-learn-btn" onClick={backToLearn}>
                <img src={arrowBackGame} alt="Back to Learn" />
              </button>
              <a className="game-name">{t(`${activeGame}`)}</a>
            </div>
            <p className="game-description">
              {gameDescriptions[activeGame] || t("game_description_not_found")}
            </p>
          </>
        )}
      </div>
      <div className="right-content">
        {activeComponent === "learn" ? (
          <LearnCard
            nextAyah={nextAyah}
            prevAyah={prevAyah}
            id={id}
            ayahId={ayahIdInt}
          />
        ) : (
          <GameCards
            activeGame={activeGame}
            startGame={startGame}
            backToLearn={backToLearn}
            maxAyahId={maxAyahId}
            selectedAyah={selectedAyah}
            setSelectedAyah={setSelectedAyah}
            updateProgress={updateProgress}
          />
        )}
      </div>
      {activeComponent === "learn" && (
        <div id="games" className="games">
          <div className="games-flex-container">
            <GameCards
              activeGame={activeGame}
              startGame={startGame}
              maxAyahId={maxAyahId}
              selectedAyah={selectedAyah}
              setSelectedAyah={setSelectedAyah}
              updateProgress={updateProgress}
            />
          </div>
        </div>
      )}
    </section>
  );
}

export default Games;

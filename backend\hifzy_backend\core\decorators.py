"""
Custom decorators for the hifzy_backend project.

This module provides reusable decorators for common functionality
like caching, logging, validation, and performance monitoring.
"""

from functools import wraps
from typing import Callable, Any, Optional
from django.core.cache import cache
from django.conf import settings
import time
import logging
from rest_framework.response import Response
from rest_framework import status

from .utils import generate_cache_key, create_error_response
from .exceptions import handle_service_errors

logger = logging.getLogger(__name__)


def cache_result(timeout: int = 300, key_prefix: str = None):
    """
    Decorator to cache function results.
    
    Args:
        timeout: Cache timeout in seconds (default: 5 minutes)
        key_prefix: Optional prefix for cache key
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key_parts = [key_prefix or func.__name__]
            cache_key_parts.extend([str(arg) for arg in args])
            cache_key_parts.extend([f"{k}:{v}" for k, v in sorted(kwargs.items())])
            
            cache_key = generate_cache_key(*cache_key_parts)
            
            # Try to get from cache
            result = cache.get(cache_key)
            if result is not None:
                logger.debug(f"Cache hit for {func.__name__}: {cache_key}")
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            logger.debug(f"Cache set for {func.__name__}: {cache_key}")
            
            return result
        return wrapper
    return decorator


def log_execution_time(func: Callable) -> Callable:
    """
    Decorator to log function execution time.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        execution_time = time.time() - start_time
        
        logger.info(f"{func.__name__} executed in {execution_time:.4f} seconds")
        return result
    return wrapper


def require_authentication(func: Callable) -> Callable:
    """
    Decorator to ensure user is authenticated for view methods.
    """
    @wraps(func)
    def wrapper(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return Response(
                create_error_response("Authentication required"),
                status=status.HTTP_401_UNAUTHORIZED
            )
        return func(self, request, *args, **kwargs)
    return wrapper


def validate_request_data(serializer_class):
    """
    Decorator to validate request data using a serializer.
    
    Args:
        serializer_class: DRF serializer class to use for validation
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            serializer = serializer_class(data=request.data)
            if not serializer.is_valid():
                return Response(
                    create_error_response("Invalid data", serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Add validated data to kwargs
            kwargs['validated_data'] = serializer.validated_data
            return func(self, request, *args, **kwargs)
        return wrapper
    return decorator


def rate_limit(max_requests: int = 100, window_seconds: int = 3600):
    """
    Simple rate limiting decorator.
    
    Args:
        max_requests: Maximum number of requests allowed
        window_seconds: Time window in seconds
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            # Get client identifier (IP address)
            client_ip = request.META.get('REMOTE_ADDR', 'unknown')
            cache_key = f"rate_limit:{func.__name__}:{client_ip}"
            
            # Get current request count
            current_requests = cache.get(cache_key, 0)
            
            if current_requests >= max_requests:
                return Response(
                    create_error_response("Rate limit exceeded"),
                    status=status.HTTP_429_TOO_MANY_REQUESTS
                )
            
            # Increment request count
            cache.set(cache_key, current_requests + 1, window_seconds)
            
            return func(self, request, *args, **kwargs)
        return wrapper
    return decorator


def handle_exceptions(func: Callable) -> Callable:
    """
    Decorator to handle exceptions in view methods.
    """
    @wraps(func)
    def wrapper(self, request, *args, **kwargs):
        try:
            return func(self, request, *args, **kwargs)
        except Exception as e:
            logger.error(f"Unhandled exception in {func.__name__}: {e}", exc_info=True)
            return Response(
                create_error_response("An unexpected error occurred"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    return wrapper


def transaction_atomic(func: Callable) -> Callable:
    """
    Decorator to wrap function in database transaction.
    """
    from django.db import transaction
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        with transaction.atomic():
            return func(*args, **kwargs)
    return wrapper


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator to retry function on failure.
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff: Multiplier for delay on each retry
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            current_delay = delay
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(
                            f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                            f"Retrying in {current_delay} seconds..."
                        )
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
            
            # Re-raise the last exception if all retries failed
            raise last_exception
        return wrapper
    return decorator


def deprecated(reason: str = None):
    """
    Decorator to mark functions as deprecated.
    
    Args:
        reason: Optional reason for deprecation
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            warning_msg = f"{func.__name__} is deprecated"
            if reason:
                warning_msg += f": {reason}"
            
            logger.warning(warning_msg)
            return func(*args, **kwargs)
        return wrapper
    return decorator


def monitor_performance(threshold_seconds: float = 1.0):
    """
    Decorator to monitor function performance and log slow executions.
    
    Args:
        threshold_seconds: Threshold for logging slow executions
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            if execution_time > threshold_seconds:
                logger.warning(
                    f"Slow execution detected: {func.__name__} took {execution_time:.4f} seconds"
                )
            
            return result
        return wrapper
    return decorator


def validate_user_permissions(required_permissions: list = None):
    """
    Decorator to validate user permissions.
    
    Args:
        required_permissions: List of required permission strings
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            if not request.user.is_authenticated:
                return Response(
                    create_error_response("Authentication required"),
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            if required_permissions:
                if not request.user.has_perms(required_permissions):
                    return Response(
                        create_error_response("Insufficient permissions"),
                        status=status.HTTP_403_FORBIDDEN
                    )
            
            return func(self, request, *args, **kwargs)
        return wrapper
    return decorator

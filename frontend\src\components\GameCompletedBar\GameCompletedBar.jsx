import { useTranslation } from "react-i18next";
const GameCompletedBar = ({ result, backToLearn }) => {
  const { t } = useTranslation();
  return (
    <div className={`result-container ${result ? "correct" : "incorrect"}`}>
      <p>{result ? t("game_completed") : t("try_again")}</p>
      <button
        style={{ backgroundColor: result ? "#A5DEA0" : "#E27878" }}
        onClick={backToLearn}
      >
        {t("back_to_games")}
      </button>
    </div>
  );
};

export default GameCompletedBar;

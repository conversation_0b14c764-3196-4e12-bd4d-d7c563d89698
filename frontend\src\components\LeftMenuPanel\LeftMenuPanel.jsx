import React from "react";
import Logo from "src/components/Logo";
import homeSvg from "src/assets/home.svg";
import studySvg from "src/assets/study.svg";
import leaderboardsSvg from "src/assets/lb.svg";
import makhrajSvg from "src/assets/makhraj.svg";
import karaokeSvg from "src/assets/karaoke.svg";
import questionsSvg from "src/assets/questions.svg";
import moreSvg from "src/assets/more.svg";
import "src/components/LeftMenuPanel/LeftMenuPanel.css";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";

const LeftMenuPanel = ({ activeMenu }) => {
  const { t } = useTranslation();
  return (
    <div className="horizontal-left-menu-container">
      <div className="left-panel-flex">
        <div className="logo-flex">
          <Logo />
        </div>
        <div className="left-panel-buttons">
          <Link to="/">
            <div
              className={`left-panel-button-with-pic ${
                activeMenu === "home" ? "active-left-menu" : ""
              }`}
            >
              <img src={homeSvg} alt="Home" />
              <button>{t("home_left_menu")}</button>
            </div>
          </Link>
          <Link to="/levels">
            <div
              className={`left-panel-button-with-pic ${
                activeMenu === "study" ? "active-left-menu" : ""
              }`}
            >
              <img src={studySvg} alt="Study" />
              <button>{t("study_left_menu")}</button>
            </div>
          </Link>
          <div
            className={`left-panel-button-with-pic ${
              activeMenu === "leaderboards" ? "active-left-menu" : ""
            }`}
          >
            <img src={leaderboardsSvg} alt="Leaderboards" />
            <button>{t("leaderboards_left_menu")}</button>
          </div>
          <div
            className={`left-panel-button-with-pic ${
              activeMenu === "makhraj" ? "active-left-menu" : ""
            }`}
          >
            <img src={makhrajSvg} alt="Makhraj" />
            <button>{t("makhraj_left_menu")}</button>
          </div>
          <div
            className={`left-panel-button-with-pic ${
              activeMenu === "karaoke" ? "active-left-menu" : ""
            }`}
          >
            <img src={karaokeSvg} alt="Karaoke" />
            <button>{t("karaoke_left_menu")}</button>
          </div>
          <div
            className={`left-panel-button-with-pic ${
              activeMenu === "questions" ? "active-left-menu" : ""
            }`}
          >
            <img src={questionsSvg} alt="Questions" />
            <button>{t("questions_left_menu")}</button>
          </div>
          <div
            className={`left-panel-button-with-pic ${
              activeMenu === "more" ? "active-left-menu" : ""
            }`}
          >
            <img src={moreSvg} alt="More" />
            <button>{t("more_left_menu")}</button>
          </div>
        </div>
      </div>
      <span className="border-line"></span>
    </div>
  );
};

export default LeftMenuPanel;
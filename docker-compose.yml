version: '3.8'

services:
  # PostgreSQL Database Service
  db:
    image: postgres:15-alpine
    container_name: hifzy_db
    environment:
      POSTGRES_DB: ${DB_NAME:-hifzy_db}
      POSTGRES_USER: ${DB_USER:-hifzyuser}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-hifzy_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-hifzyuser} -d ${DB_NAME:-hifzy_db}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django Application Service
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hifzy_web
    environment:
      - DJANGO_ENVIRONMENT=docker
      - DJANGO_SECRET_KEY=${DJANGO_SECRET_KEY:-django-insecure-change-me-in-production}
      - DB_NAME=${DB_NAME:-hifzy_db}
      - DB_USER=${DB_USER:-hifzyuser}
      - DB_PASSWORD=${DB_PASSWORD:-hifzy_password}
      - DB_HOST=db
      - DB_PORT=5432
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1}
    volumes:
      - ./backend:/app
      - static_volume:/app/hifzy_backend/staticfiles
      - media_volume:/app/hifzy_backend/media
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    command: >
      sh -c "cd /app/hifzy_backend &&
             python manage.py runserver 0.0.0.0:8000 --env=docker"

  # Nginx Reverse Proxy Service
  nginx:
    image: nginx:alpine
    container_name: hifzy_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - static_volume:/var/www/static:ro
      - media_volume:/var/www/media:ro
    depends_on:
      - web
    restart: unless-stopped

volumes:
  postgres_data:
  static_volume:
  media_volume:

networks:
  default:
    name: hifzy_network

from django.db import models
from core.settings.base import AUTH_USER_MODEL
from quran.models import <PERSON>ah, Ayah, ArabicLetter


class UserAyahMM(models.Model):
    """Model for user-ayah many-to-many relationship."""

    ayah = models.ForeignKey(Ayah, on_delete=models.CASCADE, related_name="user_relations")
    user = models.ForeignKey(AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="ayah_relations")
    is_completed = models.BooleanField(default=False, help_text="Whether the ayah is completed")
    is_bookmarked = models.BooleanField(default=False, help_text="Whether the ayah is bookmarked")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("ayah", "user")
        verbose_name = "User Ayah Relation"
        verbose_name_plural = "User Ayah Relations"
        ordering = ["ayah__surah_id", "ayah__ayah_number"]

    def __str__(self):
        return f"{self.user.username} - {self.ayah}"


class UserSurahMM(models.Model):
    """Model for user-surah many-to-many relationship."""

    user = models.ForeignKey(AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="surah_relations")
    surah = models.ForeignKey(Surah, on_delete=models.CASCADE, related_name="user_relations")
    experience_points = models.IntegerField(default=0, help_text="Experience points earned for this surah")
    is_completed = models.BooleanField(default=False, help_text="Whether the surah is completed")
    is_bookmarked = models.BooleanField(default=False, help_text="Whether the surah is bookmarked")
    is_skipped = models.BooleanField(
        default=False, help_text="Whether the surah was skipped in pedagogical progression"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("user", "surah")
        verbose_name = "User Surah Relation"
        verbose_name_plural = "User Surah Relations"
        ordering = ["surah__surah_number"]

    def __str__(self):
        return f"{self.user.username} - {self.surah}: {self.experience_points} XP"


class SurahGameCompletionMM(models.Model):
    """Model for tracking game completions per surah."""

    GAME_TYPES = (
        ("shuffling", "Shuffling Game"),
        ("matching", "Matching Game"),
        ("memory", "Memory Game"),
        ("audio", "Audio Game"),
    )

    surah = models.ForeignKey(Surah, on_delete=models.CASCADE, related_name="game_completions")
    user = models.ForeignKey(AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="surah_game_completions")
    game_type = models.CharField(max_length=20, choices=GAME_TYPES, help_text="Type of game completed")
    progress_ayah_count = models.IntegerField(default=0, help_text="Number of ayahs completed in this game")
    new_ayahs = models.IntegerField(default=0, help_text="Number of new ayahs completed in this game")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("surah", "user", "game_type", "progress_ayah_count")
        verbose_name = "Surah Game Completion"
        verbose_name_plural = "Surah Game Completions"
        ordering = ["surah__surah_number", "game_type"]

    def __str__(self):
        return f"{self.user.username} - {self.surah} - {self.get_game_type_display()}: {self.progress_ayah_count} ayahs"


class UserArabicMM(models.Model):
    """Model for user Arabic exercises progress."""

    PROGRESS_STATUS_CHOICES = (
        ("New", "New"),
        ("In progress", "In Progress"),
        ("Completed", "Completed"),
    )

    progress_status = models.CharField(max_length=20, choices=PROGRESS_STATUS_CHOICES, default="New")
    user = models.ForeignKey(AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="arabic_progress")
    arabic = models.ForeignKey(ArabicLetter, on_delete=models.CASCADE, related_name="user_progress")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("user", "arabic")
        verbose_name = "User Arabic Progress"
        verbose_name_plural = "User Arabic Progress"

    def __str__(self):
        return f"{self.user.username} - {self.arabic}: {self.progress_status}"

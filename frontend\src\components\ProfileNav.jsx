import { React, useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import arrowDownSvg from "src/assets/arrow_down.svg";
import LoadingSpinner from "src/components/LoadingSpinner";
import axiosInstance from "src/utils/axios";
import Logout from "src/components/Logout";
import "react-toastify/dist/ReactToastify.css";
import { BACKEND_URL } from "src/utils/settings";
import { toast, ToastContainer } from "react-toastify";
import LanguageSwitcher from "src/components/LanguageSwitch";
import { useTranslation } from "react-i18next";
function ProfileNav({ marginNeeded }) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [profileUrl, setProfileUrl] = useState("");
  const dropdownRef = useRef(null);

  // Reusable style variables
  const containerStyle = {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
  };

  const profileContainerStyle = {
    fontSize: "20px",
    fontWeight: "600",
    display: "flex",
    alignItems: "center",
    padding: "10px",
    backgroundColor: "#f0f0f0",
    borderRadius: "50%",
    transitionDuration: "0.2s",
    cursor: "pointer",
    maxWidth: "fit-content",
    marginTop: "10px",
    position: "relative",
  };

  const profilePicStyle = {
    width: "40px",
    height: "40px",
    borderRadius: "50%",
    objectFit: "cover",
  };

  const dropdownMenuStyle = {
    position: "absolute",
    top: "80px",
    right: "0",
    backgroundColor: "#fff",
    borderRadius: "12px",
    boxShadow: "0 8px 24px rgba(0, 0, 0, 0.15)",
    zIndex: 1000,
    width: "200px",
    padding: "15px 0",
    backdropFilter: "blur(10px)",
    transition: "opacity 0.3s ease, transform 0.3s ease",
    opacity: 1,
    transform: "translateY(0)",
  };

  const dropdownItemStyle = {
    display: "flex",
    padding: "12px 20px",
    textDecoration: "none",
    color: "#333",
    fontSize: "16px",
    fontWeight: "500",
    cursor: "pointer",
    transition: "background 0.3s",
  };

  const languageSwitcherStyle = {
    fontSize: "16px",
    fontWeight: "600",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#f0f0f0",
    borderRadius: "50%",
    width: "40px",
    height: "40px",
    cursor: "pointer",
    marginRight: "10px",
    position: "relative",
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
    transition: "background-color 0.3s ease",
  };

  const languageDropdownStyle = {
    ...dropdownMenuStyle,
    top: "50px",
    left: "0",
    transformOrigin: "top left",
  };

  const languageDropdownItemStyle = {
    ...dropdownItemStyle,
    textAlign: "center",
  };

  useEffect(() => {
    const fetchProfileInfo = async () => {
      try {
        const response = await axiosInstance.get("/auth/profile/info/photo/");
        const { url } = response.data;
        setProfileUrl(url);
      } catch (error) {
        console.error("Error fetching profile photo", error);
        toast.error("Failed to fetch profile photo information.");
      } finally {
        setLoading(false);
      }
    };

    fetchProfileInfo();

    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <>
      <ToastContainer
        position="top-center"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />
      <div
        className="profile-nav-block"
        style={{
          width: "100%",
          position: "relative",
          marginTop: marginNeeded ? "-40px" : "0px",
          marginBottom: marginNeeded ? "0px" : "-40px",
        }}
      >
        <div style={containerStyle}>
          {/* Language Switcher */}
          <LanguageSwitcher
            dropdownStyle={languageDropdownStyle}
            itemStyle={languageDropdownItemStyle}
            buttonStyle={languageSwitcherStyle}
          />
          {/* Profile Container */}
          <div
            className="profile-container"
            style={profileContainerStyle}
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          >
            <img
              src={BACKEND_URL + profileUrl}
              className="profile-pic"
              alt="profile pic"
              style={profilePicStyle}
            />
          </div>
        </div>

        {/* Profile Dropdown */}
        {isDropdownOpen && (
          <div
            ref={dropdownRef}
            className="profile-dropdown"
            style={dropdownMenuStyle}
          >
            <Link
              to="/profile"
              style={dropdownItemStyle}
              onMouseEnter={(e) =>
                (e.currentTarget.style.backgroundColor = "#f9f9f9")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.backgroundColor = "transparent")
              }
            >
              {t("profile_btn")}
            </Link>
            <hr
              style={{
                margin: "10px 0",
                border: "none",
                borderTop: "1px solid #ddd",
              }}
            />
            <div
              style={dropdownItemStyle}
              onMouseEnter={(e) =>
                (e.currentTarget.style.backgroundColor = "#f9f9f9")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.backgroundColor = "transparent")
              }
            >
              <Logout />
            </div>
          </div>
        )}
      </div>
    </>
  );
}

export default ProfileNav;

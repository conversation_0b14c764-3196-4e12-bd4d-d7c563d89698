"""
Response schema serializers for the social app.

This module provides response serializers for all social-related endpoints
to ensure comprehensive API documentation in Swagger/OpenAPI.
"""

from rest_framework import serializers
from core.response_schemas import (
    BaseSuccessResponseSerializer,
    BaseErrorResponseSerializer,
    UserProfileSerializer,
    FollowRelationshipSerializer,
    LeaderboardEntrySerializer,
    CountResponseSerializer,
)


# Follow/Unfollow response schemas
class FollowUserSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful user follow operation."""
    
    data = FollowRelationshipSerializer(help_text="Follow relationship information")


class UnfollowUserSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful user unfollow operation."""
    
    # No additional data field needed, just success message
    pass


# Followers/Following list response schemas
class FollowersListDataSerializer(serializers.Serializer):
    """Data structure for followers list response."""
    
    followers = FollowRelationshipSerializer(
        many=True,
        help_text="List of users following the current user"
    )
    count = serializers.IntegerField(help_text="Number of followers")


class FollowersListSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful followers list retrieval."""
    
    data = FollowersListDataSerializer(help_text="Followers list data")


class FollowingListDataSerializer(serializers.Serializer):
    """Data structure for following list response."""
    
    following = FollowRelationshipSerializer(
        many=True,
        help_text="List of users that the current user is following"
    )
    count = serializers.IntegerField(help_text="Number of users being followed")


class FollowingListSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful following list retrieval."""
    
    data = FollowingListDataSerializer(help_text="Following list data")


# Follow status check response schemas
class FollowStatusDataSerializer(serializers.Serializer):
    """Data structure for follow status response."""
    
    username = serializers.CharField(help_text="Username that was checked")
    is_following = serializers.BooleanField(help_text="Whether the current user is following this user")


class FollowStatusSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful follow status check."""
    
    data = FollowStatusDataSerializer(help_text="Follow status data")


# Leaderboard response schemas
class LeaderboardDataSerializer(serializers.Serializer):
    """Data structure for leaderboard response."""
    
    leaderboard = LeaderboardEntrySerializer(
        many=True,
        help_text="List of users in the leaderboard"
    )
    user_rank = serializers.IntegerField(
        allow_null=True,
        help_text="Current user's rank in the leaderboard"
    )


class GlobalLeaderboardSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful global leaderboard retrieval."""
    
    data = LeaderboardDataSerializer(help_text="Global leaderboard data")


class FollowedUsersLeaderboardSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful followed users leaderboard retrieval."""
    
    data = LeaderboardDataSerializer(help_text="Followed users leaderboard data")


# Error response schemas
class SocialValidationErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for social validation errors."""
    pass


class SocialNotFoundErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for social not found errors."""
    pass


class SocialConflictErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for social conflict errors (e.g., already following)."""
    pass


class SocialAuthenticationErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for social authentication errors."""
    pass


class SocialInternalServerErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for social internal server errors."""
    pass


# Endpoint-specific response schema collections
class FollowUserResponseSchemas:
    """Response schemas for follow user endpoint."""
    
    success_201 = FollowUserSuccessResponseSerializer
    error_400 = SocialValidationErrorResponseSerializer
    error_401 = SocialAuthenticationErrorResponseSerializer
    error_404 = SocialNotFoundErrorResponseSerializer
    error_409 = SocialConflictErrorResponseSerializer
    error_500 = SocialInternalServerErrorResponseSerializer


class UnfollowUserResponseSchemas:
    """Response schemas for unfollow user endpoint."""
    
    success_200 = UnfollowUserSuccessResponseSerializer
    error_400 = SocialValidationErrorResponseSerializer
    error_401 = SocialAuthenticationErrorResponseSerializer
    error_404 = SocialNotFoundErrorResponseSerializer
    error_500 = SocialInternalServerErrorResponseSerializer


class FollowersListResponseSchemas:
    """Response schemas for followers list endpoint."""
    
    success_200 = FollowersListSuccessResponseSerializer
    error_401 = SocialAuthenticationErrorResponseSerializer
    error_500 = SocialInternalServerErrorResponseSerializer


class FollowingListResponseSchemas:
    """Response schemas for following list endpoint."""
    
    success_200 = FollowingListSuccessResponseSerializer
    error_401 = SocialAuthenticationErrorResponseSerializer
    error_500 = SocialInternalServerErrorResponseSerializer


class FollowStatusResponseSchemas:
    """Response schemas for follow status check endpoint."""
    
    success_200 = FollowStatusSuccessResponseSerializer
    error_400 = SocialValidationErrorResponseSerializer
    error_401 = SocialAuthenticationErrorResponseSerializer
    error_404 = SocialNotFoundErrorResponseSerializer
    error_500 = SocialInternalServerErrorResponseSerializer


class GlobalLeaderboardResponseSchemas:
    """Response schemas for global leaderboard endpoint."""
    
    success_200 = GlobalLeaderboardSuccessResponseSerializer
    error_401 = SocialAuthenticationErrorResponseSerializer
    error_500 = SocialInternalServerErrorResponseSerializer


class FollowedUsersLeaderboardResponseSchemas:
    """Response schemas for followed users leaderboard endpoint."""
    
    success_200 = FollowedUsersLeaderboardSuccessResponseSerializer
    error_401 = SocialAuthenticationErrorResponseSerializer
    error_500 = SocialInternalServerErrorResponseSerializer


# Example response data for documentation
class FollowUserExampleData:
    """Example response data for follow user endpoint."""
    
    @staticmethod
    def get_example():
        return {
            "success": True,
            "message": "You are now following john_doe",
            "data": {
                "id": 123,
                "follower": {
                    "id": 1,
                    "username": "current_user",
                    "first_name": "Current",
                    "last_name": "User",
                    "total_xp": 1500,
                    "current_level": 2,
                    "day_streak": 5,
                    "gender": "female",
                    "profile_photo": "https://example.com/photos/current_user.jpg",
                },
                "following": {
                    "id": 2,
                    "username": "john_doe",
                    "first_name": "John",
                    "last_name": "Doe",
                    "total_xp": 2500,
                    "current_level": 3,
                    "day_streak": 10,
                    "gender": "male",
                    "profile_photo": "https://example.com/photos/john_doe.jpg",
                },
                "created_at": "2024-01-15T10:30:00Z",
            },
        }


class LeaderboardExampleData:
    """Example response data for leaderboard endpoints."""
    
    @staticmethod
    def get_example():
        return {
            "success": True,
            "message": "Global leaderboard retrieved successfully",
            "data": {
                "leaderboard": [
                    {
                        "user": {
                            "id": 1,
                            "username": "top_user",
                            "first_name": "Top",
                            "last_name": "User",
                            "total_xp": 5000,
                            "current_level": 5,
                            "day_streak": 30,
                            "gender": "male",
                            "profile_photo": "https://example.com/photos/top_user.jpg",
                        },
                        "rank": 1,
                        "total_xp": 5000,
                    },
                    {
                        "user": {
                            "id": 2,
                            "username": "second_user",
                            "first_name": "Second",
                            "last_name": "User",
                            "total_xp": 4500,
                            "current_level": 4,
                            "day_streak": 25,
                            "gender": "female",
                            "profile_photo": "https://example.com/photos/second_user.jpg",
                        },
                        "rank": 2,
                        "total_xp": 4500,
                    },
                ],
                "user_rank": 15,
            },
        }

import React, { useState, useEffect } from "react";
import mainProfile from "src/assets/main-profile.png";
import "src/components/ProfileBlock/ProfileBlock.css";
import axiosInstance from "src/utils/axios";
import { BACKEND_URL } from "src/utils/settings";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoadingSpinner from "src/components/LoadingSpinner";
import { useTranslation } from "react-i18next";

function ProfileBlock() {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [profileUrl, setProfileUrl] = useState("");
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingSurname, setIsEditingSurname] = useState(false);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    const fetchProfileInfo = async () => {
      try {
        const response = await axiosInstance.get("/auth/profile/info/");
        const { first_name, last_name } = response.data;
        setFirstName(first_name);
        setLastName(last_name);
      } catch (error) {
        console.error("Error fetching profile information", error);
        toast.error("Failed to fetch profile information.");
      }

      try {
        const response = await axiosInstance.get("/auth/profile/info/photo/");
        const { url } = response.data;
        setProfileUrl(url);
      } catch (error) {
        console.error("Error fetching profile photo", error);
        toast.error("Failed to fetch profile photo information.");
      }

      setLoading(false);
    };

    fetchProfileInfo();
  }, []);

  const handleNameChange = (event) => {
    setFirstName(event.target.value);
  };

  const handleSurnameChange = (event) => {
    setLastName(event.target.value);
  };

  const updateProfileInfo = async () => {
    try {
      await axiosInstance.put("/auth/profile/update/", {
        first_name: firstName,
        last_name: lastName,
      });
      toast.success("Profile information updated successfully!");
    } catch (error) {
      console.error("Error updating profile", error);
      toast.error("Failed to update profile information.");
    }
  };

  const saveName = () => {
    setIsEditingName(false);
    updateProfileInfo();
  };

  const cancelNameEdit = () => {
    setIsEditingName(false);
  };

  const saveSurname = () => {
    setIsEditingSurname(false);
    updateProfileInfo();
  };

  const cancelSurnameEdit = () => {
    setIsEditingSurname(false);
  };

  const handlePhotoUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append("profile_photo", file);

      setUploading(true);
      try {
        const response = await axiosInstance.put(
          "/auth/profile/update/photo/",
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        setProfileUrl(response.data.url);
        toast.success(response.data.message);
      } catch (error) {
        console.error("Error uploading profile photo", error);
        toast.error("Failed to upload profile photo.");
      } finally {
        setUploading(false);
      }
    }
  };

  if (loading || uploading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="my-profile-container">
      <ToastContainer
        position="top-center"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />
      <div className="horizontal-my-profile">
        {/* Profile Image */}
        <div className="profile-image-container">
          <img
            src={BACKEND_URL + profileUrl}
            alt="Profile"
            className="main-profile-img"
          />
          <button
            className="upload-photo-btn"
            onClick={() => document.getElementById("photoInput").click()}
          >
            {t("upload_text")}
          </button>
          <input
            type="file"
            id="photoInput"
            style={{ display: "none" }}
            accept="image/*"
            onChange={handlePhotoUpload}
          />
        </div>

        {/* Profile Info */}
        <div className="profile-info">
          <div className="top-profile-info">
            {/* Editable Name Field */}
            <div className="profile-field">
              {isEditingName ? (
                <>
                  <input
                    type="text"
                    value={firstName}
                    onChange={handleNameChange}
                    className="name-input"
                  />
                  <button className="save-btn" onClick={saveName}>
                    {t("save_text")}
                  </button>
                  <button className="cancel-btn" onClick={cancelNameEdit}>
                    {t("cancel_text")}
                  </button>
                </>
              ) : (
                <>
                  <a className="profile-title">{firstName}</a>
                  <button
                    className="edit-icon"
                    onClick={() => setIsEditingName(true)}
                  >
                    ✎
                  </button>
                </>
              )}
            </div>

            {/* Editable Surname Field */}
            <div className="profile-field">
              {isEditingSurname ? (
                <>
                  <input
                    type="text"
                    value={lastName}
                    onChange={handleSurnameChange}
                    className="surname-input"
                  />
                  <button className="save-btn" onClick={saveSurname}>
                    {t("save_text")}
                  </button>
                  <button className="cancel-btn" onClick={cancelSurnameEdit}>
                    {t("cancel_text")}
                  </button>
                </>
              ) : (
                <>
                  <a className="profile-title">{lastName}</a>
                  <button
                    className="edit-icon"
                    onClick={() => setIsEditingSurname(true)}
                  >
                    ✎
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Date and Follow Info */}
          <a className="date-profile">{t("joined_march_2024")}</a>
          <div className="follow-profile-info">
            <a className="following-profile">3 {t("profile_page_following")}</a>
            <a className="follower-profile">2 {t("profile_page_followers")}</a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfileBlock;

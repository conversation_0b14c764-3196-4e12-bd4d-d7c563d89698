from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass(frozen=True)
class Surah:
    """Surah information."""

    surah_id: int
    ayah_count: int
    xp_per_ayah: int
    is_pre_bismillah: bool
    is_meccan: bool
    is_medinian: bool
    created_at: datetime


@dataclass(frozen=True)
class SurahTranslation:
    """Surah translation information."""

    id: int
    surah_id: int
    locale: str
    title: str


@dataclass(frozen=True)
class Ayah:
    """Ayah information."""

    ayah_number: int
    surah_id: int
    image: Optional[str]


@dataclass(frozen=True)
class AyahTranslation:
    """Ayah translation information."""

    id: int
    ayah_id: int
    locale: str
    text: str
    description: Optional[str]


@dataclass(frozen=True)
class ArabicLetter:
    """Arabic letter information."""

    id: int
    title: str
    transcription: str
    audio_url: Optional[str] = None


@dataclass(frozen=True)
class AyahText:
    """Ayah text information."""

    surah_id: int
    ayah_id: int
    arabic_text: str
    transliteration: str
    translation: str


@dataclass(frozen=True)
class WordAudio:
    """Word audio information."""

    word: str
    audio_url: str


@dataclass(frozen=True)
class AudioRecording:
    """Audio recording information."""

    id: int
    ayah_id: int
    audio_url: str
    reader_name: str
    reader_slug: str
    audio_format: str
    duration_seconds: Optional[int]
    file_size_bytes: Optional[int]
    created_at: datetime
    updated_at: datetime


@dataclass(frozen=True)
class ReaderInfo:
    """Information about an audio reader/reciter."""

    reader_name: str
    reader_slug: str
    recording_count: int


@dataclass(frozen=True)
class SurahInfo:
    """Information about a Surah."""

    surah_id: int
    name_simple: str
    arabic_name: str
    verses_count: int
    is_pre_bismillah: bool
    is_meccan: bool
    is_medinian: bool


@dataclass(frozen=True)
class LanguageInfo:
    """Information about a language."""

    language_code: str
    language_name: str
    has_translation: bool
    has_transliteration: bool


@dataclass(frozen=True)
class AyahPreview:
    """Ayah preview information for surah preview."""

    ayah_number: int
    image_url: Optional[str]


@dataclass(frozen=True)
class SurahPreview:
    """Surah preview with first 3 ayahs and multi-language support."""

    surah_id: int
    surah_title: str
    arabic_name: str
    ayah_previews: list[AyahPreview]
    total_ayahs_in_surah: int
    returned_ayahs_count: int
    is_pre_bismillah: bool
    is_meccan: bool
    is_medinian: bool
    position_in_order: Optional[int] = None

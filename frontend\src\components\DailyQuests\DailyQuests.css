.daily-quests-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  width: 300px;
  padding: 10px 30px;
  filter: drop-shadow(5px 12px 5px #94949450);
  background-color: #fbfbfb;
  border-radius: 30px;
  row-gap: 10px;
}

.daily-quests-container:hover {
  cursor: pointer;
  transform: scale(1.05);
  transition-duration: 0.2s;
}

.dq-title {
  font-size: 24px;
  font-weight: 600;
}

.quest-container {
  display: flex;
  justify-content: flex-start;
  flex-direction: row;
  align-items: center;
  width: 100%;
}

.quest-container a {
  font-size: 12px;
}

.quest-stat {
  margin-left: 10px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  width: 100%;
}

.quest-title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.pg-bar {
  height: 15px;
  margin-top: 10px;
  background-color: #ffffff;
  filter: drop-shadow(5px 5px 2px #a7a7a725);
  width: 100%;
  border-radius: 40px;
  margin-bottom: 30px;
  display: relative;
}

.pg-bar-done {
  width: 100%;
  background-color: #a5dea0;
  border-radius: 40px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pg-bar-done .pg-text {
  color: white;
  font-size: 10px;
  margin-top: 1px;
  z-index: 1;
  width: 100%;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
}

.pg-bar-done-40 {
  width: 40%;
}

.pg-bar-done-40 .pg-text {
  color: black;
}

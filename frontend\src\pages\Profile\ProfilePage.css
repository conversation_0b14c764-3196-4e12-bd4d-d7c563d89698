.grid-container-profile {
  display: grid;
  grid-template-columns: 14vw 40vw 20vw 25vw;
  /* grid-template-rows: 0.16fr 1fr 1.5fr; */

  grid-template-areas:
    "one top-nav top-nav top-nav"
    "one mleftt mmiddlet mright"
    "one mleft mmiddle mright"
    "one bleftt bmiddlet brightt"
    "one bleft bmiddle bright"
    "one button-date _ _";
}

.left-menu-profile {
  grid-area: one;
}

.title-pg {
  font-size: 24px;
  font-weight: 600;
}

.my-profile-section {
  grid-area: mleft;
  padding: 30px;
}

.block-h1 {
  font-size: 28px;
  font-weight: 500;
}

.my-pg-section {
  grid-area: mmiddle;
  padding: 30px;
}

.follow-section {
  grid-area: mright;
  padding: 30px;
}

.statistics-section {
  grid-area: bleft;
  padding: 30px;
}

.stat-btn-container {
  grid-area: button-date;
  display: flex;
  justify-content: center;
}

.stat-date-btn {
  color: white;
  width: 200px;
  background-color: #97cb9c;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  border-radius: 30px;
}

.stat-blocks {
  grid-area: bmiddle;
  padding: 30px;
}

.bookmarks-section {
  grid-area: bright;
  padding: 30px;
}


.my-profile-title {
  grid-area: mleftt;
  padding: 0px 30px;
  display: flex;
  align-items: center;
}

.progress-pg-title {
  grid-area: mmiddlet;
  padding: 0px 30px;
  display: flex;
  align-items: center;
}

.statistics-title {
  grid-area: bleftt;
  padding: 0px 30px;
  display: flex;
  align-items: center;
}

.bookmarks-title {
  grid-area: brightt;
  padding: 0px 30px;
  display: flex;
  align-items: center;
}

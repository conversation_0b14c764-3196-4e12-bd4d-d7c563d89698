from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass(frozen=True)
class CompactUser:
    """Compact user representation for lists and references."""

    id: int
    username: str
    first_name: str
    last_name: str
    total_xp: int = 0
    current_level: int = 1
    day_streak: int = 0
    gender: Optional[str] = None


@dataclass(frozen=True)
class League:
    """League information."""

    id: int
    rank: str
    end_time: datetime
    start_time: datetime
    icon: Optional[str]
    name: str


@dataclass(frozen=True)
class LanguageMetadata:
    """Language selection metadata for multi-language responses."""

    requested_language: str
    used_language: str
    fallback_applied: bool

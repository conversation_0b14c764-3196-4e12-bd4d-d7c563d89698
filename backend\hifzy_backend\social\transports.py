from dataclasses import dataclass
from datetime import datetime
from core.transports import CompactUser


@dataclass(frozen=True)
class UserFollowRelation:
    """User follow relationship information."""

    id: int
    follower: CompactUser
    following: CompactUser
    created_at: datetime


@dataclass(frozen=True)
class LeaderboardEntry:
    """Leaderboard entry for a user."""

    user: CompactUser
    total_xp: int
    rank: int

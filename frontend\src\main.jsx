import { StrictMode, useEffect } from "react";
import { createRoot } from "react-dom/client";
import "src/index.css";
import LandingPage from "src/pages/Landing/LandingPage.jsx";
import RegisterPage from "src/pages/Register/RegisterPage";
import PickPathPage from "src/pages/PickPath/PickPathPage";
import LevelsPage from "src/pages/Levels/LevelsPage";
import Games from "src/pages/Games/Games";
import ProfilePage from "src/pages/Profile/ProfilePage";

import { createBrowserRouter, RouterProvider } from "react-router-dom";

import { GoogleOAuthProvider } from "@react-oauth/google";

import ProtectedRoute from "src/hooks/ProtectedRoute.jsx";

import useAuthStore from "src/hooks/authStore.js";
import "src/i18n";
const router = createBrowserRouter([
  {
    path: "/",
    element: <LandingPage />,
  },
  {
    path: "/pickpath",
    element: (
      <ProtectedRoute>
        <PickPathPage />
      </ProtectedRoute>
    ),
  },
  {
    path: "/levels",
    element: (
      <ProtectedRoute>
        <LevelsPage />
      </ProtectedRoute>
    ),
  },
  {
    path: "/games/:id/:ayahId",
    element: (
      <ProtectedRoute>
        <Games />
      </ProtectedRoute>
    ),
  },
  {
    path: "/profile",
    element: (
      <ProtectedRoute>
        <ProfilePage />
      </ProtectedRoute>
    ),
  },
]);

const MainApp = () => {
  const initializeAuth = useAuthStore((state) => state.initializeAuth);

  // Initialize auth state on app load
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);
  return <RouterProvider router={router} />;
};

createRoot(document.getElementById("root")).render(
  <GoogleOAuthProvider clientId="537897310749-nmddjqeqk8gf3pdhshhdsb31sgk6532p.apps.googleusercontent.com">
    <MainApp />
  </GoogleOAuthProvider>
);

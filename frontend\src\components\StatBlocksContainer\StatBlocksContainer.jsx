import React, { useTransition } from "react";
import bookStreak from "src/assets/book_streak.svg";
import startStreak from "src/assets/start_streak.svg";
import fireStreak from "src/assets/fire_streak.svg";
import "src/components/StatBlocksContainer/StatBlocksContainer.css";
import { useTranslation } from "react-i18next";
const StatBlock = ({ logo, value, name }) => {
  return (
    <div className="horizontal-stat-block-container">
      <img src={logo} alt={`${name} logo`} className="stat-block-img" />
      <div className="stat-block-info">
        <a className="stat-block-value">{value}</a>
        <a className="stat-block-name">{name}</a>
      </div>
    </div>
  );
};

const StatBlocksContainer = () => {
  const { t } = useTranslation();
  const stats = [
    { logo: bookStreak, value: 70, name: t("day_streak") },
    { logo: startStreak, value: 50, name: t("weekly_goals") },
    { logo: fireStreak, value: 100, name: t("total_points") },
  ];
  return (
    <div className="stat-blocks-container">
      {stats.map((stat, index) => (
        <StatBlock
          key={index}
          logo={stat.logo}
          value={stat.value}
          name={stat.name}
        />
      ))}
    </div>
  );
};

export default StatBlocksContainer;

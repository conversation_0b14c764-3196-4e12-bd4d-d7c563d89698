import React from "react";
import bookmarksPng from "src/assets/bookmarksv2.png";

const ProfileBookmarks = () => {
  return (
    <div
      style={{
        height: "100%",
        display: "flex",
        justifyContent: "center",
        flexDirection: "column",
      }}
    >
      <div
        style={{
          backgroundColor: "#fbfbfb",
          filter: "drop-shadow(6px 6px 15px rgba(134, 134, 134, 0.2));",
          borderRadius: "30px",
          padding: "20px",
          display: "flex",
          justifyContent: "center",
          height: "100%",
          alignItems: "center",
        }}
      >
        <img src={bookmarksPng} alt="Bookmarks" style={{ width: "100%" }} />
        {/* <a style={{ fontSize: "28px", fontWeight: "500",  }}>No Bookmarks Yet</a> */}
      </div>
    </div>
  );
};

export default ProfileBookmarks;

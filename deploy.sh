#!/bin/bash

# Hifzy Django Application Deployment Script
# This script helps deploy the Hifzy application using Docker Compose

set -e

echo "🚀 Starting Hifzy Django Application Deployment"

# Check if Docker and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Stop and remove existing containers
echo "🛑 Stopping and removing existing containers..."
docker-compose down -v

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from template..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration before continuing."
    echo "   Especially update DJANGO_SECRET_KEY and DB_PASSWORD"
    read -p "Press Enter to continue after editing .env file..."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p backend/hifzy_backend/staticfiles
# mkdir -p backend/hifzy_backend/media

# Build and start services
echo "🔨 Building Docker images..."
docker-compose build

echo "🚀 Starting services..."
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Collect static
echo "Collecting static files..."
docker-compose exec web python hifzy_backend/manage.py collectstatic --noinput --env=docker

# Run makemigrations
echo "🗃️  Making migrations..."
docker-compose exec web python hifzy_backend/manage.py makemigrations --env=docker

# Run migrations
echo "🗃️  Running database migrations..."
docker-compose exec web python hifzy_backend/manage.py migrate --env=docker

# Run load
# check if data is already loaded in db
# echo "Checking if data is already loaded in db..."
# if docker-compose exec db psql -U hifzyuser hifzy_db -c "SELECT COUNT(*) FROM quran_surah;" | grep -q '0'; then
#     echo "Data is not loaded in db. Running load_quran_data..."
# else
#     echo "Data is already loaded in db. Skipping load_quran_data..."
#     exit 0
# fi

echo "🗃️  Running Load Quran Data..."
docker-compose exec web python hifzy_backend/manage.py load_quran_data --force --load-audio --images-folder ./media/ayah_images --env=docker

echo "🗃️  Running Load Arabic Letters..."
docker-compose exec web python hifzy_backend/manage.py load_arabic_letters --force --env=docker


# Create superuser (optional)
echo "👤 Do you want to create a superuser? (y/n)"
read -r create_superuser
if [ "$create_superuser" = "y" ] || [ "$create_superuser" = "Y" ]; then
    docker-compose exec web python hifzy_backend/manage.py createsuperuser --env=docker
fi

echo "✅ Deployment completed successfully!"
echo ""
echo "🌐 Your application is now running at:"
echo "   - Main application: http://localhost"
echo "   - Django admin: http://localhost/admin/"
echo "   - API documentation: http://localhost/swagger/"
echo ""
echo "📊 To view logs:"
echo "   docker-compose logs -f"
echo ""
echo "🛑 To stop the application:"
echo "   docker-compose down"
echo ""
echo "🔄 To restart the application:"
echo "   docker-compose restart"

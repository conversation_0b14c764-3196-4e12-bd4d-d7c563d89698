"""
Clean architecture views
These views follow the best practices outlined in the article:
- Views only handle HTTP request/response concerns
- Business logic is delegated to the service layer
- Data access is handled through repositories
- Transport objects are used for API responses
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from rest_framework.exceptions import ValidationError, NotFound, PermissionDenied
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from . import services
from . import serializers
from quran.models import Surah
from core.utils import serialize_dataclass, create_success_response, create_error_response
from .response_schemas import (
    StepByStepCompletionResponseSchemas,
    GameCompletionResponseSchemas,
    SurahProgressResponseSchemas,
    SkipSurahResponseSchemas,
    ProgressionSummaryResponseSchemas,
    ArabicLetterProgressResponseSchemas,
    ArabicLetterProgressRequestSerializer,
    UpdateArabicLetterProgressRequestSerializer,
)


class CompleteStepByStepGameView(APIView):
    """View for marking a step-by-step game as completed for a specific Ayah."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Mark a step-by-step game as completed for a specific Ayah and earn 20 XP. This endpoint validates surah access based on pedagogical progression, awards experience points, and tracks ayah completion progress. If the ayah is already completed, no additional XP is awarded.",
        request_body=serializers.AyahCompletionRequestSerializer,
        responses={
            200: openapi.Response(
                "Step-by-step game successfully completed for the Ayah and experience points awarded",
                StepByStepCompletionResponseSchemas.success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Step-by-step game completed successfully for Ayah 1 of Surah 1",
                        "data": {
                            "experience_awarded": 20,
                            "surah_experience": {
                                "user_id": 123,
                                "surah": {
                                    "id": 1,
                                    "surah_id": 1,
                                    "ayah_count": 7,
                                    "xp_per_ayah": 5,
                                },
                                "experience_points": 140,
                                "is_completed": False,
                                "is_bookmarked": False,
                                "created_at": "2024-01-15T10:30:00Z",
                            },
                            "user_progress": {
                                "total_xp": 1250,
                                "current_level": 3,
                                "day_streak": 5,
                                "league": "Bronze",
                            },
                            "next_unlock_info": {
                                "next_surah_number": 78,
                                "current_surah_position": 1,
                                "next_surah_position": 2,
                                "current_xp": 140,
                                "threshold_xp": 175,
                                "total_possible_xp": 245,
                                "xp_needed": 35,
                                "progress_percentage": 80.0,
                                "is_unlocked": False,
                            },
                        },
                    }
                },
            ),
            400: openapi.Response(
                "Invalid request data or ayah already completed", StepByStepCompletionResponseSchemas.error_400
            ),
            401: openapi.Response(
                "Authentication credentials not provided", StepByStepCompletionResponseSchemas.error_401
            ),
            403: openapi.Response("Surah not unlocked for this user", StepByStepCompletionResponseSchemas.error_403),
            404: openapi.Response("Surah or ayah not found", StepByStepCompletionResponseSchemas.error_404),
            500: openapi.Response("Internal server error", StepByStepCompletionResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Mark step-by-step game as completed for an Ayah."""
        serializer = serializers.AyahCompletionRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Validate the ayah exists using QuranService
            from games.services import QuranService

            surah_info = QuranService.get_surah_info(serializer.validated_data["surah_id"])

            if serializer.validated_data["ayah_id"] > surah_info.verses_count:
                return Response(
                    create_error_response(
                        f"Invalid ayah ID. Must be between 1 and {surah_info.verses_count} for Surah {serializer.validated_data['surah_id']}"
                    ),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Complete the step-by-step game
            completion = services.GameCompletionService.complete_step_by_step_game(
                user_id=request.user.id,
                surah_number=serializer.validated_data["surah_id"],
                ayah_number=serializer.validated_data["ayah_id"],
            )

            # Check if already completed
            if not completion:
                return Response(
                    create_success_response(
                        {"experience_awarded": 0},
                        f"Step-by-step game already completed for Ayah {serializer.validated_data['ayah_id']} of Surah {serializer.validated_data['surah_id']}",
                    ),
                    status=status.HTTP_200_OK,
                )
            # Get updated experience and progress
            surah_experience = services.ExperienceService.get_user_surah_experience(
                request.user.id, serializer.validated_data["surah_id"]
            )
            user_progress = services.ProgressService.get_user_progress_summary(request.user.id)

            # Get progression information
            next_unlock_info = services.SurahProgressionService.get_next_unlock_info(
                request.user.id, serializer.validated_data["surah_id"]
            )

            return Response(
                create_success_response(
                    {
                        "experience_awarded": services.SurahProgressionService.STEP_BY_STEP_XP_PER_AYAH,
                        "surah_experience": serialize_dataclass(surah_experience) if surah_experience else None,
                        "user_progress": user_progress,
                        "next_unlock_info": next_unlock_info,
                    },
                    f"Step-by-step game completed successfully for Ayah {serializer.validated_data['ayah_id']} of Surah {serializer.validated_data['surah_id']}",
                ),
                status=status.HTTP_200_OK,
            )
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to complete step-by-step game: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CompleteGameView(APIView):
    """View for marking a game as completed for a specific Surah."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Mark a game as completed for a specific Surah with a specific number of ayahs and earn experience points. This endpoint validates surah access based on pedagogical progression, prevents duplicate completions for the same game type and ayah count, and awards XP only for new ayahs that exceed the user's previous maximum completion for that game type. XP is calculated as new_ayahs * surah.xp_per_ayah (typically 5 XP per ayah for non-step-by-step games).",
        request_body=serializers.GameCompletionRequestSerializer,
        responses={
            200: openapi.Response(
                "Game successfully completed and experience points awarded",
                GameCompletionResponseSchemas.success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Shuffling game completed successfully with 3 ayahs",
                        "data": {
                            "experience_awarded": 15,
                            "surah_experience": {
                                "user_id": 123,
                                "surah": {
                                    "id": 1,
                                    "surah_number": 1,
                                    "name_simple": "Al-Fatihah",
                                    "ayah_count": 7,
                                    "xp_per_ayah": 5,
                                },
                                "experience_points": 155,
                                "is_completed": False,
                                "is_bookmarked": False,
                                "created_at": "2024-01-15T10:30:00Z",
                            },
                            "next_unlock_info": {
                                "next_surah_number": 78,
                                "current_surah_position": 1,
                                "next_surah_position": 2,
                                "current_xp": 155,
                                "threshold_xp": 175,
                                "total_possible_xp": 245,
                                "xp_needed": 20,
                                "progress_percentage": 88.6,
                                "is_unlocked": False,
                            },
                            "game_completion": {
                                "id": 789,
                                "user_id": 123,
                                "game_type": "shuffling",
                                "progress_ayah_count": 3,
                                "new_ayahs": 3,
                                "created_at": "2024-01-15T10:35:00Z",
                            },
                        },
                    }
                },
            ),
            400: openapi.Response(
                "Invalid request data, game already completed, or surah not unlocked",
                GameCompletionResponseSchemas.error_400,
            ),
            401: openapi.Response("Authentication credentials not provided", GameCompletionResponseSchemas.error_401),
            403: openapi.Response("Surah not unlocked for this user", GameCompletionResponseSchemas.error_403),
            404: openapi.Response("Surah not found", GameCompletionResponseSchemas.error_404),
            500: openapi.Response("Internal server error", GameCompletionResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Mark a game as completed."""
        serializer = serializers.GameCompletionRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Validate the surah and ayah count using QuranService
            from games.services import QuranService

            surah_info = QuranService.get_surah_info(serializer.validated_data["surah_id"])

            if serializer.validated_data["ayah_count"] > surah_info.verses_count:
                return Response(
                    create_error_response(
                        f"Invalid ayah count. Must be between 1 and {surah_info.verses_count} for Surah {serializer.validated_data['surah_id']}"
                    ),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # add checks if the user has already played with the same number of ayahs and the same game_type and return an error if so
            if services.GameCompletionService.has_user_completed_game_with_ayah_count(
                user_id=request.user.id,
                surah_number=serializer.validated_data["surah_id"],
                game_type=serializer.validated_data["game_type"],
                ayah_count=serializer.validated_data["ayah_count"],
            ):
                return Response(
                    create_error_response(
                        f"Game already completed with {serializer.validated_data['ayah_count']} ayahs for Surah {serializer.validated_data['surah_id']}"
                    ),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Complete the game
            game_completion = services.GameCompletionService.complete_game(
                user_id=request.user.id,
                surah_number=serializer.validated_data["surah_id"],
                game_type=serializer.validated_data["game_type"],
                ayah_count=serializer.validated_data["ayah_count"],
            )

            # Get updated experience and progress
            surah_experience = services.ExperienceService.get_user_surah_experience(
                request.user.id, serializer.validated_data["surah_id"]
            )

            # Get progression information
            next_unlock_info = services.SurahProgressionService.get_next_unlock_info(
                request.user.id, serializer.validated_data["surah_id"]
            )

            # Calculate experience awarded (5 XP per ayah for other games)
            surah = Surah.objects.get(surah_number=serializer.validated_data["surah_id"])
            experience_awarded = game_completion.new_ayahs * surah.xp_per_ayah

            return Response(
                create_success_response(
                    {
                        "experience_awarded": experience_awarded,
                        "surah_experience": serialize_dataclass(surah_experience) if surah_experience else None,
                        "next_unlock_info": next_unlock_info,
                        "game_completion": serialize_dataclass(game_completion) if game_completion else None,
                    },
                    f"{serializer.validated_data['game_type'].title()} game completed successfully with {serializer.validated_data['ayah_count']} ayahs",
                ),
                status=status.HTTP_200_OK,
            )
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to complete game: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GetSurahProgressView(APIView):
    """View for getting detailed progress information for a specific Surah."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get detailed progress information for a specific Surah, including experience points, completion status, and unlocked status",
        request_body=serializers.SurahProgressRequestSerializer,
        responses={
            200: openapi.Response(
                "Successfully retrieved Surah progress information", SurahProgressResponseSchemas.success_200
            ),
            400: openapi.Response("Invalid request data", SurahProgressResponseSchemas.error_400),
            401: openapi.Response("Authentication credentials not provided", SurahProgressResponseSchemas.error_401),
            403: openapi.Response("Surah not unlocked for user", SurahProgressResponseSchemas.error_403),
            404: openapi.Response("Surah not found", SurahProgressResponseSchemas.error_404),
            500: openapi.Response("Internal server error", SurahProgressResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get detailed progress information for a specific Surah."""
        serializer = serializers.SurahProgressRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]

            # Validate Surah access
            if not services.SurahProgressionService.validate_surah_access(request.user.id, surah_id):
                return Response(
                    create_error_response(f"Surah {surah_id} is not unlocked for this user"),
                    status=status.HTTP_403_FORBIDDEN,
                )

            # Get surah info using QuranService
            from games.services import QuranService

            surah_info = QuranService.get_surah_info(surah_id)
            # Get user's experience for this surah
            surah_experience = services.ExperienceService.get_user_surah_experience(request.user.id, surah_id)

            # Get completed ayahs count
            completed_ayahs = services.GameCompletionService.get_user_completed_ayahs_for_surah(
                request.user.id, int(surah_id)
            )
            completed_ayahs_count = len(completed_ayahs)
            current_experience = surah_experience.experience_points if surah_experience else 0
            # Get next unlock info
            next_unlock_info = services.SurahProgressionService.get_next_unlock_info(request.user.id, surah_id)

            return Response(
                create_success_response(
                    {
                        "surah_id": surah_id,
                        "name": surah_info.name_simple,
                        "experience_points": current_experience,
                        "completed_ayahs": completed_ayahs_count,
                        "total_ayahs": surah_info.verses_count,
                        "next_unlock_info": next_unlock_info,
                        "is_unlocked": services.SurahProgressionService.is_surah_unlocked(request.user.id, surah_id),
                    },
                    "Surah progress retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve surah progress: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class UserProgressPedagogicalOrderView(APIView):
    """View for getting user progress in pedagogical learning order."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get user's progress for all surahs in pedagogical learning order. This shows the custom learning sequence that starts with essential prayers and shorter surahs before progressing to longer chapters.",
        responses={
            200: openapi.Response(
                "User progress in pedagogical order retrieved successfully",
                openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "success": openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        "message": openapi.Schema(type=openapi.TYPE_STRING),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "user_id": openapi.Schema(type=openapi.TYPE_INTEGER),
                                "current_surah": openapi.Schema(type=openapi.TYPE_INTEGER),
                                "current_learning_position": openapi.Schema(type=openapi.TYPE_INTEGER),
                                "total_surahs": openapi.Schema(type=openapi.TYPE_INTEGER),
                                "progress_list": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            "learning_position": openapi.Schema(type=openapi.TYPE_INTEGER),
                                            "surah_number": openapi.Schema(type=openapi.TYPE_INTEGER),
                                            "current_xp": openapi.Schema(type=openapi.TYPE_INTEGER),
                                            "threshold_xp": openapi.Schema(type=openapi.TYPE_INTEGER),
                                            "total_possible_xp": openapi.Schema(type=openapi.TYPE_INTEGER),
                                            "is_unlocked": openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                            "progress_percentage": openapi.Schema(type=openapi.TYPE_NUMBER),
                                            "is_completed": openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                        },
                                    ),
                                ),
                            },
                        ),
                    },
                ),
            ),
            401: openapi.Response("Authentication required"),
            500: openapi.Response("Internal server error"),
        },
    )
    def post(self, request):
        """Get user progress in pedagogical learning order."""
        try:
            user_id = request.user.id

            # Get user progress in pedagogical order
            progress_list = services.SurahProgressionService.get_user_progress_in_learning_order(user_id)

            # Get current surah and its learning position
            current_surah = services.SurahProgressionService.get_user_current_surah(user_id)
            from progress.services import PedagogicalProgressionService

            current_learning_position = PedagogicalProgressionService.get_learning_position_by_surah(current_surah)

            response_data = {
                "user_id": user_id,
                "current_surah": current_surah,
                "current_learning_position": current_learning_position,
                "total_surahs": services.SurahProgressionService.TOTAL_SURAHS,
                "progress_list": progress_list,
            }

            return Response(
                create_success_response(
                    response_data,
                    "User progress in pedagogical order retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve user progress: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GetUserProgressionSummaryView(APIView):
    """View for getting comprehensive user progression summary."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get comprehensive progression summary including unlocked Surahs and next unlock requirements",
        responses={
            200: openapi.Response(
                "Successfully retrieved progression summary", ProgressionSummaryResponseSchemas.success_200
            ),
            401: openapi.Response(
                "Authentication credentials not provided", ProgressionSummaryResponseSchemas.error_401
            ),
            500: openapi.Response("Internal server error", ProgressionSummaryResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get comprehensive progression summary for the authenticated user."""
        try:
            progression_summary = services.SurahProgressionService.get_user_progression_summary(request.user.id)

            return Response(
                create_success_response(progression_summary, "User progression summary retrieved successfully"),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve progression summary: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ArabicLetterProgressView(APIView):
    """View for retrieving Arabic letter progress for a specific letter."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get user progress for a specific Arabic letter",
        request_body=ArabicLetterProgressRequestSerializer,
        responses={
            200: openapi.Response(
                "Arabic letter progress retrieved successfully",
                ArabicLetterProgressResponseSchemas.success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Arabic letter progress retrieved successfully",
                        "data": {
                            "progress": {
                                "id": 1,
                                "progress_status": "In progress",
                                "user_id": 1,
                                "arabic": {
                                    "id": 1,
                                    "title": "أ",
                                    "transcription": "alif",
                                    "audio_url": "https://example.com/audio/alif.mp3",
                                },
                                "created_at": "2024-01-01T10:00:00Z",
                            }
                        },
                    }
                },
            ),
            400: openapi.Response("Invalid request data", ArabicLetterProgressResponseSchemas.error_400),
            401: openapi.Response("Authentication required", ArabicLetterProgressResponseSchemas.error_401),
            404: openapi.Response("Arabic letter not found", ArabicLetterProgressResponseSchemas.error_404),
            500: openapi.Response("Internal server error", ArabicLetterProgressResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get user progress for a specific Arabic letter."""
        try:
            # Validate request data
            serializer = ArabicLetterProgressRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    create_error_response("Invalid request data", serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            letter_id = serializer.validated_data["letter_id"]

            # Get progress from service
            progress = services.ArabicLetterProgressService.get_user_letter_progress(request.user.id, letter_id)

            if not progress:
                # If no progress exists, create a new one with "New" status
                progress = services.ArabicLetterProgressService.update_letter_progress(
                    request.user.id, letter_id, "New"
                )

            # Prepare response data
            response_data = {"progress": serialize_dataclass(progress)}

            return Response(
                create_success_response(response_data, "Arabic letter progress retrieved successfully"),
                status=status.HTTP_200_OK,
            )

        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve Arabic letter progress: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class UpdateArabicLetterProgressView(APIView):
    """View for updating Arabic letter progress."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Update user progress for a specific Arabic letter",
        request_body=UpdateArabicLetterProgressRequestSerializer,
        responses={
            200: openapi.Response(
                "Arabic letter progress updated successfully",
                ArabicLetterProgressResponseSchemas.update_success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Arabic letter progress updated successfully",
                        "data": {
                            "progress": {
                                "id": 1,
                                "progress_status": "Completed",
                                "user_id": 1,
                                "arabic": {
                                    "id": 1,
                                    "title": "أ",
                                    "transcription": "alif",
                                    "audio_url": "https://example.com/audio/alif.mp3",
                                },
                                "created_at": "2024-01-01T10:00:00Z",
                            }
                        },
                    }
                },
            ),
            400: openapi.Response("Invalid request data", ArabicLetterProgressResponseSchemas.error_400),
            401: openapi.Response("Authentication required", ArabicLetterProgressResponseSchemas.error_401),
            404: openapi.Response("Arabic letter not found", ArabicLetterProgressResponseSchemas.error_404),
            500: openapi.Response("Internal server error", ArabicLetterProgressResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Update user progress for a specific Arabic letter."""
        try:
            # Validate request data
            serializer = UpdateArabicLetterProgressRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    create_error_response("Invalid request data", serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            letter_id = serializer.validated_data["letter_id"]
            progress_status = serializer.validated_data["progress_status"]

            # Update progress through service
            progress = services.ArabicLetterProgressService.update_letter_progress(
                request.user.id, letter_id, progress_status
            )

            # Prepare response data
            response_data = {"progress": serialize_dataclass(progress)}

            return Response(
                create_success_response(response_data, "Arabic letter progress updated successfully"),
                status=status.HTTP_200_OK,
            )

        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to update Arabic letter progress: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ArabicLetterProgressStatsView(APIView):
    """View for retrieving Arabic letter progress statistics."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get detailed Arabic letter progress statistics for the authenticated user",
        responses={
            200: openapi.Response(
                "Arabic letter progress statistics retrieved successfully",
                ArabicLetterProgressResponseSchemas.stats_success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Arabic letter progress statistics retrieved successfully",
                        "data": {
                            "stats": {
                                "total_available_letters": 28,
                                "total_tracked_letters": 15,
                                "new_letters": 5,
                                "in_progress_letters": 7,
                                "completed_letters": 3,
                                "completion_percentage": 10.71,
                            }
                        },
                    }
                },
            ),
            401: openapi.Response("Authentication required", ArabicLetterProgressResponseSchemas.error_401),
            500: openapi.Response("Internal server error", ArabicLetterProgressResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get detailed Arabic letter progress statistics for the authenticated user."""
        try:
            # Get statistics from service
            stats = services.ArabicLetterProgressService.get_progress_statistics(request.user.id)

            # Prepare response data
            response_data = {"stats": serialize_dataclass(stats)}

            return Response(
                create_success_response(response_data, "Arabic letter progress statistics retrieved successfully"),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve Arabic letter progress statistics: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AllArabicLettersProgressView(APIView):
    """View for retrieving progress for all Arabic letters."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get progress for all 28 Arabic letters, auto-initializing missing progress records",
        responses={
            200: openapi.Response(
                "All Arabic letters progress retrieved successfully",
                ArabicLetterProgressResponseSchemas.all_success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "All Arabic letters progress retrieved successfully",
                        "data": {
                            "letters_progress": [
                                {
                                    "id": 1,
                                    "progress_status": "New",
                                    "user_id": 1,
                                    "arabic": {
                                        "id": 1,
                                        "title": "أ",
                                        "transcription": "alif",
                                        "audio_url": "https://example.com/audio/alif.mp3",
                                    },
                                    "created_at": "2024-01-01T10:00:00Z",
                                },
                                {
                                    "id": 2,
                                    "progress_status": "In progress",
                                    "user_id": 1,
                                    "arabic": {
                                        "id": 2,
                                        "title": "ب",
                                        "transcription": "baa",
                                        "audio_url": "https://example.com/audio/baa.mp3",
                                    },
                                    "created_at": "2024-01-01T10:05:00Z",
                                },
                            ],
                            "total_letters": 28,
                            "initialized_count": 15,
                        },
                    }
                },
            ),
            401: openapi.Response("Authentication required", ArabicLetterProgressResponseSchemas.error_401),
            404: openapi.Response("User not found", ArabicLetterProgressResponseSchemas.error_404),
            500: openapi.Response("Internal server error", ArabicLetterProgressResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get progress for all 28 Arabic letters, auto-initializing missing progress records."""
        try:
            # Get all letters progress with auto-initialization
            all_progress, initialized_count = (
                services.ArabicLetterProgressService.get_all_letters_progress_with_initialization(request.user.id)
            )

            # Prepare response data
            response_data = {
                "letters_progress": [serialize_dataclass(progress) for progress in all_progress],
                "total_letters": len(all_progress),
                "initialized_count": initialized_count,
            }

            message = "All Arabic letters progress retrieved successfully"
            if initialized_count > 0:
                message += f" ({initialized_count} letters auto-initialized)"

            return Response(
                create_success_response(response_data, message),
                status=status.HTTP_200_OK,
            )

        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve all Arabic letters progress: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SkipSurahView(APIView):
    """View for skipping a surah in the pedagogical learning sequence."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Skip a surah in the pedagogical learning sequence without completing it. This endpoint allows users to skip their current surah and unlock the next one in the pedagogical progression. No XP is awarded for skipping - the surah is simply marked as skipped and the next surah becomes accessible. Users can skip their current surah or return to previously skipped surahs to skip them again. Skipped surahs remain accessible for future completion and XP earning.",
        request_body=serializers.SkipSurahRequestSerializer,
        responses={
            200: openapi.Response(
                "Surah skipped successfully and next surah unlocked",
                SkipSurahResponseSchemas.success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Surah 78 (An-Naba) skipped successfully. Next surah unlocked.",
                        "data": {
                            "skipped_surah": {
                                "surah_number": 78,
                                "learning_position": 2,
                                "is_skipped": True,
                            },
                            "next_surah": {
                                "surah_number": 79,
                                "learning_position": 3,
                                "is_unlocked": True,
                            },
                            "new_current_surah": 79,
                            "next_unlock_info": {
                                "next_surah_number": 80,
                                "current_surah_position": 3,
                                "next_surah_position": 4,
                                "current_xp": 0,
                                "threshold_xp": 200,
                                "total_possible_xp": 280,
                                "xp_needed": 200,
                                "progress_percentage": 0.0,
                                "is_unlocked": False,
                            },
                        },
                    }
                },
            ),
            400: openapi.Response(
                "Invalid request data, surah cannot be skipped, or last surah in sequence",
                SkipSurahResponseSchemas.error_400,
            ),
            401: openapi.Response("Authentication required", SkipSurahResponseSchemas.error_401),
            403: openapi.Response(
                "Surah not accessible (not current surah or already completed)", SkipSurahResponseSchemas.error_403
            ),
            404: openapi.Response("Surah not found", SkipSurahResponseSchemas.error_404),
            500: openapi.Response("Internal server error", SkipSurahResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Skip a surah in the pedagogical learning sequence."""
        try:
            # Validate request data
            serializer = serializers.SkipSurahRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    create_error_response("Invalid request data", serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            surah_id = serializer.validated_data["surah_id"]
            user_id = request.user.id

            # Skip the surah using the service
            skip_result = services.SurahProgressionService.skip_surah(user_id, surah_id)

            # Get surah name for response message
            try:
                message = f"Surah {surah_id} skipped successfully. Next surah unlocked."
            except Surah.DoesNotExist:
                message = f"Surah {surah_id} skipped successfully. Next surah unlocked."

            return Response(
                create_success_response(skip_result, message),
                status=status.HTTP_200_OK,
            )

        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except PermissionDenied as e:
            return Response(create_error_response(str(e)), status=status.HTTP_403_FORBIDDEN)
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to skip surah: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

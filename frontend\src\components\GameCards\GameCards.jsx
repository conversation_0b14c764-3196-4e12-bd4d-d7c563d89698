import React, { useState } from "react";
import "src/components/GameCards/GameCards.css";
import "src/components/AyahDropDown/AyahDropDown.css";
import Shuffling from "src/components/Shuffling/Shuffling";
import Memory from "src/components/Memory/Memory";
import Matching from "src/components/Matching/Matching";
import AudioGame from "src/components/AudioGame/AudioGame";
import shufflingBackground from "src/assets/shuffling_background.png";
import matchingBackground from "src/assets/matching_background.png";
import audioBackground from "src/assets/audiogame_background.png";
import memoryBackground from "src/assets/memory_background.png";
import lockedBackground from "src/assets/locked_background.png";
import { useTranslation } from "react-i18next";

function GameCards({
  activeGame,
  startGame,
  backToLearn,
  maxAyahId,
  selectedAyah,
  setSelectedAyah,
  updateProgress,
}) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const ayahOptions = Array.from({ length: maxAyahId }, (_, i) => i + 1);
  const { t } = useTranslation();
  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const selectAyah = (ayah) => {
    setSelectedAyah(ayah);
    setIsDropdownOpen(false);
  };

  const gameCards = [
    { xp: "0xp", title: t("shuffling"), gameType: "shuffling" },
    { xp: "0xp", title: t("matching"), gameType: "matching" },
    { xp: "0xp", title: t("audio"), gameType: "audio" },
    { xp: "0xp", title: t("memory"), gameType: "memory" },
    { xp: "0xp", title: t("gaps"), gameType: "flashcards" },
  ];

  let activeComponent = (
    <div className="game-cards">
      {gameCards.map((card, index) => (
        <div
          key={index}
          className="game-card"
          style={{
            backgroundImage:
              card.title === t("shuffling")
                ? `url(${shufflingBackground})`
                : card.title === t("matching")
                ? `url(${matchingBackground})`
                : card.title === t("audio")
                ? `url(${audioBackground})`
                : card.title === t("memory")
                ? `url(${memoryBackground})`
                : card.title === t("gaps")
                ? `url(${lockedBackground})`
                : "",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center",
          }}
          onClick={() => startGame(card.gameType)}
        >
          <div className="card-head">{/* <a>{card.xp}</a> */}</div>
          <div className="card-bottom">{card.title}</div>
        </div>
      ))}
    </div>
  );

  if (activeGame === "shuffling") {
    activeComponent = (
      <Shuffling
        selectedAyah={selectedAyah}
        updateProgress={updateProgress}
        backToLearn={backToLearn}
      />
    );
  } else if (activeGame === "memory") {
    activeComponent = (
      <Memory
        selectedAyah={selectedAyah}
        updateProgress={updateProgress}
        backToLearn={backToLearn}
      />
    );
  } else if (activeGame === "matching") {
    activeComponent = (
      <Matching
        selectedAyah={selectedAyah}
        updateProgress={updateProgress}
        backToLearn={backToLearn}
      />
    );
  } else if (activeGame == "audio") {
    activeComponent = (
      <AudioGame
        selectedAyah={selectedAyah}
        updateProgress={updateProgress}
        backToLearn={backToLearn}
      />
    );
  }

  return (
    <>
      {!activeGame && (
        <div className={`dropdown ${isDropdownOpen ? "open" : ""}`}>
          <button className="dropdown-button" onClick={toggleDropdown}>
            {t("select_number_of_ayahs") + selectedAyah} ▼
          </button>
          {isDropdownOpen && (
            <ul className="dropdown-menu">
              {ayahOptions.map((ayah) => (
                <li
                  key={ayah}
                  onClick={() => selectAyah(ayah)}
                  className="dropdown-item"
                >
                  {ayah}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
      {activeComponent}
    </>
  );
}

export default GameCards;

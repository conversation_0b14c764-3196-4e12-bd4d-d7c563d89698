from rest_framework import serializers
from django.db.models import Sum
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
import re
from .models import HifzyUser
from quran.models import Surah, Ayah


# class UserProfileSerializer(serializers.ModelSerializer):
#     """Basic user profile serializer for profile updates."""

#     username = serializers.CharField(
#         required=False,
#         allow_blank=True,
#         help_text="Username (optional) - must be 3-30 characters long and contain only letters, numbers, and underscores",
#     )
#     email = serializers.EmailField(required=False, help_text="Email address (optional)")
#     first_name = serializers.CharField(required=False, allow_blank=True, help_text="First name (optional)")
#     last_name = serializers.Char<PERSON>ield(required=False, allow_blank=True, help_text="Last name (optional)")
#     gender = serializers.<PERSON><PERSON><PERSON>(
#         choices=HifzyUser.GENDER_CHOICES,
#         required=False,
#         allow_blank=True,
#         help_text="Gender (optional) - 'male' or 'female'",
#     )

#     class Meta:
#         model = HifzyUser
#         fields = ["username", "email", "first_name", "last_name", "total_xp", "current_level", "day_streak", "gender"]
#         read_only_fields = ["total_xp", "current_level", "day_streak"]

#     def validate_username(self, value):
#         """Validate username format and uniqueness during updates."""
#         if not value:
#             return value

#         # Check username format (alphanumeric and underscores only, 3-30 characters)
#         username_regex = r"^[a-zA-Z0-9_]{3,30}$"
#         if not re.match(username_regex, value):
#             raise serializers.ValidationError(
#                 "Username must be 3-30 characters long and contain only letters, numbers, and underscores."
#             )

#         # Check username uniqueness (exclude current user)
#         current_user = self.context.get("request").user if self.context.get("request") else None
#         if current_user and HifzyUser.objects.filter(username=value).exclude(id=current_user.id).exists():
#             raise serializers.ValidationError("A user with this username already exists.")

#         return value

#     def validate_email(self, value):
#         """Validate email format and uniqueness during updates."""
#         if not value:
#             return value

#         # Check email format
#         email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
#         if not re.match(email_regex, value):
#             raise serializers.ValidationError("Enter a valid email address.")

#         # Check email uniqueness (exclude current user)
#         current_user = self.context.get("request").user if self.context.get("request") else None
#         if current_user and HifzyUser.objects.filter(email=value).exclude(id=current_user.id).exists():
#             raise serializers.ValidationError("A user with this email already exists.")

#         return value


class UserProfilePictureSerializer(serializers.ModelSerializer):
    class Meta:
        model = HifzyUser
        fields = ["profile_photo"]


class UserProfileCompleteSerializer(serializers.ModelSerializer):
    """Serializer for complete user profile information including profile picture."""

    username = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Username (optional) - must be 3-30 characters long and contain only letters, numbers, and underscores",
    )
    email = serializers.EmailField(required=False, help_text="Email address (optional)")
    first_name = serializers.CharField(required=False, allow_blank=True, help_text="First name (optional)")
    last_name = serializers.CharField(required=False, allow_blank=True, help_text="Last name (optional)")
    gender = serializers.ChoiceField(
        choices=HifzyUser.GENDER_CHOICES,
        required=False,
        allow_blank=True,
        help_text="Gender (optional) - 'male' or 'female'",
    )
    profile_photo = serializers.ImageField(required=False, help_text="Profile photo (optional)")

    class Meta:
        model = HifzyUser
        fields = [
            "username",
            "email",
            "first_name",
            "last_name",
            "total_xp",
            "current_level",
            "day_streak",
            "profile_photo",
            "gender",
        ]
        read_only_fields = ["total_xp", "current_level", "day_streak"]

    def validate_username(self, value):
        """Validate username format and uniqueness during updates."""
        if not value:
            return value

        # Check username format (alphanumeric and underscores only, 3-30 characters)
        username_regex = r"^[a-zA-Z0-9_]{3,30}$"
        if not re.match(username_regex, value):
            raise serializers.ValidationError(
                "Username must be 3-30 characters long and contain only letters, numbers, and underscores."
            )

        # Check username uniqueness (exclude current user)
        current_user = self.context.get("request").user if self.context.get("request") else None
        if current_user and HifzyUser.objects.filter(username=value).exclude(id=current_user.id).exists():
            raise serializers.ValidationError("A user with this username already exists.")

        return value

    def validate_email(self, value):
        """Validate email format and uniqueness during updates."""
        if not value:
            raise serializers.ValidationError("Email is required.")

        # Check email format
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_regex, value):
            raise serializers.ValidationError("Enter a valid email address.")

        # Check email uniqueness (exclude current user)
        current_user = self.context.get("request").user if self.context.get("request") else None
        if current_user and HifzyUser.objects.filter(email=value).exclude(id=current_user.id).exists():
            raise serializers.ValidationError("A user with this email already exists.")

        return value


class BookmarkAyahSerializer(serializers.Serializer):
    """Serializer for bookmark operations using UserAyahMM model."""

    surah_id = serializers.IntegerField(min_value=1, max_value=114)
    ayah_id = serializers.IntegerField(min_value=1)

    def validate(self, data):
        """Validate that the surah and ayah exist."""
        try:
            surah = Surah.objects.get(surah_number=data["surah_id"])
            ayah = Ayah.objects.get(surah=surah, ayah_number=data["ayah_id"])
        except (Surah.DoesNotExist, Ayah.DoesNotExist):
            raise serializers.ValidationError("Invalid surah_id or ayah_id")
        return data


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration with email, username, and password."""

    password = serializers.CharField(write_only=True, style={"input_type": "password"})
    password_confirm = serializers.CharField(write_only=True, style={"input_type": "password"})

    class Meta:
        model = HifzyUser
        fields = ["username", "email", "password", "password_confirm", "first_name", "last_name", "gender"]
        extra_kwargs = {
            "email": {"required": True},
            "username": {"required": False},
            "first_name": {"required": False},
            "last_name": {"required": False},
            "gender": {"required": False},
        }

    def validate_email(self, value):
        """Validate email format and uniqueness."""
        if not value:
            raise serializers.ValidationError("Email is required.")

        # Check email format
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_regex, value):
            raise serializers.ValidationError("Enter a valid email address.")

        # Check email uniqueness
        if HifzyUser.objects.filter(email=value).exists():
            raise serializers.ValidationError("A user with this email already exists.")

        return value

    def validate_username(self, value):
        """Validate username format and uniqueness when provided."""
        # Username is now optional, so allow empty/None values
        if not value:
            return value

        # Check username format (alphanumeric and underscores only, 3-30 characters)
        username_regex = r"^[a-zA-Z0-9_]{3,30}$"
        if not re.match(username_regex, value):
            raise serializers.ValidationError(
                "Username must be 3-30 characters long and contain only letters, numbers, and underscores."
            )

        # Check username uniqueness
        if HifzyUser.objects.filter(username=value).exists():
            raise serializers.ValidationError("A user with this username already exists.")

        return value

    def validate_password(self, value):
        """Validate password strength."""
        if not value:
            raise serializers.ValidationError("Password is required.")

        try:
            validate_password(value)
        except DjangoValidationError as e:
            raise serializers.ValidationError(list(e.messages))

        return value

    def validate(self, attrs):
        """Validate that passwords match."""
        password = attrs.get("password")
        password_confirm = attrs.get("password_confirm")

        if password != password_confirm:
            raise serializers.ValidationError("Passwords do not match.")

        return attrs

    def create(self, validated_data):
        """Create a new user with encrypted password."""
        validated_data.pop("password_confirm")
        password = validated_data.pop("password")

        # Generate username if not provided
        username = validated_data.get("username")
        if not username:
            from . import services

            username = services.AuthenticationService._generate_username_from_email(validated_data["email"])
            validated_data["username"] = username

        user = HifzyUser.objects.create_user(password=password, **validated_data)
        return user


class UserLoginSerializer(serializers.Serializer):
    """Serializer for user login with email/username and password."""

    login = serializers.CharField(help_text="Enter your email address or username")
    password = serializers.CharField(write_only=True, style={"input_type": "password"}, help_text="Enter your password")

    def validate(self, attrs):
        """Validate login credentials."""
        login = attrs.get("login")
        password = attrs.get("password")

        if not login or not password:
            raise serializers.ValidationError("Both login and password are required.")

        # Try to find user by email or username
        user = None
        if "@" in login:
            # Login with email
            try:
                user = HifzyUser.objects.get(email=login)
            except HifzyUser.DoesNotExist:
                pass
        else:
            # Login with username
            try:
                user = HifzyUser.objects.get(username=login)
            except HifzyUser.DoesNotExist:
                pass

        if user and user.check_password(password):
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled.")
            attrs["user"] = user
            return attrs

        raise serializers.ValidationError("Invalid login credentials.")


class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for changing user password."""

    current_password = serializers.CharField(
        write_only=True, style={"input_type": "password"}, help_text="Enter your current password"
    )
    new_password = serializers.CharField(
        write_only=True, style={"input_type": "password"}, help_text="Enter your new password"
    )
    new_password_confirm = serializers.CharField(
        write_only=True, style={"input_type": "password"}, help_text="Confirm your new password"
    )

    def validate_current_password(self, value):
        """Validate current password."""
        user = self.context["request"].user
        if not user.check_password(value):
            raise serializers.ValidationError("Current password is incorrect.")
        return value

    def validate_new_password(self, value):
        """Validate new password strength."""
        try:
            validate_password(value, user=self.context["request"].user)
        except DjangoValidationError as e:
            raise serializers.ValidationError(list(e.messages))
        return value

    def validate(self, attrs):
        """Validate that new passwords match."""
        new_password = attrs.get("new_password")
        new_password_confirm = attrs.get("new_password_confirm")

        if new_password != new_password_confirm:
            raise serializers.ValidationError("New passwords do not match.")

        # Check that new password is different from current password
        current_password = attrs.get("current_password")
        if new_password == current_password:
            raise serializers.ValidationError("New password must be different from current password.")

        return attrs


class EmailAvailabilitySerializer(serializers.Serializer):
    """Serializer for checking email availability."""

    email = serializers.EmailField(help_text="Email address to check for availability")

    def validate_email(self, value):
        """Validate email format."""
        if not value:
            raise serializers.ValidationError("Email is required.")

        # Check email format using the same regex as registration
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_regex, value):
            raise serializers.ValidationError("Enter a valid email address.")

        return value.lower()  # Normalize email to lowercase


class EmailVerificationRequestSerializer(serializers.Serializer):
    """Serializer for requesting email verification for registration."""

    email = serializers.EmailField(help_text="Email address to verify for registration")

    def validate_email(self, value):
        """Validate email format."""
        if not value:
            raise serializers.ValidationError("Email is required.")

        # Check email format using the same regex as registration
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_regex, value):
            raise serializers.ValidationError("Enter a valid email address.")

        return value.lower()  # Normalize email to lowercase


class EmailVerificationCodeSerializer(serializers.Serializer):
    """Serializer for verifying email verification code."""

    email = serializers.EmailField(help_text="Email address being verified")
    verification_code = serializers.CharField(
        max_length=10, help_text="Verification code sent to email (use '0000' for development)"
    )

    def validate_email(self, value):
        """Validate email format."""
        if not value:
            raise serializers.ValidationError("Email is required.")

        # Check email format using the same regex as registration
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_regex, value):
            raise serializers.ValidationError("Enter a valid email address.")

        return value.lower()  # Normalize email to lowercase

    def validate_verification_code(self, value):
        """Validate verification code format."""
        if not value:
            raise serializers.ValidationError("Verification code is required.")

        return value.strip()


class PasswordResetRequestSerializer(serializers.Serializer):
    """Serializer for requesting password reset."""

    email = serializers.EmailField(help_text="Email address of the account to reset password")

    def validate_email(self, value):
        """Validate email format."""
        if not value:
            raise serializers.ValidationError("Email is required.")

        # Check email format using the same regex as registration
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_regex, value):
            raise serializers.ValidationError("Enter a valid email address.")

        return value.lower()  # Normalize email to lowercase


class PasswordResetVerificationSerializer(serializers.Serializer):
    """Serializer for verifying password reset code."""

    email = serializers.EmailField(help_text="Email address of the account")
    verification_code = serializers.CharField(
        max_length=10, help_text="Verification code sent to email (use '0000' for development)"
    )

    def validate_email(self, value):
        """Validate email format."""
        if not value:
            raise serializers.ValidationError("Email is required.")

        # Check email format using the same regex as registration
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_regex, value):
            raise serializers.ValidationError("Enter a valid email address.")

        return value.lower()  # Normalize email to lowercase

    def validate_verification_code(self, value):
        """Validate verification code format."""
        if not value:
            raise serializers.ValidationError("Verification code is required.")

        return value.strip()


class PasswordResetCompleteSerializer(serializers.Serializer):
    """Serializer for completing password reset with verification."""

    email = serializers.EmailField(help_text="Email address of the account")
    verification_code = serializers.CharField(
        max_length=10, help_text="Verification code sent to email (use '0000' for development)"
    )
    new_password = serializers.CharField(
        write_only=True, style={"input_type": "password"}, help_text="New password for the account"
    )
    new_password_confirm = serializers.CharField(
        write_only=True, style={"input_type": "password"}, help_text="Confirm new password"
    )

    def validate_email(self, value):
        """Validate email format."""
        if not value:
            raise serializers.ValidationError("Email is required.")

        # Check email format using the same regex as registration
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_regex, value):
            raise serializers.ValidationError("Enter a valid email address.")

        return value.lower()  # Normalize email to lowercase

    def validate_verification_code(self, value):
        """Validate verification code format."""
        if not value:
            raise serializers.ValidationError("Verification code is required.")

        return value.strip()

    def validate_new_password(self, value):
        """Validate new password strength."""
        if not value:
            raise serializers.ValidationError("New password is required.")

        try:
            validate_password(value)
        except DjangoValidationError as e:
            raise serializers.ValidationError(list(e.messages))

        return value

    def validate(self, attrs):
        """Validate that passwords match."""
        new_password = attrs.get("new_password")
        new_password_confirm = attrs.get("new_password_confirm")

        if new_password != new_password_confirm:
            raise serializers.ValidationError("New passwords do not match.")

        return attrs


class VerifiedUserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration with email verification."""

    email = serializers.EmailField(help_text="Email address (must be verified first)")
    verification_code = serializers.CharField(
        max_length=10, help_text="Email verification code (use '0000' for development)"
    )
    password = serializers.CharField(write_only=True, style={"input_type": "password"})
    password_confirm = serializers.CharField(write_only=True, style={"input_type": "password"})

    class Meta:
        model = HifzyUser
        fields = [
            "username",
            "email",
            "verification_code",
            "password",
            "password_confirm",
            "first_name",
            "last_name",
            "gender",
        ]
        extra_kwargs = {
            "email": {"required": True},
            "username": {"required": False},
            "first_name": {"required": False},
            "last_name": {"required": False},
            "gender": {"required": False},
        }

    def validate_email(self, value):
        """Validate email format and uniqueness."""
        if not value:
            raise serializers.ValidationError("Email is required.")

        # Check email format
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if not re.match(email_regex, value):
            raise serializers.ValidationError("Enter a valid email address.")

        # Check email uniqueness
        if HifzyUser.objects.filter(email=value.lower()).exists():
            raise serializers.ValidationError("A user with this email already exists.")

        return value.lower()

    def validate_username(self, value):
        """Validate username format and uniqueness when provided."""
        # Username is now optional, so allow empty/None values
        if not value:
            return value

        # Check username format (alphanumeric and underscores only, 3-30 characters)
        username_regex = r"^[a-zA-Z0-9_]{3,30}$"
        if not re.match(username_regex, value):
            raise serializers.ValidationError(
                "Username must be 3-30 characters long and contain only letters, numbers, and underscores."
            )

        # Check username uniqueness
        if HifzyUser.objects.filter(username=value).exists():
            raise serializers.ValidationError("A user with this username already exists.")

        return value

    def validate_verification_code(self, value):
        """Validate verification code format."""
        if not value:
            raise serializers.ValidationError("Verification code is required.")

        return value.strip()

    def validate_password(self, value):
        """Validate password strength."""
        if not value:
            raise serializers.ValidationError("Password is required.")

        try:
            validate_password(value)
        except DjangoValidationError as e:
            raise serializers.ValidationError(list(e.messages))

        return value

    def validate(self, attrs):
        """Validate that passwords match and email verification code is correct."""
        password = attrs.get("password")
        password_confirm = attrs.get("password_confirm")
        email = attrs.get("email")
        verification_code = attrs.get("verification_code")

        if password != password_confirm:
            raise serializers.ValidationError("Passwords do not match.")

        # Verify the email verification code
        from . import services
        from rest_framework.exceptions import ValidationError as RestValidationError

        try:
            verification_result = services.AuthenticationService.verify_email_code_for_registration(
                email, verification_code
            )
            if not verification_result.is_verified:
                raise serializers.ValidationError("Invalid verification code.")
        except RestValidationError as e:
            raise serializers.ValidationError(str(e))

        return attrs

    def create(self, validated_data):
        """Create a new user with encrypted password after email verification."""
        validated_data.pop("password_confirm")
        validated_data.pop("verification_code")  # Remove verification code from user data
        password = validated_data.pop("password")

        # Generate username if not provided
        username = validated_data.get("username")
        if not username:
            from . import services

            username = services.AuthenticationService._generate_username_from_email(validated_data["email"])
            validated_data["username"] = username

        user = HifzyUser.objects.create_user(password=password, **validated_data)
        return user


class AyahReactionSerializer(serializers.Serializer):
    """Serializer for ayah reaction operations."""

    surah_id = serializers.IntegerField(min_value=1, max_value=114, help_text="Surah ID (1-114)")
    ayah_number = serializers.IntegerField(min_value=1, help_text="Ayah number within the surah")

    def validate(self, attrs):
        """Validate that the ayah exists."""
        surah_id = attrs["surah_id"]
        ayah_number = attrs["ayah_number"]

        try:
            surah = Surah.objects.get(surah_number=surah_id)
        except Surah.DoesNotExist:
            raise serializers.ValidationError(f"Surah {surah_id} does not exist.")

        try:
            Ayah.objects.get(surah=surah, ayah_number=ayah_number)
        except Ayah.DoesNotExist:
            raise serializers.ValidationError(f"Ayah {ayah_number} does not exist in Surah {surah_id}.")

        return attrs


class AyahReactionRemoveSerializer(serializers.Serializer):
    """Serializer for removing ayah reactions."""

    surah_id = serializers.IntegerField(min_value=1, max_value=114, help_text="Surah ID (1-114)")
    ayah_number = serializers.IntegerField(min_value=1, help_text="Ayah number within the surah")

    def validate(self, attrs):
        """Validate that the ayah exists."""
        surah_id = attrs["surah_id"]
        ayah_number = attrs["ayah_number"]

        try:
            surah = Surah.objects.get(surah_number=surah_id)
        except Surah.DoesNotExist:
            raise serializers.ValidationError(f"Surah {surah_id} does not exist.")

        try:
            Ayah.objects.get(surah=surah, ayah_number=ayah_number)
        except Ayah.DoesNotExist:
            raise serializers.ValidationError(f"Ayah {ayah_number} does not exist in Surah {surah_id}.")

        return attrs


class AyahReportSerializer(serializers.Serializer):
    """Serializer for reporting ayahs."""

    surah_id = serializers.IntegerField(min_value=1, max_value=114, help_text="Surah ID (1-114)")
    ayah_number = serializers.IntegerField(min_value=1, help_text="Ayah number within the surah")
    complaint_type = serializers.ChoiceField(
        choices=[
            ("inappropriate_content", "Inappropriate content"),
            ("translation_error", "Translation error"),
            ("audio_quality_issues", "Audio quality issues"),
            ("technical_problems", "Technical problems"),
            ("other", "Other"),
        ],
        help_text="Type of complaint",
    )
    description = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=1000,
        help_text="Optional detailed description of the issue (max 1000 characters)",
    )

    def validate(self, attrs):
        """Validate that the ayah exists."""
        surah_id = attrs["surah_id"]
        ayah_number = attrs["ayah_number"]

        try:
            surah = Surah.objects.get(surah_number=surah_id)
        except Surah.DoesNotExist:
            raise serializers.ValidationError(f"Surah {surah_id} does not exist.")

        try:
            Ayah.objects.get(surah=surah, ayah_number=ayah_number)
        except Ayah.DoesNotExist:
            raise serializers.ValidationError(f"Ayah {ayah_number} does not exist in Surah {surah_id}.")

        return attrs

import LeftMenuPanel from "src/components/LeftMenuPanel/LeftMenuPanel";
import "src/pages/Profile/ProfilePage.css";

import ProfileBlock from "src/components/ProfileBlock/ProfileBlock.jsx";
import ProfilePgBlock from "src/components/ProfilePgBlock/ProfilePgBlock.jsx";
import ProfileFollowersBlock from "src/components/ProfileFollowersBlock/ProfileFollowersBlock.jsx";
import ProfileStatBlock from "src/components/ProfileStatBlock.jsx";
import StatBlocksContainer from "src/components/StatBlocksContainer/StatBlocksContainer.jsx";
import ProfileBookmarks from "src/components/ProfileBookmarks.jsx";
import useAuthStore from "src/hooks/authStore";
import { useTranslation } from "react-i18next";

function ProfilePage() {
  const { t } = useTranslation();
  const { token } = useAuthStore();
  return (
    <>
      <section className="grid-container-profile">
        <div className="left-menu-profile">
          <LeftMenuPanel />
        </div>
        <div className="my-profile-title">
          <a className="block-h1">{t("my_profile_title")}</a>
        </div>
        <div className="my-profile-section">
          <ProfileBlock />
        </div>
        <div className="progress-pg-title">
          <a className="block-h1">{t("progress_title")}</a>
        </div>
        <div className="my-pg-section">
          <ProfilePgBlock />
        </div>
        <div className="follow-section">
          <ProfileFollowersBlock />
        </div>
        <div className="statistics-title">
          <a className="block-h1">{t("statistics_title")}</a>
        </div>
        <div className="statistics-section">
          <ProfileStatBlock />
        </div>
        <div className="stat-btn-container">
          <button className="stat-date-btn">23.05.24 - 02.08.24</button>
        </div>
        <div className="stat-blocks">
          <StatBlocksContainer />
        </div>
        <div className="bookmarks-title">
          <a className="block-h1">{t("bookmarks_title")}</a>
        </div>
        <div className="bookmarks-section">
          <ProfileBookmarks />
        </div>
      </section>
    </>
  );
}

export default ProfilePage;

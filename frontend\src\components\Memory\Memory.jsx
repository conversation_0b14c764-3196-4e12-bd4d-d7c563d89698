import React, { useState, useEffect } from "react";
import jsBadge from "src/assets/memory_flipped.svg";
import image from "src/assets/image_memory.svg";
import text from "src/assets/text_memory.svg";
import "src/components/Memory/Memory.css";
import { useParams } from "react-router-dom";
import "react-toastify/dist/ReactToastify.css";
import { toast, ToastContainer } from "react-toastify";
import axiosInstance from "src/utils/axios";
import LoadingSpinner from "src/components/LoadingSpinner";
import GameCompletedBar from "src/components/GameCompletedBar/GameCompletedBar";
import { BACKEND_URL } from "src/utils/settings";
import useLanguageStore from "src/hooks/languageStore";
function Card({ card, onClick }) {
  const language = useLanguageStore((state) => state.language);
  return (
    <div
      className={`memory-card${card.isFlipped ? " flip" : ""}`}
      onClick={onClick}
      style={{ order: card.order }}
      data-testid={card.id}
    >
      {card.image ? (
        <img className="front-face" src={BACKEND_URL + card.image} alt="Card" />
      ) : (
        <div className="front-face front-text">
          <div className="ayah-text">{card.text}</div>
          <div className="english-text">
            {language === "en" ? card.english_text : card.russian_text}
          </div>
        </div>
      )}
      <img className="back-face" src={jsBadge} alt="JS Badge" />
    </div>
  );
}

function Memory({ selectedAyah, updateProgress, backToLearn }) {
  const [loading, setLoading] = useState(true);
  const { id, ayahId } = useParams();
  const [cardsState, setCardsState] = useState(null);
  const [firstCard, setFirstCard] = useState(null);
  const [secondClick, setSecondClick] = useState(false);
  const [wait, setWait] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);

  useEffect(() => {
    const fetchGameData = async () => {
      try {
        const response = await axiosInstance.post("/games/memory/", {
          id,
          ayahId,
          selectedAyah,
        });
        setCardsState(response.data.cards);
        setLoading(false);
      } catch (error) {
        toast.error("Error fetching data from API");
        console.log(error);
        setLoading(false);
      }
    };

    fetchGameData();
  }, [id, ayahId, selectedAyah]);

  // Проверка завершения игры
  useEffect(() => {
    if (cardsState && cardsState.every((card) => card.passed)) {
      setIsCorrect(true);
      updateProgress();
    }
  }, [cardsState, updateProgress]);

  const checker = async (card) => {
    if (card.name === firstCard.name) {
      card["passed"] = true;
      firstCard["passed"] = true;
      changeCardStatusHandler(card);
      changeCardStatusHandler(firstCard);
    } else {
      setWait(true);
      setTimeout(() => {
        changeCardStatusHandler(card);
        changeCardStatusHandler(firstCard);
        setWait(false);
      }, 1500);
    }
  };

  const changeCardStatusHandler = async (clickedCard) => {
    if (!clickedCard.passed) clickedCard.isFlipped = !clickedCard.isFlipped;
    let index = cardsState.findIndex((card) => card.id === clickedCard.id);
    let newState = [...cardsState];
    newState.splice(index, 1, clickedCard);
    setCardsState(newState);
  };

  const handleClick = async (e, clickedCard) => {
    if (wait || clickedCard.isFlipped || clickedCard.passed) {
      return; // Игнорировать клик, если идет ожидание, карта перевернута или уже совпала
    }

    if (!secondClick) {
      setFirstCard(clickedCard);
      setSecondClick(true);
      changeCardStatusHandler(clickedCard);
    } else {
      setSecondClick(false);
      changeCardStatusHandler(clickedCard);
      checker(clickedCard);
      setFirstCard(null);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <>
      <section className="memory-game">
        <ToastContainer
          position="top-center"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="colored"
        />

        {cardsState?.map((card) => {
          return (
            <Card
              key={card.id}
              card={card}
              onClick={(e) => handleClick(e, card)}
            />
          );
        })}
      </section>
      {isCorrect && (
        <GameCompletedBar result={true} backToLearn={backToLearn} />
      )}
    </>
  );
}

export default Memory;

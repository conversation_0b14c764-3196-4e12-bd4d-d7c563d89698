from django.db import models
from core.settings.base import AUTH_USER_MODEL


class League(models.Model):
    """Model for user leagues/competitions."""

    RANK_CHOICES = (
        ("Bronze", "Bronze"),
        ("Silver", "Silver"),
        ("Gold", "Gold"),
    )

    rank = models.CharField(max_length=10, choices=RANK_CHOICES, help_text="League rank")
    end_time = models.DateTimeField(help_text="League end time")
    start_time = models.DateTimeField(help_text="League start time")
    icon = models.CharField(max_length=100, blank=True, null=True, help_text="League icon")
    name = models.Char<PERSON>ield(max_length=100, help_text="League name")

    class Meta:
        verbose_name = "League"
        verbose_name_plural = "Leagues"
        ordering = ["start_time"]

    def __str__(self):
        return f"{self.name} ({self.rank})"


class UserFollowMM(models.Model):
    """Model for user follow relationships."""

    follower = models.ForeignKey(AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="following_relations")
    following = models.ForeignKey(AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="follower_relations")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("follower", "following")
        verbose_name = "User Follow Relation"
        verbose_name_plural = "User Follow Relations"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.follower.username} follows {self.following.username}"

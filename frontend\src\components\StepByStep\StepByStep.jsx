import React, { useState } from "react";
import "src/components/StepByStep/StepByStep.css";
import { useTranslation } from "react-i18next";
const StepByStep = ({ ayahText }) => {
  const { t } = useTranslation();
  const [revealedWordsCount, setRevealedWordsCount] = useState(1);
  const words = ayahText.split(" ");

  const handleAddWord = () => {
    if (revealedWordsCount < words.length) {
      setRevealedWordsCount(revealedWordsCount + 1);
    }
  };

  return (
    <div className="reveal-container">
      <div className="text" dir="rtl" lang="ar">
        {words.map((word, index) => (
          <span
            key={index}
            className={`word ${
              index < revealedWordsCount ? "visible" : "hidden"
            }`}
          >
            {word}
          </span>
        ))}
      </div>
      {revealedWordsCount < words.length && (
        <button className="add-button" onClick={handleAddWord}>
          {t("add_sbs")}
        </button>
      )}
    </div>
  );
};

export default StepByStep;

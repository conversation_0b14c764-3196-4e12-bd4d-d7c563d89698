// ProtectedRoute.js
import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import useAuthStore from "src/hooks/authStore.js";
import LoadingSpinner from "src/components/LoadingSpinner";
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuthStore(); // Access isAuthenticated and loading state

  // If loading, return null or a loading spinner to wait for initialization
  if (loading) {
    return <LoadingSpinner />;
  }

  // If not authenticated, redirect to the landing page
  if (!isAuthenticated) {
    return <Navigate to="/" />;
  }

  // If authenticated, render the children
  return children;
};

export default ProtectedRoute;

// src/utils/axios.js
import axios from "axios";
import useAuthStore from "src/hooks/authStore.js";
import { useNavigate } from "react-router-dom";
import { BACKEND_URL } from "src/utils/settings";
// Create an Axios instance
const axiosInstance = axios.create({
  // baseURL: "https://hifzy.ru/api/v1",
  baseURL: `${BACKEND_URL}/api/v1`,
  timeout: 10000,
});

// Request interceptor to add the token to headers
axiosInstance.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token; // Get the token from Zustand store
    if (token) {
      config.headers.Authorization = `Token ${token}`; // Add token to Authorization header
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token expiration or unauthorized access
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    const navigate = useNavigate(); // To handle redirects on unauthorized access

    if (error.response && error.response.status === 401) {
      // If token is expired or user is unauthorized, clear the token and redirect to login
      useAuthStore.getState().logout(); // Clear token from Zustand store
      navigate("/"); // Redirect to landing or login page
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;

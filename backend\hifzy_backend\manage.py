#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys


def main():
    """Run administrative tasks."""
    # Check if environment is specified in command line arguments
    env = 'development'  # Default environment
    
    # Look for --env=production or similar in arguments
    for arg in sys.argv:
        if arg.startswith('--env='):
            env = arg.split('=')[1]
            sys.argv.remove(arg)
            break
    
    # Set the environment variable
    os.environ.setdefault('DJANGO_ENVIRONMENT', env)
    
    # Set the Django settings module to our package
    if env == 'production':
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings.production')
    elif env == "development":
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings.development')
    elif env == "docker":
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings.docker")
    
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()

"""
Clean architecture views
These views follow the best practices outlined in the article:
- Views only handle HTTP request/response concerns
- Business logic is delegated to the service layer
- Data access is handled through repositories
- Transport objects are used for API responses
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from rest_framework.exceptions import ValidationError
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from . import services
from . import serializers
from accounts.models import HifzyUser
from core.utils import serialize_dataclass, create_success_response, create_error_response, serialize_dataclass_list
from .response_schemas import (
    FollowUserResponseSchemas,
    UnfollowUserResponseSchemas,
    FollowersListResponseSchemas,
    FollowingListResponseSchemas,
    FollowStatusResponseSchemas,
    GlobalLeaderboardResponseSchemas,
    FollowedUsersLeaderboardResponseSchemas,
)


class FollowUserView(APIView):
    """View for following another user."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Follow another user",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["username"],
            properties={
                "username": openapi.Schema(type=openapi.TYPE_STRING, description="Username of the user to follow"),
            },
        ),
        responses={
            201: openapi.Response("Successfully followed user", FollowUserResponseSchemas.success_201),
            400: openapi.Response(
                "Invalid request data or cannot follow yourself", FollowUserResponseSchemas.error_400
            ),
            401: openapi.Response("Authentication credentials not provided", FollowUserResponseSchemas.error_401),
            404: openapi.Response("User to follow not found", FollowUserResponseSchemas.error_404),
            409: openapi.Response("Already following this user", FollowUserResponseSchemas.error_409),
            500: openapi.Response("Internal server error", FollowUserResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Follow another user."""
        username = request.data.get("username")
        if not username:
            return Response(create_error_response("Username is required"), status=status.HTTP_400_BAD_REQUEST)

        # Cannot follow yourself
        if username == request.user.username:
            return Response(create_error_response("You cannot follow yourself"), status=status.HTTP_400_BAD_REQUEST)

        try:
            user_to_follow = HifzyUser.objects.get(username=username)
        except HifzyUser.DoesNotExist:
            return Response(create_error_response("User not found"), status=status.HTTP_404_NOT_FOUND)

        try:
            follow = services.FollowService.follow_user(request.user.id, user_to_follow.id)

            return Response(
                create_success_response(serialize_dataclass(follow), f"You are now following {username}"),
                status=status.HTTP_201_CREATED,
            )
        except ValidationError as e:
            if "Already following" in str(e):
                return Response(
                    create_error_response(f"You are already following {username}"), status=status.HTTP_409_CONFLICT
                )
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to follow user: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class UnfollowUserView(APIView):
    """View for unfollowing a user."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Unfollow a user",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["username"],
            properties={
                "username": openapi.Schema(type=openapi.TYPE_STRING, description="Username of the user to unfollow"),
            },
        ),
        responses={
            200: openapi.Response("Successfully unfollowed user", UnfollowUserResponseSchemas.success_200),
            400: openapi.Response("Invalid request data", UnfollowUserResponseSchemas.error_400),
            401: openapi.Response("Authentication credentials not provided", UnfollowUserResponseSchemas.error_401),
            404: openapi.Response("User not found or not following this user", UnfollowUserResponseSchemas.error_404),
            500: openapi.Response("Internal server error", UnfollowUserResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Unfollow a user."""
        username = request.data.get("username")
        if not username:
            return Response(create_error_response("Username is required"), status=status.HTTP_400_BAD_REQUEST)

        try:
            user_to_unfollow = HifzyUser.objects.get(username=username)
        except HifzyUser.DoesNotExist:
            return Response(create_error_response("User not found"), status=status.HTTP_404_NOT_FOUND)

        try:
            removed = services.FollowService.unfollow_user(request.user.id, user_to_unfollow.id)

            if not removed:
                return Response(
                    create_error_response(f"You are not following {username}"), status=status.HTTP_404_NOT_FOUND
                )

            return Response(create_success_response(None, f"You have unfollowed {username}"), status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to unfollow user: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ListFollowersView(APIView):
    """View for listing all followers of the authenticated user."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="List all followers of the authenticated user",
        responses={
            200: openapi.Response("Successfully retrieved followers", FollowersListResponseSchemas.success_200),
            401: openapi.Response("Authentication credentials not provided", FollowersListResponseSchemas.error_401),
            500: openapi.Response("Internal server error", FollowersListResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get all followers of the current user."""
        try:
            followers = services.FollowService.get_user_followers(request.user.id)

            return Response(
                create_success_response(
                    {"followers": serialize_dataclass_list(followers), "count": len(followers)},
                    "Your followers retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve followers: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ListFollowingView(APIView):
    """View for listing all users that the authenticated user is following."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="List all users that the authenticated user is following",
        responses={
            200: openapi.Response("Successfully retrieved following users", FollowingListResponseSchemas.success_200),
            401: openapi.Response("Authentication credentials not provided", FollowingListResponseSchemas.error_401),
            500: openapi.Response("Internal server error", FollowingListResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get all users that the current user is following."""
        try:
            following = services.FollowService.get_user_following(request.user.id)

            return Response(
                create_success_response(
                    {"following": serialize_dataclass_list(following), "count": len(following)},
                    "Users you are following retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve following users: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class CheckFollowStatusView(APIView):
    """View for checking if the authenticated user is following a specific user."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Check if the authenticated user is following a specific user",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["username"],
            properties={
                "username": openapi.Schema(type=openapi.TYPE_STRING, description="Username to check follow status"),
            },
        ),
        responses={
            200: openapi.Response("Successfully checked follow status", FollowStatusResponseSchemas.success_200),
            400: openapi.Response("Invalid request data", FollowStatusResponseSchemas.error_400),
            401: openapi.Response("Authentication credentials not provided", FollowStatusResponseSchemas.error_401),
            404: openapi.Response("User not found", FollowStatusResponseSchemas.error_404),
            500: openapi.Response("Internal server error", FollowStatusResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Check if the current user is following a specific user."""
        username = request.data.get("username")
        if not username:
            return Response(create_error_response("Username is required"), status=status.HTTP_400_BAD_REQUEST)

        try:
            user_to_check = HifzyUser.objects.get(username=username)
        except HifzyUser.DoesNotExist:
            return Response(create_error_response("User not found"), status=status.HTTP_404_NOT_FOUND)

        try:
            is_following = services.FollowService.is_following(request.user.id, user_to_check.id)

            return Response(
                create_success_response(
                    {"username": username, "is_following": is_following}, "Follow status checked successfully"
                ),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Failed to check follow status: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# TODO REFACTOR THIS CODE TO MAKE IT MORE CLEAN AND ELEGANT
class GlobalLeaderboardView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get the global leaderboard of users based on total XP from all surahs",
        manual_parameters=[
            openapi.Parameter(
                "limit",
                openapi.IN_QUERY,
                description="Number of users to return (default: 10)",
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                "Successfully retrieved global leaderboard", GlobalLeaderboardResponseSchemas.success_200
            ),
            401: openapi.Response(
                "Authentication credentials not provided", GlobalLeaderboardResponseSchemas.error_401
            ),
            500: openapi.Response("Internal server error", GlobalLeaderboardResponseSchemas.error_500),
        },
    )
    def post(self, request):
        try:
            # Get the limit parameter, default to 10
            limit = int(request.query_params.get("limit", 10))

            # Use the service layer to get global leaderboard
            leaderboard_entries = services.LeaderboardService.get_global_leaderboard(limit)

            # Convert to the expected format
            leaderboard_data = serialize_dataclass_list(leaderboard_entries)

            # Find current user's rank in the full leaderboard
            current_user_rank = None
            full_leaderboard = services.LeaderboardService.get_global_leaderboard(
                1000
            )  # Get more entries to find user rank
            for i, entry in enumerate(full_leaderboard):
                if entry.user.id == request.user.id:
                    current_user_rank = i + 1
                    break

            return Response(
                create_success_response(
                    {"leaderboard": leaderboard_data, "user_rank": current_user_rank},
                    "Global leaderboard retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve global leaderboard: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class FollowedUsersLeaderboardView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get the leaderboard of users that the current user follows, based on total XP from all surahs",
        manual_parameters=[
            openapi.Parameter(
                "limit",
                openapi.IN_QUERY,
                description="Number of users to return (default: 10)",
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                "Successfully retrieved followed users leaderboard", FollowedUsersLeaderboardResponseSchemas.success_200
            ),
            401: openapi.Response(
                "Authentication credentials not provided", FollowedUsersLeaderboardResponseSchemas.error_401
            ),
            500: openapi.Response("Internal server error", FollowedUsersLeaderboardResponseSchemas.error_500),
        },
    )
    def post(self, request):
        try:
            # Get the limit parameter, default to 10
            limit = int(request.query_params.get("limit", 10))

            # Use the service layer to get followed users leaderboard
            leaderboard_entries = services.LeaderboardService.get_followed_users_leaderboard(request.user.id, limit)

            # Convert to the expected format
            leaderboard_data = serialize_dataclass_list(leaderboard_entries)

            # Find current user's rank in the followed users leaderboard
            current_user_rank = None
            full_leaderboard = services.LeaderboardService.get_followed_users_leaderboard(request.user.id, 1000)
            for i, entry in enumerate(full_leaderboard):
                if entry.user.id == request.user.id:
                    current_user_rank = i + 1
                    break

            return Response(
                create_success_response(
                    {"leaderboard": leaderboard_data, "user_rank": current_user_rank},
                    "Followed users leaderboard retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve followed users leaderboard: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

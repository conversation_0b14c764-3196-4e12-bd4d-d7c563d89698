{"name": "hifzy", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-oauth/google": "^0.12.1", "axios": "^1.7.7", "i18next": "^23.16.5", "i18next-browser-languagedetector": "^8.0.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-card-flip": "^1.2.3", "react-dom": "^18.3.1", "react-i18next": "^15.1.1", "react-loader-spinner": "^6.1.6", "react-router-dom": "^6.26.0", "react-toastify": "^10.0.5", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.8.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.8.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "vite": "^5.4.0"}}
.logo-flex {
  display: flex;
  justify-content: center;
}

.logo {
  width: 120px;
  height: auto;
}

.left-panel-flex {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-top: 25px;
}

.left-panel-buttons {
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  row-gap: 5px;
}

.left-panel-button-with-pic {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  padding-left: 20px;
  font-weight: 600;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-right: 10px;
  border: 1px solid white;
  cursor: pointer;
}

.left-panel-button-with-pic:hover {
  border: 1px solid #97cb9c;
  border-radius: 30px;
  width: 100%;
  background-color: #97cb9c26;
}

.left-panel-button-with-pic button {
  margin-left: 20px;
}

.horizontal-left-menu-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 215px;
}

.border-line {
  width: 2px;
  background-color: #97cb9c;
  height: 98vh;
}

.active-left-menu {
  border: 1px solid #97cb9c;
  border-radius: 30px;
  width: 100%;
  background-color: #97cb9c26;
}
"""
Repository layer

This module contains repository classes that abstract database operations
and convert Django models to transport objects for game-related data.
"""

from typing import List, Optional, Sequence
from django.db.models import QuerySet
from django.contrib.auth import get_user_model
from . import models
from . import transports
from accounts.repositories import Users
from core.transports import League
from accounts.models import HifzyUser

User = get_user_model()


class Leaderboards:
    """Repository for leaderboard operations."""

    @staticmethod
    def get_global_leaderboard(limit: int = 10) -> Sequence[transports.LeaderboardEntry]:
        """Get global leaderboard."""
        queryset = HifzyUser.objects.order_by("-total_xp")[:limit]

        leaderboard = []
        for rank, user in enumerate(queryset, 1):
            user_transport = Users._make_compact_user_transport(user)
            entry = transports.LeaderboardEntry(user=user_transport, total_xp=user.total_xp, rank=rank)
            leaderboard.append(entry)

        return leaderboard

    @staticmethod
    def get_followed_users_leaderboard(user_id: int, limit: int = 10) -> Sequence[transports.LeaderboardEntry]:
        """Get leaderboard for users that the given user follows."""
        # Get IDs of users that the current user follows
        following_ids = list(
            models.UserFollowMM.objects.filter(follower_id=user_id).values_list("following_id", flat=True)
        )

        # Include the current user in the leaderboard
        following_ids.append(user_id)

        queryset = HifzyUser.objects.filter(id__in=following_ids).order_by("-total_xp")[:limit]

        leaderboard = []
        for rank, user in enumerate(queryset, 1):
            user_transport = Users._make_compact_user_transport(user)
            entry = transports.LeaderboardEntry(user=user_transport, total_xp=user.total_xp, rank=rank)
            leaderboard.append(entry)

        return leaderboard


class UserFollowRepository:
    """Repository for UserFollowMM operations."""

    @staticmethod
    def _make_user_follow_queryset() -> QuerySet[models.UserFollowMM]:
        """Create optimized queryset for user-follow operations."""
        return models.UserFollowMM.objects.select_related("follower", "following")

    @staticmethod
    def _make_user_follow_transport(user_follow: models.UserFollowMM) -> transports.UserFollowRelation:
        """Convert Django user-follow model to transport object."""
        follower_transport = Users._make_compact_user_transport(user_follow.follower)
        following_transport = Users._make_compact_user_transport(user_follow.following)
        return transports.UserFollowRelation(
            id=user_follow.id,
            follower=follower_transport,
            following=following_transport,
            created_at=user_follow.created_at,
        )

    @staticmethod
    def get_user_followers(user_id: int) -> List[transports.UserFollowRelation]:
        """Get all followers for a user."""
        user_follows = (
            UserFollowRepository._make_user_follow_queryset().filter(following_id=user_id).order_by("-created_at")
        )
        return [UserFollowRepository._make_user_follow_transport(uf) for uf in user_follows]

    @staticmethod
    def get_user_following(user_id: int) -> List[transports.UserFollowRelation]:
        """Get all users that a user is following."""
        user_follows = (
            UserFollowRepository._make_user_follow_queryset().filter(follower_id=user_id).order_by("-created_at")
        )
        return [UserFollowRepository._make_user_follow_transport(uf) for uf in user_follows]

    @staticmethod
    def is_following(follower_id: int, following_id: int) -> bool:
        """Check if a user is following another user."""
        return (
            UserFollowRepository._make_user_follow_queryset()
            .filter(follower_id=follower_id, following_id=following_id)
            .exists()
        )


class LeagueRepository:
    """Repository for League operations."""

    @staticmethod
    def _make_league_queryset() -> QuerySet[models.League]:
        """Create optimized queryset for league operations."""
        return models.League.objects.all()

    @staticmethod
    def _make_league_transport(league: models.League) -> League:
        """Convert Django league model to transport object."""
        return League(
            id=league.id,
            rank=league.rank,
            end_time=league.end_time,
            start_time=league.start_time,
            icon=league.icon,
            name=league.name,
        )

    @staticmethod
    def get_league_by_id(league_id: int) -> Optional[League]:
        """Get league by ID."""
        try:
            league = LeagueRepository._make_league_queryset().get(id=league_id)
            return LeagueRepository._make_league_transport(league)
        except models.League.DoesNotExist:
            return None

    @staticmethod
    def get_all_leagues() -> List[League]:
        """Get all leagues."""
        leagues = LeagueRepository._make_league_queryset().order_by("start_time")
        return [LeagueRepository._make_league_transport(league) for league in leagues]

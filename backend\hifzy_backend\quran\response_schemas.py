"""
Response schema serializers for the quran app.

This module provides response serializers for all Quran-related endpoints
to ensure comprehensive API documentation in Swagger/OpenAPI.
"""

from rest_framework import serializers
from core.response_schemas import (
    BaseSuccessResponseSerializer,
    BaseErrorResponseSerializer,
    SurahBasicInfoSerializer,
    AyahBasicInfoSerializer,
    ReaderInfoSerializer,
    LanguageInfoSerializer,
    LanguageMetadataSerializer,
)


# Enhanced serializers for Quran data
class SurahInfoDetailSerializer(SurahBasicInfoSerializer):
    """Detailed Surah information serializer."""
    
    revelation_place = serializers.CharField(
        allow_null=True,
        help_text="Place where the surah was revealed (Mecca/Medina)"
    )
    revelation_order = serializers.IntegerField(
        allow_null=True,
        help_text="Order in which the surah was revealed"
    )
    bismillah_pre = serializers.BooleanField(
        help_text="Whether the surah starts with Bismillah"
    )
    pages = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="Page numbers in the Quran where this surah appears"
    )


class AyahDetailSerializer(AyahBasicInfoSerializer):
    """Detailed Ayah information serializer with audio and image."""

    audio_url = serializers.URLField(
        allow_null=True,
        help_text="URL to audio file for this ayah"
    )
    image_url = serializers.URLField(allow_null=True, help_text="Absolute URL to the ayah image for visual display")
    global_number = serializers.IntegerField(
        allow_null=True,
        help_text="Global ayah number in the entire Quran"
    )
    page_number = serializers.IntegerField(
        allow_null=True,
        help_text="Page number in the Quran where this ayah appears"
    )
    juz_number = serializers.IntegerField(
        allow_null=True,
        help_text="Juz (Para) number where this ayah appears"
    )


# Surah information response schemas
class SurahInfoDataSerializer(serializers.Serializer):
    """Data structure for surah information response."""
    
    surah_info = SurahInfoDetailSerializer(help_text="Basic surah information")
    ayahs = AyahDetailSerializer(many=True, help_text="List of ayahs in the surah with audio URLs and image URLs")
    language_info = LanguageMetadataSerializer(
        required=False,
        help_text="Language selection metadata"
    )


class SurahInfoSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful surah information retrieval."""
    
    data = SurahInfoDataSerializer(help_text="Surah information data")


# Ayah text response schemas
class AyahTextDataSerializer(serializers.Serializer):
    """Data structure for ayah text response."""

    surah_id = serializers.IntegerField(help_text="Surah ID")
    ayah_id = serializers.IntegerField(help_text="Ayah ID within the surah")
    arabic_text = serializers.CharField(help_text="Arabic text of the ayah")
    transliteration = serializers.CharField(
        allow_null=True,
        help_text="Transliteration of the ayah"
    )
    translation = serializers.CharField(
        allow_null=True,
        help_text="Translation of the ayah"
    )
    globalNumber = serializers.IntegerField(
        allow_null=True,
        help_text="Global ayah number in the entire Quran"
    )
    language_info = LanguageMetadataSerializer(
        required=False,
        help_text="Language selection metadata"
    )


class AyahTextSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful ayah text retrieval."""
    
    data = AyahTextDataSerializer(help_text="Ayah text data")


# Available readers response schemas
class AvailableReadersDataSerializer(serializers.Serializer):
    """Data structure for available readers response."""
    
    readers = ReaderInfoSerializer(
        many=True,
        help_text="List of all available audio readers in the system"
    )


class AvailableReadersSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful available readers retrieval."""
    
    data = AvailableReadersDataSerializer(help_text="Available readers data")


# Available languages response schemas
class AvailableLanguagesDataSerializer(serializers.Serializer):
    """Data structure for available languages response."""
    
    languages = LanguageInfoSerializer(
        many=True,
        help_text="List of all available languages with translation/transliteration support"
    )


class AvailableLanguagesSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful available languages retrieval."""
    
    data = AvailableLanguagesDataSerializer(help_text="Available languages data")


# Error response schemas
class QuranValidationErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for Quran validation errors."""
    pass


class QuranNotFoundErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for Quran not found errors."""
    pass


class QuranInternalServerErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for Quran internal server errors."""
    pass


# Endpoint-specific response schema collections
class SurahInfoResponseSchemas:
    """Response schemas for surah information endpoint."""
    
    success_200 = SurahInfoSuccessResponseSerializer
    error_400 = QuranValidationErrorResponseSerializer
    error_404 = QuranNotFoundErrorResponseSerializer
    error_500 = QuranInternalServerErrorResponseSerializer


class AyahTextResponseSchemas:
    """Response schemas for ayah text endpoint."""
    
    success_200 = AyahTextSuccessResponseSerializer
    error_400 = QuranValidationErrorResponseSerializer
    error_404 = QuranNotFoundErrorResponseSerializer
    error_500 = QuranInternalServerErrorResponseSerializer


class AvailableReadersResponseSchemas:
    """Response schemas for available readers endpoint."""
    
    success_200 = AvailableReadersSuccessResponseSerializer
    error_500 = QuranInternalServerErrorResponseSerializer


class AvailableLanguagesResponseSchemas:
    """Response schemas for available languages endpoint."""
    
    success_200 = AvailableLanguagesSuccessResponseSerializer
    error_500 = QuranInternalServerErrorResponseSerializer


# Example response data for documentation
class SurahInfoExampleData:
    """Example response data for surah information endpoint."""
    
    @staticmethod
    def get_example():
        return {
            "success": True,
            "message": "Surah information with audio recordings retrieved successfully",
            "data": {
                "surah_info": {
                    "surah_id": 1,
                    "name": "Al-Fatihah",
                    "name_simple": "Al-Fatihah",
                    "arabic_name": "الفاتحة",
                    "verses_count": 7,
                    "is_pre_bismillah": False,
                    "is_meccan": True,
                    "is_medinian": False,
                },
                "ayahs": [
                    {
                        "surah_id": 1,
                        "ayah_id": 1,
                        "arabic_text": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
                        "transliteration": "Bismillahir-Rahmanir-Raheem",
                        "translation": "In the name of Allah, the Most Gracious, the Most Merciful",
                        "audio_url": "https://example.com/audio/001001.mp3",
                        "global_number": 1,
                        "page_number": 1,
                        "juz_number": 1,
                    }
                ],
                "language_info": {
                    "requested_language": "en",
                    "used_language": "en",
                    "fallback_applied": False,
                    "available_languages": ["en", "id", "ar"],
                },
            },
        }


class AyahTextExampleData:
    """Example response data for ayah text endpoint."""
    
    @staticmethod
    def get_example():
        return {
            "success": True,
            "message": "Ayah text retrieved successfully",
            "data": {
                "surah_id": 1,
                "ayah_id": 1,
                "arabic_text": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
                "transliteration": "Bismillahir-Rahmanir-Raheem",
                "translation": "In the name of Allah, the Most Gracious, the Most Merciful",
                "globalNumber": 1,
                "language_info": {
                    "requested_language": "en",
                    "used_language": "en",
                    "fallback_applied": False,
                    "available_languages": ["en", "id", "ar"],
                },
            },
        }


# Arabic Letter response schemas
class ArabicLetterDataSerializer(serializers.Serializer):
    """Data structure for Arabic letter response."""

    id = serializers.IntegerField(help_text="Arabic letter ID")
    title = serializers.CharField(help_text="Arabic letter title/character")
    transcription = serializers.CharField(help_text="Letter transcription/pronunciation guide")
    audio_url = serializers.URLField(
        allow_null=True, required=False, help_text="URL to the audio file for letter pronunciation"
    )


class ArabicLetterSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful Arabic letter retrieval."""

    data = ArabicLetterDataSerializer(help_text="Arabic letter data")


class ArabicLettersListDataSerializer(serializers.Serializer):
    """Data structure for Arabic letters list response."""

    letters = ArabicLetterDataSerializer(many=True, help_text="List of Arabic letters")


class ArabicLettersListSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful Arabic letters list retrieval."""

    data = ArabicLettersListDataSerializer(help_text="Arabic letters list data")


# Arabic Letter response schema collections
class ArabicLetterResponseSchemas:
    """Response schemas for Arabic letter endpoints."""

    success_200 = ArabicLetterSuccessResponseSerializer
    list_success_200 = ArabicLettersListSuccessResponseSerializer
    error_400 = BaseErrorResponseSerializer
    error_404 = BaseErrorResponseSerializer
    error_500 = BaseErrorResponseSerializer


class ArabicLetterExampleData:
    """Example response data for Arabic letter endpoints."""

    @staticmethod
    def get_single_letter_example():
        return {
            "success": True,
            "message": "Arabic letter retrieved successfully",
            "data": {
                "id": 1,
                "title": "ا",
                "transcription": "alif",
                "audio_url": "https://example.com/audio/letters/alif.mp3",
            },
        }

    @staticmethod
    def get_letters_list_example():
        return {
            "success": True,
            "message": "Arabic letters retrieved successfully",
            "data": {
                "letters": [
                    {
                        "id": 1,
                        "title": "ا",
                        "transcription": "alif",
                        "audio_url": "https://example.com/audio/letters/alif.mp3",
                    },
                    {
                        "id": 2,
                        "title": "ب",
                        "transcription": "ba",
                        "audio_url": "https://example.com/audio/letters/ba.mp3",
                    },
                ]
            },
        }


# Surah Preview Response Schemas
class AyahPreviewSerializer(serializers.Serializer):
    """Serializer for ayah preview information."""

    ayah_number = serializers.IntegerField(help_text="Ayah number within the surah")
    image_url = serializers.URLField(allow_null=True, help_text="URL to ayah image")


class SurahPreviewDataSerializer(serializers.Serializer):
    """Data structure for surah preview response."""

    surah_id = serializers.IntegerField(help_text="Surah ID")
    surah_title = serializers.CharField(help_text="Surah title in requested language")
    arabic_name = serializers.CharField(help_text="Original Arabic name of the surah")
    ayah_previews = AyahPreviewSerializer(many=True, help_text="First 3 ayah previews")
    total_ayahs_in_surah = serializers.IntegerField(help_text="Total number of ayahs in the surah")
    returned_ayahs_count = serializers.IntegerField(help_text="Number of ayah previews returned")
    is_pre_bismillah = serializers.BooleanField(help_text="Whether the surah has a pre-bismillah")
    is_meccan = serializers.BooleanField(help_text="Whether the surah was revealed in Mecca")
    is_medinian = serializers.BooleanField(help_text="Whether the surah was revealed in Medina")
    position_in_order = serializers.IntegerField(
        help_text="Position in the specified ordering system (1-114)", required=False, allow_null=True
    )
    learning_position = serializers.IntegerField(
        help_text="Position in pedagogical learning sequence (1-114)", required=False
    )
    language_info = LanguageMetadataSerializer(help_text="Language metadata", required=False)


class SurahPreviewSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful surah preview retrieval."""

    data = SurahPreviewDataSerializer(help_text="Surah preview data")


class AllSurahsPreviewDataSerializer(serializers.Serializer):
    """Data structure for all surahs preview response."""

    surahs = SurahPreviewDataSerializer(many=True, help_text="Preview data for all 114 surahs")
    total_surahs_count = serializers.IntegerField(help_text="Total number of surahs (should be 114)")
    ordering = serializers.CharField(help_text="Ordering method used ('pedagogical' or 'traditional')")
    language_info = LanguageMetadataSerializer(help_text="Language metadata")


class AllSurahsPreviewSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful all surahs preview retrieval."""

    data = AllSurahsPreviewDataSerializer(help_text="All surahs preview data")


# Response Schema Collections
class SurahPreviewResponseSchemas:
    """Response schemas for surah preview endpoint."""

    success_200 = SurahPreviewSuccessResponseSerializer
    error_400 = BaseErrorResponseSerializer
    error_401 = BaseErrorResponseSerializer
    error_404 = BaseErrorResponseSerializer
    error_500 = BaseErrorResponseSerializer


class AllSurahsPreviewResponseSchemas:
    """Response schemas for all surahs preview endpoint."""

    success_200 = AllSurahsPreviewSuccessResponseSerializer
    error_401 = BaseErrorResponseSerializer
    error_500 = BaseErrorResponseSerializer

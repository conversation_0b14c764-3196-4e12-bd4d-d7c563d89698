"""
Core utility functions for the hifzy_backend project.

This module contains common utility functions used across the application,
including serialization helpers and common data transformations.
"""

from dataclasses import asdict, is_dataclass
from datetime import datetime
from typing import Any, Dict, List, Union
from rest_framework.exceptions import ValidationError
import json


def serialize_dataclass(val: Any) -> Dict[str, Any]:
    """
    Serialize a dataclass instance to a dictionary.

    Args:
        val: The dataclass instance to serialize

    Returns:
        Dictionary representation of the dataclass

    Raises:
        ValidationError: If the value is not a dataclass instance
    """
    if not is_dataclass(val) or isinstance(val, type):
        raise ValidationError(f"Not a dataclass, got type: {type(val)}")

    result = asdict(val)

    # Convert datetime objects to ISO format strings
    def convert_datetime(obj):
        if isinstance(obj, dict):
            return {k: convert_datetime(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_datetime(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        return obj

    return convert_datetime(result)


def serialize_dataclass_list(dataclass_list: List[Any]) -> List[Dict[str, Any]]:
    """
    Serialize a list of dataclass instances to a list of dictionaries.

    Args:
        dataclass_list: List of dataclass instances to serialize

    Returns:
        List of dictionary representations
    """
    return [serialize_dataclass(item) for item in dataclass_list]


def create_paginated_response(
    items: List[Any], page: int = 1, per_page: int = 20, total_count: int = None
) -> Dict[str, Any]:
    """
    Create a paginated response structure.

    Args:
        items: List of items (already paginated)
        page: Current page number
        per_page: Items per page
        total_count: Total number of items (if None, calculated from items length)

    Returns:
        Paginated response dictionary
    """
    if total_count is None:
        total_count = len(items)

    total_pages = (total_count + per_page - 1) // per_page
    has_next = page < total_pages
    has_prev = page > 1

    return {
        "items": serialize_dataclass_list(items) if items and is_dataclass(items[0]) else items,
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total_count": total_count,
            "total_pages": total_pages,
            "has_next": has_next,
            "has_prev": has_prev,
            "next_page": page + 1 if has_next else None,
            "prev_page": page - 1 if has_prev else None,
        },
    }


def create_success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """
    Create a standardized success response.

    Args:
        data: Response data (can be dataclass, list, or dict)
        message: Success message

    Returns:
        Standardized success response
    """
    response = {"success": True, "message": message}

    if data is not None:
        if is_dataclass(data):
            response["data"] = serialize_dataclass(data)
        elif isinstance(data, list) and data and is_dataclass(data[0]):
            response["data"] = serialize_dataclass_list(data)
        else:
            response["data"] = data

    return response


def create_error_response(message: str, errors: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Create a standardized error response.

    Args:
        message: Error message
        errors: Detailed error information

    Returns:
        Standardized error response
    """
    response = {"success": False, "message": message}

    if errors:
        response["errors"] = errors

    return response


def calculate_user_level(total_progress: int) -> int:
    """
    Calculate user level based on total progress points.

    Args:
        total_progress: Total progress points

    Returns:
        User level (minimum 1)
    """
    if total_progress <= 0:
        return 1

    # Every 1000 points = 1 level
    return max(1, total_progress // 1000 + 1)


def calculate_completion_percentage(completed: int, total: int) -> float:
    """
    Calculate completion percentage.

    Args:
        completed: Number of completed items
        total: Total number of items

    Returns:
        Completion percentage (0.0 to 100.0)
    """
    if total <= 0:
        return 0.0

    percentage = (completed / total) * 100
    return round(percentage, 2)


def format_arabic_number(number: int) -> str:
    """
    Convert a number to Arabic-Indic numerals.

    Args:
        number: Integer to convert

    Returns:
        String with Arabic-Indic numerals
    """
    arabic_digits = "٠١٢٣٤٥٦٧٨٩"
    western_digits = "0123456789"

    number_str = str(number)
    arabic_number = ""

    for digit in number_str:
        if digit in western_digits:
            arabic_number += arabic_digits[int(digit)]
        else:
            arabic_number += digit

    return arabic_number


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename by removing or replacing invalid characters.

    Args:
        filename: Original filename

    Returns:
        Sanitized filename
    """
    import re

    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', "_", filename)

    # Remove leading/trailing spaces and dots
    filename = filename.strip(" .")

    # Ensure filename is not empty
    if not filename:
        filename = "unnamed_file"

    # Limit length
    if len(filename) > 255:
        name, ext = filename.rsplit(".", 1) if "." in filename else (filename, "")
        max_name_length = 255 - len(ext) - 1 if ext else 255
        filename = name[:max_name_length] + ("." + ext if ext else "")

    return filename


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """
    Truncate text to a maximum length with optional suffix.

    Args:
        text: Text to truncate
        max_length: Maximum length including suffix
        suffix: Suffix to add when truncating

    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text

    if len(suffix) >= max_length:
        return text[:max_length]

    return text[: max_length - len(suffix)] + suffix


def get_client_ip(request) -> str:
    """
    Get the client IP address from a Django request.

    Args:
        request: Django request object

    Returns:
        Client IP address
    """
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip or "unknown"


def generate_cache_key(*args, **kwargs) -> str:
    """
    Generate a cache key from arguments.

    Args:
        *args: Positional arguments
        **kwargs: Keyword arguments

    Returns:
        Cache key string
    """
    import hashlib

    # Convert all arguments to strings
    key_parts = [str(arg) for arg in args]
    key_parts.extend([f"{k}:{v}" for k, v in sorted(kwargs.items())])

    # Create a hash of the combined key parts
    key_string = ":".join(key_parts)
    return hashlib.md5(key_string.encode()).hexdigest()


def batch_process(items: List[Any], batch_size: int = 100):
    """
    Process items in batches.

    Args:
        items: List of items to process
        batch_size: Size of each batch

    Yields:
        Batches of items
    """
    for i in range(0, len(items), batch_size):
        yield items[i : i + batch_size]

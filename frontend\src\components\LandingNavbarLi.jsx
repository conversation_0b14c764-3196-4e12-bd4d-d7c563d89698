import { useState } from "react";
function LandingNavbarLi(porps) {
  const [isHover, setIsHover] = useState(false);

  const handleMouseEnter = () => {
    setIsHover(true);
  };

  const handleMouseLeave = () => {
    setIsHover(false);
  };
  return (
    <>
      <li
        href="#"
        style={{
          fontWeight: 600,
          transform: isHover ? "scale(1.05)" : "scale(1)",
          transitionDuration: "0.2s",
          cursor: "pointer",
          minWidth: "fit-content",
        }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {porps.title}
      </li>
    </>
  );
}

export default LandingNavbarLi;

import generatedStats from "src/assets/generated-statistics.png";

function ProfileStatBlock() {
  return (
    <div
      style={{
        height: "100%",
        display: "flex",
        justifyContent: "center",
        flexDirection: "column",
      }}
    >
      <div
        style={{
          backgroundColor: "#fbfbfb",
          filter: "drop-shadow(6px 6px 15px rgba(134, 134, 134, 0.2));",
          borderRadius: "30px",
          padding: "20px",
          display: "flex",
          justifyContent: "center",
        }}
      >
        <img src={generatedStats} alt="" style={{ width: "100%" }} />
      </div>
    </div>
  );
}

export default ProfileStatBlock;

import "src/components/LevelsNet/LevelsNet.css";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
function LevelsNet() {
  const navigate = useNavigate();
  const handleLevelPick = (event) => {
    const index = event.target.getAttribute("data-index");
    navigate(`/games/${index}/1`);
  };

  useEffect(() => {
    const container = document.querySelector(".middle-image-container");
    const svgElement = document.querySelector(".main-svg");

    let isDragging = false;
    let startX, startY;
    let offsetX = 0,
      offsetY = 0;
    let scale = 1;

    container.addEventListener("mousedown", (e) => {
      isDragging = true;
      startX = e.clientX - offsetX;
      startY = e.clientY - offsetY;
    });

    container.addEventListener("mousemove", (e) => {
      if (isDragging) {
        offsetX = e.clientX - startX;
        offsetY = e.clientY - startY;
        updateTransform();
      }
    });

    container.addEventListener("mouseup", () => {
      isDragging = false;
    });

    container.addEventListener("mouseleave", () => {
      isDragging = false;
    });

    container.addEventListener("wheel", (e) => {
      e.preventDefault();
      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      scale += delta;
      scale = Math.min(Math.max(0.125, scale), 4);
      updateTransform();
    });

    function updateTransform() {
      svgElement.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${scale})`;
    }
    return () => {
      container.removeEventListener("mousedown", () => {});
      container.removeEventListener("mousemove", () => {});
      container.removeEventListener("mouseup", () => {});
      container.removeEventListener("mouseleave", () => {});
      container.removeEventListener("wheel", () => {});
    };
  }, []);

  return (
    <>
      <div className="middle-image-container">
        <svg
          width="1084"
          height="1000"
          viewBox="0 0 1084 1000"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="main-svg"
        >
          <path
            d="M862.5 246.5C862.5 255.815 854.517 263.5 844.5 263.5C834.483 263.5 826.5 255.815 826.5 246.5C826.5 237.185 834.483 229.5 844.5 229.5C854.517 229.5 862.5 237.185 862.5 246.5Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="107"
            className="level-done"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M824.5 271.5C824.5 280.815 816.517 288.5 806.5 288.5C796.483 288.5 788.5 280.815 788.5 271.5C788.5 262.185 796.483 254.5 806.5 254.5C816.517 254.5 824.5 262.185 824.5 271.5Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="103"
            className="level-done"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M989.335 532.712C997.541 537.121 1000.53 547.791 995.79 556.614C991.049 565.438 980.501 568.833 972.296 564.424C964.09 560.015 961.099 549.345 965.84 540.521C970.581 531.698 981.129 528.302 989.335 532.712Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="19"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M180.691 569.043C188.923 577.232 188.676 591.015 179.905 599.832C171.135 608.649 157.353 608.967 149.121 600.778C140.889 592.589 141.135 578.806 149.906 569.989C158.677 561.172 172.459 560.854 180.691 569.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="87"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M199.691 519.043C207.923 527.232 207.676 541.015 198.905 549.832C190.135 558.649 176.353 558.967 168.121 550.778C159.889 542.589 160.135 528.806 168.906 519.989C177.677 511.172 191.459 510.854 199.691 519.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="86"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M214.691 468.043C222.923 476.232 222.676 490.015 213.905 498.832C205.135 507.649 191.353 507.967 183.121 499.778C174.889 491.589 175.135 477.806 183.906 468.989C192.677 460.172 206.459 459.854 214.691 468.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="85"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M239.691 421.043C247.923 429.232 247.676 443.015 238.905 451.832C230.135 460.649 216.353 460.967 208.121 452.778C199.889 444.589 200.135 430.806 208.906 421.989C217.677 413.172 231.459 412.854 239.691 421.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="84"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M283.691 394.043C291.923 402.232 291.676 416.015 282.905 424.832C274.135 433.649 260.353 433.967 252.121 425.778C243.889 417.589 244.135 403.806 252.906 394.989C261.677 386.172 275.459 385.854 283.691 394.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="83"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M282.691 443.043C290.923 451.232 290.676 465.015 281.905 473.832C273.135 482.649 259.353 482.967 251.121 474.778C242.889 466.589 243.135 452.806 251.906 443.989C260.677 435.172 274.459 434.854 282.691 443.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="82"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M276.691 494.043C284.923 502.232 284.676 516.015 275.905 524.832C267.135 533.649 253.353 533.967 245.121 525.778C236.889 517.589 237.135 503.806 245.906 494.989C254.677 486.172 268.459 485.854 276.691 494.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="81"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M268.017 595.612C274.52 605.232 271.626 618.71 261.322 625.674C251.019 632.639 237.433 630.3 230.931 620.68C224.428 611.06 227.322 597.582 237.626 590.617C247.93 583.653 261.515 585.992 268.017 595.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="79"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M260.017 644.612C266.52 654.232 263.626 667.71 253.322 674.674C243.019 681.639 229.433 679.3 222.931 669.68C216.428 660.06 219.322 646.582 229.626 639.617C239.93 632.653 253.515 634.992 260.017 644.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="78"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M247.017 741.612C253.52 751.232 250.626 764.71 240.322 771.674C230.019 778.639 216.433 776.3 209.931 766.68C203.428 757.06 206.322 743.582 216.626 736.617C226.93 729.653 240.515 731.992 247.017 741.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="76"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M271.691 542.043C279.923 550.232 279.676 564.015 270.905 572.832C262.135 581.649 248.353 581.967 240.121 573.778C231.889 565.589 232.135 551.806 240.906 542.989C249.677 534.172 263.459 533.854 271.691 542.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="80"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M251.039 692.647C257.512 702.287 254.577 715.756 244.252 722.689C233.927 729.622 220.349 727.241 213.876 717.601C207.403 707.962 210.338 694.492 220.663 687.559C230.988 680.627 244.566 683.007 251.039 692.647Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="77"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M240.017 790.612C246.52 800.232 243.626 813.71 233.322 820.674C223.019 827.639 209.433 825.3 202.931 815.68C196.428 806.06 199.322 792.582 209.626 785.617C219.93 778.653 233.515 780.992 240.017 790.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="75"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M239.017 841.612C245.52 851.232 242.626 864.71 232.322 871.674C222.019 878.639 208.433 876.3 201.931 866.68C195.428 857.06 198.322 843.582 208.626 836.617C218.93 829.653 232.515 831.992 239.017 841.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="74"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M899.017 532.612C905.52 542.232 902.626 555.71 892.322 562.674C882.019 569.639 868.433 567.3 861.931 557.68C855.428 548.06 858.322 534.582 868.626 527.617C878.93 520.653 892.515 522.992 899.017 532.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="29"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M256.017 894.612C262.52 904.232 259.626 917.71 249.322 924.674C239.019 931.639 225.433 929.3 218.931 919.68C212.428 910.06 215.322 896.582 225.626 889.617C235.93 882.653 249.515 884.992 256.017 894.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="73"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M292.017 928.612C298.52 938.232 295.626 951.71 285.322 958.674C275.019 965.639 261.433 963.3 254.931 953.68C248.428 944.06 251.322 930.582 261.626 923.617C271.93 916.653 285.515 918.992 292.017 928.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="72"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M384.017 954.612C390.52 964.232 387.626 977.71 377.322 984.674C367.019 991.639 353.433 989.3 346.931 979.68C340.428 970.06 343.322 956.582 353.626 949.617C363.93 942.653 377.515 944.992 384.017 954.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="70"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M432.017 945.612C438.52 955.232 435.626 968.71 425.322 975.674C415.019 982.639 401.433 980.3 394.931 970.68C388.428 961.06 391.322 947.582 401.626 940.617C411.93 933.653 425.515 935.992 432.017 945.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="69"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M472.017 917.612C478.52 927.232 475.626 940.71 465.322 947.674C455.019 954.639 441.433 952.3 434.931 942.68C428.428 933.06 431.322 919.582 441.626 912.617C451.93 905.653 465.515 907.992 472.017 917.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="68"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M496.017 875.612C502.52 885.232 499.626 898.71 489.322 905.674C479.019 912.639 465.433 910.3 458.931 900.68C452.428 891.06 455.322 877.582 465.626 870.617C475.93 863.653 489.515 865.992 496.017 875.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="67"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M514.017 829.612C520.52 839.232 517.626 852.71 507.322 859.674C497.019 866.639 483.433 864.3 476.931 854.68C470.428 845.06 473.322 831.582 483.626 824.617C493.93 817.653 507.515 819.992 514.017 829.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="66"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M518.394 782.372C524.2 792.428 520.36 805.668 509.59 811.886C498.819 818.104 485.433 814.81 479.628 804.754C473.822 794.698 477.662 781.459 488.432 775.24C499.203 769.022 512.589 772.316 518.394 782.372Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="65"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M517.017 734.067C523.52 743.687 520.626 757.165 510.322 764.13C500.019 771.094 486.433 768.755 479.931 759.135C473.428 749.515 476.322 736.037 486.626 729.072C496.93 722.108 510.515 724.447 517.017 734.067Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="64"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M517.017 681.612C523.52 691.232 520.626 704.71 510.322 711.674C500.019 718.639 486.433 716.3 479.931 706.68C473.428 697.06 476.322 683.582 486.626 676.617C496.93 669.653 510.515 671.992 517.017 681.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="63"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M518.017 629.612C524.52 639.232 521.626 652.71 511.322 659.674C501.019 666.639 487.433 664.3 480.931 654.68C474.428 645.06 477.322 631.582 487.626 624.617C497.93 617.653 511.515 619.992 518.017 629.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="62"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M517.017 577.194C523.52 586.814 520.626 600.292 510.322 607.256C500.019 614.221 486.433 611.882 479.931 602.262C473.428 592.642 476.322 579.164 486.626 572.199C496.93 565.235 510.515 567.574 517.017 577.194Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="61"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M517.017 524.903C523.52 534.523 520.626 548.001 510.322 554.965C500.019 561.93 486.433 559.591 479.931 549.971C473.428 540.351 476.322 526.873 486.626 519.908C496.93 512.944 510.515 515.283 517.017 524.903Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="60"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M522.017 417.612C528.52 427.232 525.626 440.71 515.322 447.674C505.019 454.639 491.433 452.3 484.931 442.68C478.428 433.06 481.322 419.582 491.626 412.617C501.93 405.653 515.515 407.992 522.017 417.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="58"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M517.017 472.612C523.52 482.232 520.626 495.71 510.322 502.674C500.019 509.639 486.433 507.3 479.931 497.68C473.428 488.06 476.322 474.582 486.626 467.617C496.93 460.653 510.515 462.992 517.017 472.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="59"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M564.017 498.612C570.52 508.232 567.626 521.71 557.322 528.674C547.019 535.639 533.433 533.3 526.931 523.68C520.428 514.06 523.322 500.582 533.626 493.617C543.93 486.653 557.515 488.992 564.017 498.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="56"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M562.017 444.612C568.52 454.232 565.626 467.71 555.322 474.674C545.019 481.639 531.433 479.3 524.931 469.68C518.428 460.06 521.322 446.582 531.626 439.617C541.93 432.653 555.515 434.992 562.017 444.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="57"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M564.017 550.612C570.52 560.232 567.626 573.71 557.322 580.674C547.019 587.639 533.433 585.3 526.931 575.68C520.428 566.06 523.322 552.582 533.626 545.617C543.93 538.653 557.515 540.992 564.017 550.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="55"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M564.017 604.612C570.52 614.232 567.626 627.71 557.322 634.674C547.019 641.639 533.433 639.3 526.931 629.68C520.428 620.06 523.322 606.582 533.626 599.617C543.93 592.653 557.515 594.992 564.017 604.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="54"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M565.017 655.612C571.52 665.232 568.626 678.71 558.322 685.674C548.019 692.639 534.433 690.3 527.931 680.68C521.428 671.06 524.322 657.582 534.626 650.617C544.93 643.653 558.515 645.992 565.017 655.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="53"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M562.017 711.612C568.52 721.232 565.626 734.71 555.322 741.674C545.019 748.639 531.433 746.3 524.931 736.68C518.428 727.06 521.322 713.582 531.626 706.617C541.93 699.653 555.515 701.992 562.017 711.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="52"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M564.017 765.612C570.52 775.232 567.626 788.71 557.322 795.674C547.019 802.639 533.433 800.3 526.931 790.68C520.428 781.06 523.322 767.582 533.626 760.617C543.93 753.653 557.515 755.992 564.017 765.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="51"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M566.017 817.612C572.52 827.232 569.626 840.71 559.322 847.674C549.019 854.639 535.433 852.3 528.931 842.68C522.428 833.06 525.322 819.582 535.626 812.617C545.93 805.653 559.515 807.992 566.017 817.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="50"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M576.498 868.784C581.867 879.079 577.462 892.142 566.435 897.892C555.408 903.643 542.176 899.778 536.807 889.482C531.438 879.187 535.842 866.124 546.869 860.373C557.896 854.623 571.129 858.488 576.498 868.784Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="49"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M598.498 913.784C603.867 924.079 599.462 937.142 588.435 942.892C577.408 948.643 564.176 944.778 558.807 934.482C553.438 924.187 557.842 911.124 568.869 905.373C579.896 899.623 593.129 903.488 598.498 913.784Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="48"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M641.498 942.784C646.867 953.079 642.462 966.142 631.435 971.892C620.408 977.643 607.176 973.778 601.807 963.482C596.438 953.187 600.842 940.124 611.869 934.373C622.896 928.623 636.129 932.488 641.498 942.784Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="47"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M745.498 944.784C750.867 955.079 746.462 968.142 735.435 973.892C724.408 979.643 711.176 975.778 705.807 965.482C700.438 955.187 704.842 942.124 715.869 936.373C726.896 930.623 740.129 934.488 745.498 944.784Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="45"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M336.498 951.784C341.867 962.079 337.462 975.142 326.435 980.892C315.408 986.643 302.176 982.778 296.807 972.482C291.438 962.187 295.842 949.124 306.869 943.373C317.896 937.623 331.129 941.488 336.498 951.784Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="71"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M692.498 952.784C697.867 963.079 693.462 976.142 682.435 981.892C671.408 987.643 658.176 983.778 652.807 973.482C647.438 963.187 651.842 950.124 662.869 944.373C673.896 938.623 687.129 942.488 692.498 952.784Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="46"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M793.017 563.612C799.52 573.232 796.626 586.71 786.322 593.674C776.019 600.639 762.433 598.3 755.931 588.68C749.428 579.06 752.322 565.582 762.626 558.617C772.93 551.653 786.515 553.992 793.017 563.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="37"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M781.017 513.612C787.52 523.232 784.626 536.71 774.322 543.674C764.019 550.639 750.433 548.3 743.931 538.68C737.428 529.06 740.322 515.582 750.626 508.617C760.93 501.653 774.515 503.992 781.017 513.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="36"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M774.017 463.612C780.52 473.232 777.626 486.71 767.322 493.674C757.019 500.639 743.433 498.3 736.931 488.68C730.428 479.06 733.322 465.582 743.626 458.617C753.93 451.653 767.515 453.992 774.017 463.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="35"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M763.017 412.612C769.52 422.232 766.626 435.71 756.322 442.674C746.019 449.639 732.433 447.3 725.931 437.68C719.428 428.06 722.322 414.582 732.626 407.617C742.93 400.653 756.515 402.992 763.017 412.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="34"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M788.017 362.612C794.52 372.232 791.626 385.71 781.322 392.674C771.019 399.639 757.433 397.3 750.931 387.68C744.428 378.06 747.322 364.582 757.626 357.617C767.93 350.653 781.515 352.992 788.017 362.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="33"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M822.017 403.612C828.52 413.232 825.626 426.71 815.322 433.674C805.019 440.639 791.433 438.3 784.931 428.68C778.428 419.06 781.322 405.582 791.626 398.617C801.93 391.653 815.515 393.992 822.017 403.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="32"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M806.017 611.612C812.52 621.232 809.626 634.71 799.322 641.674C789.019 648.639 775.433 646.3 768.931 636.68C762.428 627.06 765.322 613.582 775.626 606.617C785.93 599.653 799.515 601.992 806.017 611.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="38"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M815.017 661.612C821.52 671.232 818.626 684.71 808.322 691.674C798.019 698.639 784.433 696.3 777.931 686.68C771.428 677.06 774.322 663.582 784.626 656.617C794.93 649.653 808.515 651.992 815.017 661.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="39"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M823.017 710.612C829.52 720.232 826.626 733.71 816.322 740.674C806.019 747.639 792.433 745.3 785.931 735.68C779.428 726.06 782.322 712.582 792.626 705.617C802.93 698.653 816.515 700.992 823.017 710.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="40"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M827.017 761.612C833.52 771.232 830.626 784.71 820.322 791.674C810.019 798.639 796.433 796.3 789.931 786.68C783.428 777.06 786.322 763.582 796.626 756.617C806.93 749.653 820.515 751.992 827.017 761.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="41"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M828.017 813.612C834.52 823.232 831.626 836.71 821.322 843.674C811.019 850.639 797.433 848.3 790.931 838.68C784.428 829.06 787.322 815.582 797.626 808.617C807.93 801.653 821.515 803.992 828.017 813.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="42"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M788.498 913.784C793.867 924.079 789.462 937.142 778.435 942.892C767.408 948.643 754.176 944.778 748.807 934.482C743.438 924.187 747.842 911.124 758.869 905.373C769.896 899.623 783.129 903.488 788.498 913.784Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="44"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M825.498 868.784C830.867 879.079 826.462 892.142 815.435 897.892C804.408 903.643 791.176 899.778 785.807 889.482C780.438 879.187 784.842 866.124 795.869 860.373C806.896 854.623 820.129 858.488 825.498 868.784Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="43"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M152.691 613.043C160.923 621.232 160.676 635.015 151.905 643.832C143.135 652.649 129.353 652.967 121.121 644.778C112.889 636.589 113.135 622.806 121.906 613.989C130.677 605.172 144.459 604.854 152.691 613.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="88"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M107.691 635.043C115.923 643.232 115.676 657.015 106.905 665.832C98.1345 674.649 84.3529 674.967 76.1209 666.778C67.8889 658.589 68.1353 644.806 76.9062 635.989C85.6771 627.172 99.4587 626.854 107.691 635.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="89"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M61.6907 614.043C69.9227 622.232 69.6763 636.015 60.9054 644.832C52.1345 653.649 38.3529 653.967 30.1209 645.778C21.8889 637.589 22.1353 623.806 30.9062 614.989C39.6771 606.172 53.4587 605.854 61.6907 614.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="90"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M48.6907 564.043C56.9227 572.232 56.6763 586.015 47.9054 594.832C39.1345 603.649 25.3529 603.967 17.1209 595.778C8.88893 587.589 9.13528 573.806 17.9062 564.989C26.6771 556.172 40.4587 555.854 48.6907 564.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="91"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M873.691 484.043C881.923 492.232 881.676 506.015 872.905 514.832C864.135 523.649 850.353 523.967 842.121 515.778C833.889 507.589 834.135 493.806 842.906 484.989C851.677 476.172 865.459 475.854 873.691 484.043Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="30"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M997.313 846.734C1004.86 852.188 1006.42 863.159 1000.56 871.279C994.691 879.399 983.787 881.372 976.235 875.918C968.683 870.464 967.128 859.492 972.993 851.372C978.857 843.252 989.761 841.279 997.313 846.734Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="26"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M1058.31 499.248C1065.86 504.702 1067.42 515.674 1061.56 523.794C1055.69 531.914 1044.79 533.887 1037.24 528.433C1029.68 522.978 1028.13 512.007 1033.99 503.887C1039.86 495.767 1050.76 493.794 1058.31 499.248Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="15"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M566.788 312.929C574.385 318.32 576.033 329.277 570.236 337.446C564.44 345.615 553.553 347.68 545.956 342.289C538.359 336.898 536.711 325.94 542.508 317.771C548.304 309.602 559.191 307.538 566.788 312.929Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="95"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M526.499 299.425C534.096 304.816 535.744 315.774 529.947 323.943C524.151 332.112 513.264 334.176 505.667 328.785C498.069 323.394 496.422 312.436 502.219 304.267C508.015 296.098 518.902 294.034 526.499 299.425Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="96"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M479.916 309.75C488.459 313.464 492.324 323.849 488.331 333.035C484.338 342.221 474.108 346.48 465.564 342.766C457.021 339.052 453.156 328.667 457.149 319.481C461.142 310.295 471.373 306.037 479.916 309.75Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="99"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M896.5 221.5C896.5 230.815 888.517 238.5 878.5 238.5C868.483 238.5 860.5 230.815 860.5 221.5C860.5 212.185 868.483 204.5 878.5 204.5C888.517 204.5 896.5 212.185 896.5 221.5Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="108"
            className="level-done"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M983.405 490.928C991.611 495.338 994.602 506.007 989.861 514.831C985.12 523.654 974.572 527.05 966.366 522.641C958.16 518.231 955.169 507.562 959.91 498.738C964.651 489.915 975.199 486.519 983.405 490.928Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="18"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M996.954 804.533C1004.51 809.988 1006.06 820.959 1000.2 829.079C994.332 837.199 983.428 839.172 975.876 833.718C968.324 828.264 966.769 817.292 972.633 809.172C978.498 801.052 989.402 799.079 996.954 804.533Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="25"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M1057.95 457.048C1065.51 462.502 1067.06 473.474 1061.2 481.594C1055.33 489.714 1044.43 491.687 1036.88 486.232C1029.32 480.778 1027.77 469.807 1033.63 461.687C1039.5 453.567 1050.4 451.594 1057.95 457.048Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="14"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M1036.95 421.048C1044.51 426.502 1046.06 437.474 1040.2 445.594C1034.33 453.714 1023.43 455.687 1015.88 450.232C1008.32 444.778 1006.77 433.807 1012.63 425.687C1018.5 417.567 1029.4 415.594 1036.95 421.048Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="13"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M1015.95 382.048C1023.51 387.502 1025.06 398.474 1019.2 406.594C1013.33 414.714 1002.43 416.687 994.876 411.232C987.324 405.778 985.769 394.807 991.633 386.687C997.498 378.567 1008.4 376.594 1015.95 382.048Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="12"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M994.954 891.048C1002.51 896.502 1004.06 907.474 998.196 915.594C992.332 923.714 981.428 925.687 973.876 920.232C966.324 914.778 964.769 903.807 970.633 895.687C976.498 887.567 987.402 885.594 994.954 891.048Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="27"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M990.954 935.049C998.505 940.503 1000.06 951.474 994.196 959.594C988.332 967.714 977.428 969.687 969.876 964.233C962.324 958.779 960.769 947.807 966.633 939.687C972.498 931.567 983.402 929.594 990.954 935.049Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="28"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M596.788 280.929C604.385 286.32 606.033 297.277 600.236 305.446C594.44 313.615 583.553 315.68 575.956 310.289C568.359 304.898 566.711 293.94 572.508 285.771C578.304 277.602 589.191 275.538 596.788 280.929Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="94"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M525.788 257.23C533.385 262.62 535.033 273.578 529.236 281.747C523.44 289.916 512.553 291.98 504.956 286.59C497.359 281.199 495.711 270.241 501.508 262.072C507.304 253.903 518.191 251.839 525.788 257.23Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="97"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M455.916 273.75C464.459 277.464 468.324 287.849 464.331 297.035C460.338 306.221 450.108 310.48 441.564 306.766C433.021 303.052 429.156 292.667 433.149 283.481C437.142 274.295 447.373 270.037 455.916 273.75Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="100"
            onClick={handleLevelPick}
          ></path>
          <circle
            cx="917"
            cy="199"
            r="17.5"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="110"
            className="level-done"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="973.289"
            cy="462.22"
            r="17.5"
            transform="rotate(-61.7503 973.289 462.22)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="17"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="985.527"
            cy="772.527"
            r="17.5"
            transform="rotate(-54.162 985.527 772.527)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="24"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="579.49"
            cy="256.491"
            r="17.5"
            transform="rotate(-54.6423 579.49 256.491)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="93"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="583.49"
            cy="212.491"
            r="17.5"
            transform="rotate(-54.6423 583.49 212.491)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="92"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="519.299"
            cy="227.491"
            r="17.5"
            transform="rotate(-54.6423 519.299 227.491)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="98"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="448.999"
            cy="248.999"
            r="17.5"
            transform="rotate(-66.5062 448.999 248.999)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="101"
            onClick={handleLevelPick}
          ></circle>
          <path
            d="M972.5 174.5C972.5 183.851 964.703 191.5 955 191.5C945.297 191.5 937.5 183.851 937.5 174.5C937.5 165.149 945.297 157.5 955 157.5C964.703 157.5 972.5 165.149 972.5 174.5Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="5"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M977.979 401.735C986.216 406.161 989.263 416.65 984.671 425.197C980.078 433.744 969.65 436.992 961.413 432.566C953.175 428.14 950.128 417.652 954.721 409.104C959.313 400.557 969.741 397.309 977.979 401.735Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="16"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M996.368 714.888C1003.95 720.363 1005.58 731.162 999.903 739.028C994.222 746.894 983.457 748.736 975.876 743.261C968.295 737.786 966.659 726.987 972.34 719.121C978.021 711.255 988.787 709.412 996.368 714.888Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="23"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M126.066 345.027C124.512 354.249 115.553 360.495 105.985 358.883C96.4165 357.27 89.999 348.432 91.5531 339.211C93.1072 329.989 102.067 323.743 111.635 325.355C121.203 326.968 127.62 335.806 126.066 345.027Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="114"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M1011.5 148.5C1011.5 157.815 1003.52 165.5 993.5 165.5C983.483 165.5 975.5 157.815 975.5 148.5C975.5 139.185 983.483 131.5 993.5 131.5C1003.52 131.5 1011.5 139.185 1011.5 148.5Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="6"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M973.534 355.073C981.74 359.482 984.731 370.152 979.99 378.975C975.249 387.799 964.701 391.194 956.495 386.785C948.289 382.376 945.298 371.706 950.039 362.883C954.78 354.059 965.328 350.664 973.534 355.073Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="11"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M998.125 668.048C1005.68 673.502 1007.23 684.473 1001.37 692.593C995.503 700.713 984.599 702.686 977.048 697.232C969.496 691.778 967.94 680.806 973.805 672.686C979.67 664.566 990.574 662.593 998.125 668.048Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="22"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M168.845 325.869C167.297 335.055 158.148 341.306 148.271 339.642C138.393 337.977 131.798 329.072 133.346 319.887C134.894 310.701 144.044 304.45 153.921 306.114C163.798 307.779 170.394 316.683 168.845 325.869Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="113"
            onClick={handleLevelPick}
          ></path>
          <circle
            cx="1032"
            cy="120"
            r="17.5"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="7"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="958.132"
            cy="323.526"
            r="17.5"
            transform="rotate(-61.7503 958.132 323.526)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="10"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="986.301"
            cy="634.259"
            r="17.5"
            transform="rotate(-54.162 986.301 634.259)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="21"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="528.667"
            cy="65.6678"
            r="17.5"
            transform="rotate(-52.0451 528.667 65.6678)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="1"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="531.667"
            cy="106.668"
            r="17.5"
            transform="rotate(-52.0451 531.667 106.668)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="104"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="516.667"
            cy="145.668"
            r="17.5"
            transform="rotate(-52.0451 516.667 145.668)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="105"
            onClick={handleLevelPick}
          ></circle>
          <circle
            cx="193.797"
            cy="301.171"
            r="17.5"
            transform="rotate(9.5663 193.797 301.171)"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="112"
            onClick={handleLevelPick}
          ></circle>
          <path
            d="M1082.5 90.5C1082.5 99.8154 1074.52 107.5 1064.5 107.5C1054.48 107.5 1046.5 99.8154 1046.5 90.5C1046.5 81.1846 1054.48 73.5 1064.5 73.5C1074.52 73.5 1082.5 81.1846 1082.5 90.5Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="8"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M956.046 265.078C964.252 269.487 967.242 280.157 962.501 288.98C957.76 297.804 947.212 301.199 939.006 296.79C930.801 292.381 927.81 281.711 932.551 272.888C937.292 264.064 947.84 260.669 956.046 265.078Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="9"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M991.954 576.048C999.505 581.502 1001.06 592.473 995.196 600.593C989.332 608.713 978.428 610.686 970.876 605.232C963.324 599.778 961.769 588.806 967.633 580.686C973.498 572.566 984.402 570.593 991.954 576.048Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="20"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M528.651 12.5612C535.996 18.2906 537.145 29.3118 530.985 37.2098C524.824 45.1078 513.855 46.6764 506.51 40.947C499.165 35.2176 498.015 24.1963 504.176 16.2984C510.337 8.40042 521.306 6.83182 528.651 12.5612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="102"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M248.498 280.475C246.95 289.661 237.8 295.912 227.923 294.247C218.046 292.582 211.45 283.678 212.998 274.492C214.547 265.306 223.696 259.055 233.573 260.72C243.451 262.384 250.046 271.289 248.498 280.475Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="2"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M282.052 236.475C280.504 245.661 271.355 251.912 261.478 250.247C251.6 248.582 245.005 239.678 246.553 230.492C248.101 221.306 257.251 215.055 267.128 216.72C277.005 218.384 283.601 227.289 282.052 236.475Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="111"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M282.052 191.475C280.504 200.661 271.355 206.912 261.478 205.247C251.6 203.582 245.005 194.678 246.553 185.492C248.101 176.306 257.251 170.055 267.128 171.72C277.005 173.384 283.601 182.289 282.052 191.475Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="4"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M233.052 201.475C231.504 210.661 222.355 216.912 212.478 215.247C202.6 213.582 196.005 204.678 197.553 195.492C199.101 186.306 208.251 180.055 218.128 181.72C228.005 183.384 234.601 192.289 233.052 201.475Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="109"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M211.052 245.475C209.504 254.661 200.355 260.912 190.478 259.247C180.6 257.582 174.005 248.678 175.553 239.492C177.101 230.306 186.251 224.055 196.128 225.72C206.005 227.384 212.601 236.289 211.052 245.475Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="3"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M304.052 281.475C302.504 290.661 293.355 296.912 283.478 295.247C273.6 293.582 267.005 284.678 268.553 275.492C270.101 266.306 279.251 260.055 289.128 261.72C299.005 263.384 305.601 272.289 304.052 281.475Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="106"
            onClick={handleLevelPick}
          ></path>
          <path
            d="M849.017 445.612C855.52 455.232 852.626 468.71 842.322 475.674C832.019 482.639 818.433 480.3 811.931 470.68C805.428 461.06 808.322 447.582 818.626 440.617C828.93 433.653 842.515 435.992 849.017 445.612Z"
            fill="#eee9e9"
            stroke="#eee9e9"
            stroke-width="3"
            data-index="31"
            onClick={handleLevelPick}
          ></path>
        </svg>
      </div>
    </>
  );
}

export default LevelsNet;

"""
Custom validators for the hifzy_backend project.

This module provides reusable validation functions and classes
that can be used across different apps and serializers.
"""

from rest_framework import serializers
from django.core.exceptions import ValidationError as DjangoValidationError
from typing import Any, List, Dict
import re


class QuranValidator:
    """Validator for Quran-related data."""
    
    TOTAL_SURAHS = 114
    
    # Approximate verse counts for each Surah (1-indexed)
    SURAH_VERSE_COUNTS = {
        1: 7, 2: 286, 3: 200, 4: 176, 5: 120, 6: 165, 7: 206, 8: 75, 9: 129, 10: 109,
        11: 123, 12: 111, 13: 43, 14: 52, 15: 99, 16: 128, 17: 111, 18: 110, 19: 98, 20: 135,
        21: 112, 22: 78, 23: 118, 24: 64, 25: 77, 26: 227, 27: 93, 28: 88, 29: 69, 30: 60,
        31: 34, 32: 30, 33: 73, 34: 54, 35: 45, 36: 83, 37: 182, 38: 88, 39: 75, 40: 85,
        41: 54, 42: 53, 43: 89, 44: 59, 45: 37, 46: 35, 47: 38, 48: 29, 49: 18, 50: 45,
        51: 60, 52: 49, 53: 62, 54: 55, 55: 78, 56: 96, 57: 29, 58: 22, 59: 24, 60: 13,
        61: 14, 62: 11, 63: 11, 64: 18, 65: 12, 66: 12, 67: 30, 68: 52, 69: 52, 70: 44,
        71: 28, 72: 28, 73: 20, 74: 56, 75: 40, 76: 31, 77: 50, 78: 40, 79: 46, 80: 42,
        81: 29, 82: 19, 83: 36, 84: 25, 85: 22, 86: 17, 87: 19, 88: 26, 89: 30, 90: 20,
        91: 15, 92: 21, 93: 11, 94: 8, 95: 8, 96: 19, 97: 5, 98: 8, 99: 8, 100: 11,
        101: 11, 102: 8, 103: 3, 104: 9, 105: 5, 106: 4, 107: 7, 108: 3, 109: 6, 110: 3,
        111: 5, 112: 4, 113: 5, 114: 6
    }
    
    @classmethod
    def validate_surah_id(cls, surah_id: int) -> int:
        """Validate Surah ID."""
        if not isinstance(surah_id, int):
            raise serializers.ValidationError("Surah ID must be an integer")
        
        if surah_id < 1 or surah_id > cls.TOTAL_SURAHS:
            raise serializers.ValidationError(f"Surah ID must be between 1 and {cls.TOTAL_SURAHS}")
        
        return surah_id
    
    @classmethod
    def validate_ayah_id(cls, ayah_id: int, surah_id: int = None) -> int:
        """Validate Ayah ID, optionally against a specific Surah."""
        if not isinstance(ayah_id, int):
            raise serializers.ValidationError("Ayah ID must be an integer")
        
        if ayah_id < 1:
            raise serializers.ValidationError("Ayah ID must be positive")
        
        # If surah_id is provided, validate against the specific Surah's verse count
        if surah_id is not None:
            max_verses = cls.SURAH_VERSE_COUNTS.get(surah_id)
            if max_verses and ayah_id > max_verses:
                raise serializers.ValidationError(
                    f"Ayah ID {ayah_id} exceeds the number of verses in Surah {surah_id} ({max_verses})"
                )
        
        return ayah_id
    
    @classmethod
    def validate_ayah_range(cls, surah_id: int, start_ayah: int, count: int) -> tuple:
        """Validate a range of Ayahs."""
        cls.validate_surah_id(surah_id)
        cls.validate_ayah_id(start_ayah, surah_id)
        
        if count < 1:
            raise serializers.ValidationError("Count must be at least 1")
        
        max_verses = cls.SURAH_VERSE_COUNTS.get(surah_id, 300)  # Default fallback
        if start_ayah + count - 1 > max_verses:
            available_count = max_verses - start_ayah + 1
            raise serializers.ValidationError(
                f"Requested range exceeds available verses. Maximum count from Ayah {start_ayah}: {available_count}"
            )
        
        return surah_id, start_ayah, count


class UserValidator:
    """Validator for user-related data."""
    
    @staticmethod
    def validate_username(username: str) -> str:
        """Validate username format."""
        if not username:
            raise serializers.ValidationError("Username cannot be empty")
        
        if len(username) < 3:
            raise serializers.ValidationError("Username must be at least 3 characters long")
        
        if len(username) > 150:
            raise serializers.ValidationError("Username cannot exceed 150 characters")
        
        # Allow alphanumeric characters, underscores, and hyphens
        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            raise serializers.ValidationError(
                "Username can only contain letters, numbers, underscores, and hyphens"
            )
        
        return username.lower()
    
    @staticmethod
    def validate_name(name: str, field_name: str = "name") -> str:
        """Validate first name or last name."""
        if name and len(name) > 150:
            raise serializers.ValidationError(f"{field_name} cannot exceed 150 characters")
        
        # Allow letters, spaces, apostrophes, and hyphens
        if name and not re.match(r"^[a-zA-Z\s'-]+$", name):
            raise serializers.ValidationError(
                f"{field_name} can only contain letters, spaces, apostrophes, and hyphens"
            )
        
        return name.strip() if name else ""


class GameValidator:
    """Validator for game-related data."""
    
    VALID_GAME_TYPES = ['shuffling', 'matching', 'memory', 'audio']
    
    @classmethod
    def validate_game_type(cls, game_type: str) -> str:
        """Validate game type."""
        if game_type not in cls.VALID_GAME_TYPES:
            raise serializers.ValidationError(
                f"Invalid game type. Must be one of: {', '.join(cls.VALID_GAME_TYPES)}"
            )
        return game_type
    
    @staticmethod
    def validate_ayah_count(count: int) -> int:
        """Validate ayah count for games."""
        if not isinstance(count, int):
            raise serializers.ValidationError("Ayah count must be an integer")
        
        if count < 1:
            raise serializers.ValidationError("Ayah count must be at least 1")
        
        if count > 50:
            raise serializers.ValidationError("Ayah count cannot exceed 50")
        
        return count
    
    @staticmethod
    def validate_selected_ayah(selected_ayah: int) -> int:
        """Validate selected ayah count for game generation."""
        if not isinstance(selected_ayah, int):
            raise serializers.ValidationError("Selected ayah must be an integer")
        
        if selected_ayah < 1:
            raise serializers.ValidationError("Selected ayah must be at least 1")
        
        if selected_ayah > 20:  # Reasonable limit for game generation
            raise serializers.ValidationError("Selected ayah cannot exceed 20")
        
        return selected_ayah


class ProgressValidator:
    """Validator for progress-related data."""
    
    @staticmethod
    def validate_progress_points(points: int) -> int:
        """Validate progress points."""
        if not isinstance(points, int):
            raise serializers.ValidationError("Progress points must be an integer")
        
        if points < 1:
            raise serializers.ValidationError("Progress points must be at least 1")
        
        if points > 1000:
            raise serializers.ValidationError("Progress points cannot exceed 1000")
        
        return points
    
    @staticmethod
    def validate_experience_points(points: int) -> int:
        """Validate experience points."""
        if not isinstance(points, int):
            raise serializers.ValidationError("Experience points must be an integer")
        
        if points < 0:
            raise serializers.ValidationError("Experience points cannot be negative")
        
        if points > 10000:
            raise serializers.ValidationError("Experience points cannot exceed 10000")
        
        return points


def validate_positive_integer(value: Any, field_name: str = "value") -> int:
    """Generic validator for positive integers."""
    if not isinstance(value, int):
        try:
            value = int(value)
        except (ValueError, TypeError):
            raise serializers.ValidationError(f"{field_name} must be an integer")
    
    if value < 1:
        raise serializers.ValidationError(f"{field_name} must be positive")
    
    return value


def validate_non_negative_integer(value: Any, field_name: str = "value") -> int:
    """Generic validator for non-negative integers."""
    if not isinstance(value, int):
        try:
            value = int(value)
        except (ValueError, TypeError):
            raise serializers.ValidationError(f"{field_name} must be an integer")
    
    if value < 0:
        raise serializers.ValidationError(f"{field_name} cannot be negative")
    
    return value


def validate_string_length(value: str, min_length: int = 0, max_length: int = None, field_name: str = "value") -> str:
    """Generic validator for string length."""
    if not isinstance(value, str):
        raise serializers.ValidationError(f"{field_name} must be a string")
    
    if len(value) < min_length:
        raise serializers.ValidationError(f"{field_name} must be at least {min_length} characters long")
    
    if max_length and len(value) > max_length:
        raise serializers.ValidationError(f"{field_name} cannot exceed {max_length} characters")
    
    return value.strip()

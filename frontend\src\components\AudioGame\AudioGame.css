.audio-game {
  margin: auto;
  display: flex;
  justify-content: center;
  overflow-y: auto;
  width: 100%;
  position: relative; /* Контейнер для фиксированного позиционирования аудио-элемента */
}

.audio-text-container::-webkit-scrollbar {
  width: 0.5em;
}

.audio-text-container::-webkit-scrollbar-thumb {
  background-color: #97cb9c;
  outline: 1px solid slategrey;
  border-radius: 10px;
}

.audio-text-container::-webkit-scrollbar-thumb:hover {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.audio-text-container {
padding-right: 30px;
overflow-y: auto;
  width: 80%;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  gap: 10px;
}

.audio-text {
  /* width: 100%; */
  border-radius: 40px;
  border: thin solid #bfbfbf;
  padding: 10px;
  font-size: 18px;
  text-align: center;
  font-weight: 500;
  height: 75px;
  display: flex;
}

.audio-text:hover{
  cursor: pointer;
  background-color: #F0F0F0;
  transition: background-color 0.25s ease-in-out;
}

.audio-text-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  height: 100%;
  width: 100%;
  font-size: 30px;
}

.audio-text-text {
  padding: 0px 40px;
}

.audio-text-number {
  border: thin solid #bfbfbf;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
}

.audio-player {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: sticky; /* Остается фиксированным внутри родительского контейнера */
  top: 0;
  padding: 10px;
  background-color: white;
  z-index: 100; /* Повышаем z-index для отображения над текстом */
}

.play-button,
.volume-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #a3d7a2; /* Цвет кнопки */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
  cursor: pointer;
}

.progress-bar {
  width: 150px;
  height: 10px;
  background-color: #eaeaea; /* Цвет заднего фона прогресса */
  border-radius: 5px;
  position: relative;
  overflow: hidden;
}

.progress {
  height: 100%;
  width: 0%; /* Ширина прогресса (изменяемая) */
  background-color: #97c89c; /* Цвет заполненного прогресса */
}


.audio-text.correct {
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.audio-text.incorrect {
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.result-container {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.3s ease, transform 0.3s ease;
  width: 100%;
  border-radius: 40px;
  border: thin solid #bfbfbf;
  padding: 15px;
  font-size: 18px;
  text-align: center;
  font-weight: 500;
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: sticky;
  bottom: 0;
}

.result-container.correct,
.result-container.incorrect {
  opacity: 1;
  transform: scale(1);
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Добавляем анимацию для появления result-container */
.result-container.correct,
.result-container.incorrect {
  animation: fadeInScale 0.4s ease forwards;
}

.result-container.correct {
  background-color: #d4edda; /* Светло-зелёный цвет для правильного ответа */
  color: #155724;
}

.result-container.incorrect {
  background-color: #f8d7da; /* Светло-красный цвет для неправильного ответа */
  color: #721c24;
}

/* Стили для текста */
.result-container p {
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0;
}

/* Стили кнопки */
.result-container button {
  padding: 7px 20px;
  width: 200px;
  /* background-color: #ffffff; */
  color: #333;
  font-size: 1rem;
  font-weight: bold;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.result-container button:hover {
  background-color: #dddddd;
}

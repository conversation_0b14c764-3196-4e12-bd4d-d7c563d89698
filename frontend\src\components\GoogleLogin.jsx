import React, { useState } from "react";
import { useGoogleLogin } from "@react-oauth/google";
import axios from "axios";
import useAuthStore from "src/hooks/authStore.js";
import { useNavigate } from "react-router-dom";
import LoadingSpinner from "src/components/LoadingSpinner";
import { BACKEND_URL } from "src/utils/settings";

const GoogleLoginComponent = () => {
  const [loading, setLoading] = useState(false);
  const setToken = useAuthStore((state) => state.setToken);
  const navigate = useNavigate();

  const handleLoginSuccess = async (tokenResponse) => {
    setLoading(true); // Set loading to true when login starts
    try {
      const result = await axios.post(
        // "https://hifzy.ru/api/v1/auth/login_google/",
        `${BACKEND_URL}/api/v1/auth/login_google/`,
        {
          access_token: tokenResponse.access_token,
        }
      );

      setToken(result.data.token); // Set the token in Zustand store
      navigate("/profile"); // Navigate to the profile page
    } catch (error) {
      console.error("Error during login:", error.response || error.message);
    } finally {
      setLoading(false); // Set loading to false after process is done
    }
  };

  const login = useGoogleLogin({
    onSuccess: handleLoginSuccess,
  });

  return (
    <div>
      {loading && <LoadingSpinner />} {/* Use LoadingSpinner component */}
      {!loading && (
        <button
          onClick={() => login()}
          style={buttonStyle}
          onMouseOver={(e) =>
            (e.currentTarget.style.backgroundColor =
              buttonHoverStyle.backgroundColor)
          }
          onMouseOut={(e) =>
            (e.currentTarget.style.backgroundColor =
              buttonStyle.backgroundColor)
          }
        >
          Sign in with Google 🚀
        </button>
      )}
    </div>
  );
};

// Inline styles for button
const buttonStyle = {
  padding: "10px 20px",
  fontSize: "16px",
  color: "#fff",
  backgroundColor: "#1a73e8",
  border: "none",
  borderRadius: "5px",
  cursor: "pointer",
  transition: "background-color 0.3s ease",
  outline: "none",
};

const buttonHoverStyle = {
  backgroundColor: "#155ab0",
};

export default GoogleLoginComponent;

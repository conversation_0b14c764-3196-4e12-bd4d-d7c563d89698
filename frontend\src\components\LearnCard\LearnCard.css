.learn-card {
  display: flex;
  flex-direction: row;
  justify-content: center;
  row-gap: 20px;
  align-items: center;
  margin-top: 20px;
}

.left-arrow-learn {
  width: 40px;
  cursor: pointer;
}

.left-arrow-learn:hover {
  transform: scale(1.05);
  transition-duration: 0.2s;
}

.settings-button {
  width: 40px;
  cursor: pointer;
}

.settings-button:hover {
  transform: scale(1.05);
  transition-duration: 0.2s;
}

.bookmarks {
  width: 40px;
  cursor: pointer;
}

.bookmarks:hover {
  transform: scale(1.05);
  transition-duration: 0.2s;
}

.right-arrow-learn {
  width: 40px;
  cursor: pointer;
}

.learn-card-img {
  display: flex;
  margin-left: 20px;
  margin-right: 20px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 680px;
  height: 380px;
  border-radius: 20px;
  cursor: pointer;
  background-color: white;
  filter: drop-shadow(0 0 20px rgba(0, 0, 0, 0.3));
  transition: transform 0.6s ease;
}

.card-img {
  width: 50%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

.card-img.flash {
  width: 100%;
  border-radius: 20px;
}

.learn-card-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  align-items: center;
  width: 50%;
  height: 100%;
  padding: 10px;
  overflow: hidden;
}

.learn-card-info.flash {
  width: 100%;
  transform: rotateY(180deg);
  /* transition: none; */
}

.learn-card-text {
  font-size: min(max(24px, 3vw), 46px); /* Шрифт адаптируется в зависимости от контейнера */
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  overflow-wrap: break-word; /* Перенос слов при необходимости */
  white-space: normal; /* Разрешаем переносы строк */
  display: inline-block;
  max-width: 100%;
}

.learn-card-transliteration,
.learn-card-english {
  font-size: min(max(16px, 1vw), 24px); /* Для адаптации размера */
  color: #828282;
  text-align: center;
  line-height: 1.2;
  overflow-wrap: break-word;
  white-space: normal;
}
.learn-card-english {
  color: black;
}

.learn-card-img.flash {
  width: 340px;
}

.learn-card-img.flipped {
  transform: rotateY(180deg);
  backface-visibility: visible;
}

.right-learn-card-controls {
  padding-top: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  justify-content: flex-start;
  position: relative;
  row-gap: 10px;
}

.right-learn-card-controls > :nth-child(3) {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  align-self: center;
}

.right-learn-card-controls > :nth-child(3):hover {
  width: 105%;
  transition-duration: 0.2s;
}

.play-audio-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  width: 75%;
}

.left-audio-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 25%;
}

.play-sound {
  width: 40px;
  cursor: pointer;
}

.play-sound:hover {
  transform: scale(1.05);
  transition-duration: 0.2s;
}

.dynamic-icon {
  width: 40px;
  cursor: pointer;
}

.dynamic-icon:hover {
  transform: scale(1.05);
  transition-duration: 0.2s;
}

.main-audio-pg-bar {
  /* filter: drop-shadow(5px 12px 5px #78787825); */
  background-color: #eee9e9;
  width: 55%;
  height: 15px;
  border-radius: 30px;
  /* border: 1px solid black; */
  cursor: pointer;
}

.done-audio-pg-bar {
  background-color: #a5dea0;
  height: 100%;
  width: 40%;
  border-radius: 30px;
}

.audio-line {
  /* filter: drop-shadow(5px 12px 5px #78787825); */
  background-color: #eee9e9;
  width: 75%;
  height: 15px;
  border-radius: 30px;
  /* border: 1px solid black; */
  cursor: pointer;
}

.done-audio-line {
  background-color: #a5dea0;
  height: 100%;
  width: 60%;
  border-radius: 30px;
}

.learn-card-buttons {
  padding-top: 18px;
  display: flex;
  height: 110px;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 225px;
  height: 37px;
  border-radius: 30px;
  /* border: 1px solid black; */
  cursor: pointer;
  font-weight: 200;
  font-size: 18px;
}

.button:hover {
  transform: scale(1.05);
  transition-duration: 0.2s;
}

.learn {
  background-color: #97cb9c;
  filter: drop-shadow(5px 6px 5px #1d1d1d25);
  color: white;
}

.practise {
  background-color: #eee9e9;
  filter: drop-shadow(5px 12px 5px #9c9c9c25);
  cursor: pointer;
}

.practise:hover {
  transform: scale(1.05);
  transition-duration: 0.2s;
}

.custom-switch-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.custom-switch {
  background-color: #e0e0e0;
  border-radius: 30px;
  position: relative;
  width: 250px;
  padding: 0.75px;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  overflow: hidden;
}

.custom-switch .switch-slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  background-color: #97cb9c;
  border-radius: 30px;
  transition: left 0.3s ease;
}

.custom-switch.full-active .switch-slider {
  left: 0;
}

.custom-switch.flash-active .switch-slider {
  left: 50%;
}

.switch-option {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #888;
  z-index: 1; /* To ensure text stays above the slider */
  padding: 5px;
  transition: color 0.3s ease;
  font-size: 15px;
}

.switch-option.active-switch {
  color: white;
}

.audio-minutes-counter {
  color: #828282;
}

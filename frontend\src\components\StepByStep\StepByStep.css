.reveal-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: 'Arial', sans-serif;
  padding: 20px;
}

.text {
  display: flex;
  flex-wrap: wrap;
  /* overflow-y: auto;  */
  /* max-height: 300px;  */
  padding: 10px;
  /* scrollbar-width: thin;  */
  /* scrollbar-color: #888 #f1f1f1;  */
}


/* .text::-webkit-scrollbar {
  width: 8px; 
}

.text::-webkit-scrollbar-track {
  background: #f1f1f1; 
}

.text::-webkit-scrollbar-thumb {
  background-color: #888; 
  border-radius: 10px; 
  border: 2px solid #f1f1f1; 
}

.text::-webkit-scrollbar-thumb:hover {
  background-color: #555; 
} */

.word {
  margin-right: 8px;
  font-size: 4.8vh;
  transition: color 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

.word.hidden {
  position: relative;
  color: transparent;
}

.word.hidden::before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #ccc;
  z-index: 1;
  border-radius: 4px;
  transition: width 0.5s ease-in-out;
}

.word.visible {
  color: black;
  opacity: 1;
}

/* Add button styles */
.add-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 225px;
  height: 37px;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 200;
  font-size: 18px;
  background-color: #eee9e9;
  filter: drop-shadow(5px 12px 5px #9c9c9c25);
  cursor: pointer;
  color: #828282;
  border: none;
  transition: background-color 0.3s ease-in-out;
}

/* .add-button:hover {
  background-color: #0056b3;
} */

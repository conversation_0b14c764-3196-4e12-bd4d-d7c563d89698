"""
Django management command to load Arabic letters into the database.

This command populates the ArabicLetter model with the complete Arabic alphabet,
including letter characters and their transcriptions. Audio URLs are initially
set to None and can be updated later via the Django admin interface.

Features:
- Loads 28 Arabic letters with proper transcriptions
- Uses bulk_create() for efficient database operations
- <PERSON><PERSON> duplicate entries gracefully
- Provides progress reporting and detailed feedback
- Supports force reload with --force flag

Usage:
    python manage.py load_arabic_letters
    python manage.py load_arabic_letters --force
    python manage.py load_arabic_letters --dry-run

Examples:
    # Load letters (will fail if letters already exist)
    python manage.py load_arabic_letters

    # Force reload (deletes existing letters and recreates them)
    python manage.py load_arabic_letters --force

    # Test what would be created without making changes
    python manage.py load_arabic_letters --dry-run

    # With environment specification
    python manage.py load_arabic_letters --env=docker
"""

from typing import Dict, List
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from quran.models import ArabicLetter


class Command(BaseCommand):
    help = "Load Arabic letters into the database"

    def add_arguments(self, parser):
        parser.add_argument(
            "--force", action="store_true", help="Force reload existing letters (will delete and recreate all records)"
        )
        parser.add_argument(
            "--dry-run", action="store_true", help="Show what would be created without actually creating anything"
        )

    def handle(self, *args, **options):
        force_reload = options["force"]
        dry_run = options["dry_run"]

        # Arabic alphabet data
        arabic_alphabet = [
            {"letter": "أ", "name": "alif"},
            {"letter": "ب", "name": "baa"},
            {"letter": "ت", "name": "taa"},
            {"letter": "ث", "name": "thaa"},
            {"letter": "ج", "name": "jlim"},
            {"letter": "ح", "name": "Haa"},
            {"letter": "خ", "name": "xaa"},
            {"letter": "د", "name": "daal"},
            {"letter": "ذ", "name": "dhaal"},
            {"letter": "ر", "name": "raa"},
            {"letter": "ز", "name": "zayn"},
            {"letter": "س", "name": "sim"},
            {"letter": "ش", "name": "shiin"},
            {"letter": "ص", "name": "Saad"},
            {"letter": "ض", "name": "Daad"},
            {"letter": "ط", "name": "Taa"},
            {"letter": "ظ", "name": "DHaa"},
            {"letter": "ع", "name": "ayn"},
            {"letter": "غ", "name": "ghayn"},
            {"letter": "ف", "name": "faa"},
            {"letter": "ق", "name": "qaaf"},
            {"letter": "ك", "name": "kaaf"},
            {"letter": "ل", "name": "lam"},
            {"letter": "م", "name": "miim"},
            {"letter": "ن", "name": "nuun"},
            {"letter": "ه", "name": "haa"},
            {"letter": "و", "name": "waaw"},
            {"letter": "ي", "name": "yaa"},
        ]

        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No changes will be made to the database"))

        self.stdout.write(f"Loading {len(arabic_alphabet)} Arabic letters...")

        try:
            # Check for existing data
            existing_letters = ArabicLetter.objects.count()

            if existing_letters > 0:
                if not force_reload and not dry_run:
                    raise CommandError(
                        f"Database already contains {existing_letters} Arabic letters. Use --force to reload data."
                    )
                else:
                    message = (
                        f"Would delete existing data: {existing_letters} Arabic letters"
                        if dry_run
                        else f"Deleting existing data: {existing_letters} Arabic letters"
                    )
                    self.stdout.write(self.style.WARNING(message))

            # Load data with transaction (skip if dry run)
            if dry_run:
                stats = self._simulate_load_arabic_letters(arabic_alphabet)
            else:
                with transaction.atomic():
                    if force_reload:
                        self._clear_existing_data()

                    stats = self._load_arabic_letters(arabic_alphabet)

            # Display results
            final_count = ArabicLetter.objects.count()
            result_message = "Successfully loaded Arabic letters:\n"
            result_message += f"  - Letters processed: {len(arabic_alphabet)}\n"
            result_message += f"  - Letters created: {stats['letters_created']}\n"
            result_message += f"  - Total letters in database: {final_count}"

            self.stdout.write(self.style.SUCCESS(result_message))
            
        except Exception as e:
            raise CommandError(f"Failed to load Arabic letters: {str(e)}")

    def _clear_existing_data(self):
        """Clear existing Arabic letters from the database."""
        ArabicLetter.objects.all().delete()
        self.stdout.write("Cleared existing Arabic letters")

    def _load_arabic_letters(self, letters_data: List[Dict[str, str]]) -> Dict[str, int]:
        """Load Arabic letters into the database."""
        stats = {"letters_created": 0}

        # Validate data structure
        if not self._validate_letters_data(letters_data):
            raise CommandError("Invalid Arabic letters data structure")

        # Prepare letters for bulk creation
        letters_to_create = []

        self.stdout.write("Processing Arabic letters...")

        for i, letter_data in enumerate(letters_data, 1):
            # Map the fields correctly: "letter" -> title, "name" -> transcription
            letter = ArabicLetter(
                title=letter_data["letter"],
                transcription=letter_data["name"],
                audio_url=None,  # Set to None initially, can be updated via admin
            )
            letters_to_create.append(letter)

            # Progress reporting
            if i % 10 == 0 or i == len(letters_data):
                self.stdout.write(f"  Processed {i}/{len(letters_data)} letters...")

        # Bulk create letters for efficiency
        if letters_to_create:
            self.stdout.write("Saving letters to database...")
            try:
                ArabicLetter.objects.bulk_create(letters_to_create, batch_size=100, ignore_conflicts=True)
                stats["letters_created"] = len(letters_to_create)
                self.stdout.write(f"Successfully created {len(letters_to_create)} Arabic letters")
            except Exception as e:
                raise CommandError(f"Failed to save letters to database: {str(e)}")

        return stats

    def _validate_letters_data(self, letters_data: List[Dict[str, str]]) -> bool:
        """Validate the structure of Arabic letters data."""
        if not isinstance(letters_data, list):
            self.stdout.write(self.style.ERROR("Letters data must be a list"))
            return False

        for i, letter_data in enumerate(letters_data):
            if not isinstance(letter_data, dict):
                self.stdout.write(self.style.ERROR(f"Letter {i + 1} must be a dictionary"))
                return False

            if "letter" not in letter_data or "name" not in letter_data:
                self.stdout.write(self.style.ERROR(f"Letter {i + 1} missing required fields 'letter' or 'name'"))
                return False

            if not letter_data["letter"] or not letter_data["name"]:
                self.stdout.write(self.style.ERROR(f"Letter {i + 1} has empty 'letter' or 'name' field"))
                return False

        return True

    def _simulate_load_arabic_letters(self, letters_data: List[Dict[str, str]]) -> Dict[str, int]:
        """Simulate loading Arabic letters (dry run mode)."""
        stats = {"letters_created": len(letters_data)}

        # Validate data structure
        if not self._validate_letters_data(letters_data):
            raise CommandError("Invalid Arabic letters data structure")

        self.stdout.write("Simulating Arabic letters creation...")

        for i, _ in enumerate(letters_data, 1):
            # Progress reporting
            if i % 10 == 0 or i == len(letters_data):
                self.stdout.write(f"  Would process {i}/{len(letters_data)} letters...")

        self.stdout.write(f"Would create {len(letters_data)} Arabic letters")
        return stats

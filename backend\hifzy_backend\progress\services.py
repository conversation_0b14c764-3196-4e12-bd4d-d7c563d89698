"""
Service layer.

This module contains business logic for game-related operations.
Services handle game generation, validation, and coordination.
"""

from typing import Optional, List
from django.contrib.auth import get_user_model
from django.db import transaction
from rest_framework.exceptions import ValidationError, PermissionDenied, NotFound
import logging

from . import models
from . import transports
from . import repositories

logger = logging.getLogger(__name__)
User = get_user_model()


class ProgressService:
    """Service for progress tracking operations using new models."""

    @staticmethod
    @transaction.atomic
    def update_user_total_xp(user_id: int, additional_xp: int) -> int:
        """Add XP to user's total and return new total."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        user.total_xp += additional_xp
        user.save()
        return user.total_xp

    @staticmethod
    @transaction.atomic
    def update_user_level(user_id: int, new_level: int) -> int:
        """Update user's current level."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        user.current_level = new_level
        user.save()
        return user.current_level

    @staticmethod
    @transaction.atomic
    def update_day_streak(user_id: int, new_streak: int) -> int:
        """Update user's day streak."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        user.day_streak = new_streak
        user.save()
        return user.day_streak

    @staticmethod
    def get_user_progress_summary(user_id: int) -> Optional[dict]:
        """Get user's progress summary from HifzyUser model."""
        try:
            user = User.objects.get(id=user_id)
            return {
                "total_xp": user.total_xp,
                "current_level": user.current_level,
                "day_streak": user.day_streak,
                "league": user.league_pk.name if user.league_pk else None,
            }
        except User.DoesNotExist:
            return None

    @staticmethod
    def calculate_progression_based_level(user_id: int) -> int:
        """Calculate user level based on progression including skipped surahs."""
        try:
            # Get user's progression summary
            progression_summary = SurahProgressionService.get_user_progression_summary(user_id)

            # Base level calculation on unlocked surahs (including skipped ones)
            unlocked_count = progression_summary["total_unlocked"]

            # Return the higher of the two levels to ensure progression reflects both XP and accessibility
            return unlocked_count

        except User.DoesNotExist:
            return 1

    @staticmethod
    def synchronize_user_level(user_id: int) -> int:
        """Synchronize user's level in database with their actual progression status."""
        calculated_level = ProgressService.calculate_progression_based_level(user_id)
        return ProgressService.update_user_level(user_id, calculated_level)


class ExperienceService:
    """Service for experience and level operations using UserSurahMM model."""

    @staticmethod
    @transaction.atomic
    def add_surah_experience(
        user_id: int, surah_number: int, experience_points: int
    ) -> Optional[transports.UserSurahRelation]:
        """Add experience points for a specific Surah."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        try:
            surah = models.Surah.objects.get(surah_number=surah_number)
        except models.Surah.DoesNotExist:
            raise NotFound(f"Surah {surah_number} not found")

        # Get or create UserSurahMM relationship
        user_surah, created = models.UserSurahMM.objects.get_or_create(
            user=user, surah=surah, defaults={"experience_points": 0}
        )
        user_surah.experience_points += experience_points
        user_surah.save()

        # Also update user's total XP
        ProgressService.update_user_total_xp(user_id, experience_points)

        # Synchronize user's level with their current progression status
        ProgressService.synchronize_user_level(user_id)

        return repositories.UserSurahRepository._make_user_surah_transport(user_surah)

    @staticmethod
    def get_user_surah_experience(user_id: int, surah_number: int) -> Optional[transports.UserSurahRelation]:
        """Get experience for a specific Surah."""
        return repositories.UserSurahRepository.get_user_surah_relation(user_id, surah_number)

    @staticmethod
    def get_user_all_experience(user_id: int) -> List[transports.UserSurahRelation]:
        """Get all Surah experience for a user."""
        return repositories.UserSurahRepository.get_user_surah_relations(user_id)

    @staticmethod
    @transaction.atomic
    def mark_surah_as_skipped(user_id: int, surah_number: int) -> Optional[transports.UserSurahRelation]:
        """Mark a surah as skipped without awarding any XP."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        try:
            surah = models.Surah.objects.get(surah_number=surah_number)
        except models.Surah.DoesNotExist:
            raise NotFound(f"Surah {surah_number} not found")

        # Get or create UserSurahMM relationship
        user_surah, created = models.UserSurahMM.objects.get_or_create(
            user=user, surah=surah, defaults={"experience_points": 0, "is_skipped": True}
        )

        # If it already exists, mark it as skipped
        if not created:
            user_surah.is_skipped = True
            user_surah.save()

        # Synchronize user's level with their current progression status
        ProgressService.synchronize_user_level(user_id)

        return repositories.UserSurahRepository._make_user_surah_transport(user_surah)


class GameCompletionService:
    """Service for game completion operations using new models."""

    @staticmethod
    @transaction.atomic
    def complete_step_by_step_game(
        user_id: int, surah_number: int, ayah_number: int
    ) -> Optional[transports.UserAyahRelation]:
        """Mark step-by-step game as completed for an Ayah."""
        # Validate Surah access
        if not SurahProgressionService.validate_surah_access(user_id, surah_number):
            raise PermissionDenied(f"Surah {surah_number} is not unlocked for this user")

        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        # Get the Ayah
        try:
            surah = models.Surah.objects.get(surah_number=surah_number)
            ayah = models.Ayah.objects.get(surah=surah, ayah_number=ayah_number)
        except models.Ayah.DoesNotExist:
            raise NotFound(f"Ayah {ayah_number} in Surah {surah_number} not found")

        # Get or create UserAyahMM relationship
        user_ayah, created = models.UserAyahMM.objects.get_or_create(
            user=user, ayah=ayah, defaults={"is_completed": False}
        )

        # Only award XP if this is a new completion
        if not user_ayah.is_completed:
            user_ayah.is_completed = True
            user_ayah.save()

            # Add experience points for step-by-step completion (20 XP per ayah)
            ExperienceService.add_surah_experience(
                user_id, surah_number, SurahProgressionService.STEP_BY_STEP_XP_PER_AYAH
            )

            return repositories.UserAyahRepository._make_user_ayah_transport(user_ayah)

        # Return None if already completed (no new XP awarded)
        return None

    @staticmethod
    def has_user_completed_game_with_ayah_count(
        user_id: int, surah_number: int, game_type: str, ayah_count: int
    ) -> bool:
        """Check if a user has already completed a game with the same number of ayahs."""
        try:
            _ = models.SurahGameCompletionMM.objects.get(
                user_id=user_id, surah__surah_number=surah_number, game_type=game_type, progress_ayah_count=ayah_count
            )
            return True
        except models.SurahGameCompletionMM.DoesNotExist:
            return False

    @staticmethod
    @transaction.atomic
    def complete_game(
        user_id: int, surah_number: int, game_type: str, ayah_count: int
    ) -> transports.SurahGameCompletion:
        """Mark a game as completed."""
        # Validate Surah access
        if not SurahProgressionService.validate_surah_access(user_id, surah_number):
            raise PermissionDenied(f"Surah {surah_number} is not unlocked for this user")

        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        try:
            surah = models.Surah.objects.get(surah_number=surah_number)
        except models.Surah.DoesNotExist:
            raise NotFound(f"Surah {surah_number} not found")

        # Validate game type
        valid_game_types = [choice[0] for choice in models.SurahGameCompletionMM.GAME_TYPES]
        if game_type not in valid_game_types:
            raise ValidationError(f"Invalid game type. Must be one of: {valid_game_types}")

        # Get the previous maximum ayah count for this game type
        previous_max_completion = (
            models.SurahGameCompletionMM.objects.filter(user=user, game_type=game_type, surah=surah)
            .order_by("-progress_ayah_count")
            .first()
        )

        if previous_max_completion:
            previous_max_completion_count = previous_max_completion.progress_ayah_count
        else:
            previous_max_completion_count = 0
        new_ayahs = ayah_count - previous_max_completion_count
        completion, created = models.SurahGameCompletionMM.objects.get_or_create(
            user=user, surah=surah, game_type=game_type, progress_ayah_count=ayah_count, new_ayahs=new_ayahs
        )

        if created:
            # Add experience points for new completion (5 XP per ayah for other games)
            experience_points = (completion.new_ayahs) * surah.xp_per_ayah
            ExperienceService.add_surah_experience(user_id, surah_number, experience_points)

        return repositories.SurahGameCompletionRepository._make_game_completion_transport(completion)

    @staticmethod
    def get_user_ayah_completions(user_id: int) -> List[transports.UserAyahRelation]:
        """Get all completed Ayahs for a user."""
        return repositories.UserAyahRepository.get_user_completed_ayahs(user_id)

    @staticmethod
    def get_user_completed_ayahs_for_surah(user_id: int, surah_number: int) -> List[transports.UserAyahRelation]:
        """Get all completed Ayahs for a user in a specific Surah."""
        completed_ayahs = GameCompletionService.get_user_ayah_completions(user_id)
        completed_ayahs = [c for c in completed_ayahs if c.ayah.surah_id == surah_number and c.is_completed]
        return completed_ayahs

    @staticmethod
    def get_user_game_completions(user_id: int) -> List[transports.SurahGameCompletion]:
        """Get all game completions for a user."""
        return repositories.SurahGameCompletionRepository.get_user_game_completions(user_id)


class PedagogicalProgressionService:
    """Service for managing custom pedagogical Surah progression order."""

    # Custom learning progression order (learning position -> surah number)
    PEDAGOGICAL_ORDER = [
        1,
        78,
        79,
        80,
        81,
        82,
        83,
        84,
        85,
        86,
        87,
        88,
        89,
        90,
        91,
        92,
        93,
        94,
        95,
        96,
        97,
        98,
        99,
        100,
        101,
        102,
        103,
        104,
        105,
        106,
        107,
        108,
        109,
        110,
        111,
        112,
        113,
        114,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        25,
        26,
        27,
        28,
        29,
        30,
        31,
        32,
        33,
        34,
        35,
        36,
        37,
        38,
        39,
        40,
        41,
        42,
        43,
        44,
        45,
        46,
        47,
        48,
        49,
        50,
        51,
        52,
        53,
        54,
        55,
        56,
        57,
        58,
        59,
        60,
        61,
        62,
        63,
        64,
        65,
        66,
        67,
        68,
        69,
        70,
        71,
        72,
        73,
        74,
        75,
        76,
        77,
    ]

    # Reverse mapping (surah number -> learning position)
    SURAH_TO_POSITION = {surah_num: pos + 1 for pos, surah_num in enumerate(PEDAGOGICAL_ORDER)}

    @staticmethod
    def get_surah_by_learning_position(position: int) -> int:
        """Get surah number by learning position (1-114)."""
        if position < 1 or position > 114:
            raise ValueError(f"Learning position must be between 1 and 114, got {position}")
        return PedagogicalProgressionService.PEDAGOGICAL_ORDER[position - 1]

    @staticmethod
    def get_learning_position_by_surah(surah_number: int) -> int:
        """Get learning position by surah number."""
        if surah_number < 1 or surah_number > 114:
            raise ValueError(f"Surah number must be between 1 and 114, got {surah_number}")
        return PedagogicalProgressionService.SURAH_TO_POSITION[surah_number]

    @staticmethod
    def get_next_surah_in_sequence(current_surah: int) -> Optional[int]:
        """Get the next surah in the pedagogical sequence."""
        try:
            current_position = PedagogicalProgressionService.get_learning_position_by_surah(current_surah)
            if current_position >= 114:
                return None  # Last surah in sequence
            return PedagogicalProgressionService.get_surah_by_learning_position(current_position + 1)
        except ValueError:
            return None

    @staticmethod
    def get_previous_surah_in_sequence(current_surah: int) -> Optional[int]:
        """Get the previous surah in the pedagogical sequence."""
        try:
            current_position = PedagogicalProgressionService.get_learning_position_by_surah(current_surah)
            if current_position <= 1:
                return None  # First surah in sequence
            return PedagogicalProgressionService.get_surah_by_learning_position(current_position - 1)
        except ValueError:
            return None

    @staticmethod
    def get_all_surahs_in_learning_order() -> List[int]:
        """Get all surahs in pedagogical learning order."""
        return PedagogicalProgressionService.PEDAGOGICAL_ORDER.copy()


class SurahProgressionService:
    """Service for managing Surah-based progression system with pedagogical ordering."""

    # Constants for the progression system
    STEP_BY_STEP_XP_PER_AYAH = 20
    UNLOCK_THRESHOLD_PERCENTAGE = 0.8  # all steb by step completed
    TOTAL_SURAHS = 114

    @staticmethod
    def get_total_possible_xp_for_surah(surah_number: int) -> int:
        """Calculate total possible experience points for a Surah."""
        try:
            surah = models.Surah.objects.get(surah_number=surah_number)
            return surah.ayah_count * (20 + 5 * 5)
        except models.Surah.DoesNotExist:
            # Fallback to QuranService for backward compatibility
            from games.services import QuranService

            surah_info = QuranService.get_surah_info(surah_number)
            if not surah_info:
                return 0

            # Total possible XP = verses × step-by-step XP per ayah
            return surah_info.verses_count * SurahProgressionService.STEP_BY_STEP_XP_PER_AYAH

    @staticmethod
    def get_xp_for_unlocking_new_level(surah_number: int) -> int:
        """Calculate amount of xp needed to unlock new Surah"""
        try:
            surah = models.Surah.objects.get(surah_number=surah_number)
            return surah.ayah_count * (20 + 5)
        except models.Surah.DoesNotExist:
            # Fallback to QuranService for backward compatibility
            from games.services import QuranService

            surah_info = QuranService.get_surah_info(surah_number)
            if not surah_info:
                return 0

            # Total possible XP = verses × step-by-step XP per ayah
            return surah_info.verses_count * SurahProgressionService.STEP_BY_STEP_XP_PER_AYAH

    @staticmethod
    def get_unlock_threshold_for_surah(surah_number: int) -> int:
        """Calculate XP threshold needed to unlock the next Surah."""
        needed_to_unlock = SurahProgressionService.get_xp_for_unlocking_new_level(surah_number)
        return int(needed_to_unlock * SurahProgressionService.UNLOCK_THRESHOLD_PERCENTAGE)

    @staticmethod
    def is_surah_unlocked(user_id: int, surah_number: int) -> bool:
        """Check if a Surah is unlocked for the user based on pedagogical progression."""
        # First surah in pedagogical order (Surah 1) is always unlocked
        if surah_number == 1:
            return True

        # Check if this surah itself was skipped - if so, it's always accessible
        current_surah_xp = ExperienceService.get_user_surah_experience(user_id, surah_number)
        if current_surah_xp and current_surah_xp.is_skipped:
            return True

        # Get the previous surah in pedagogical sequence
        previous_surah_number = PedagogicalProgressionService.get_previous_surah_in_sequence(surah_number)

        if previous_surah_number is None:
            # This shouldn't happen for valid surah numbers, but handle gracefully
            return False

        # Check if previous Surah in sequence has enough XP or was skipped
        previous_surah_xp = ExperienceService.get_user_surah_experience(user_id, previous_surah_number)

        if not previous_surah_xp:
            return False

        # If the previous surah was skipped, the current surah is unlocked
        if previous_surah_xp.is_skipped:
            return True

        # Otherwise, check if previous surah has enough XP
        threshold = SurahProgressionService.get_unlock_threshold_for_surah(previous_surah_number)
        return previous_surah_xp.experience_points >= threshold

    @staticmethod
    def get_user_current_surah(user_id: int) -> int:
        """Get the current Surah the user should be working on based on pedagogical progression."""
        # Check surahs in pedagogical order
        for position in range(1, SurahProgressionService.TOTAL_SURAHS + 1):
            surah_number = PedagogicalProgressionService.get_surah_by_learning_position(position)
            if not SurahProgressionService.is_surah_unlocked(user_id, surah_number):
                # Return the last unlocked surah in pedagogical sequence
                if position == 1:
                    return 1  # First surah is always available
                previous_position = position - 1
                return PedagogicalProgressionService.get_surah_by_learning_position(previous_position)

        # All Surahs unlocked - return the last surah in pedagogical sequence
        return PedagogicalProgressionService.get_surah_by_learning_position(SurahProgressionService.TOTAL_SURAHS)

    @staticmethod
    def get_next_unlock_info(user_id: int, surah_number: int) -> Optional[dict]:
        """Get information about unlocking the next Surah in pedagogical sequence."""
        # Get the next surah in pedagogical sequence
        next_surah_number = PedagogicalProgressionService.get_next_surah_in_sequence(surah_number)

        if next_surah_number is None:
            return None  # No next Surah in sequence

        current_xp = ExperienceService.get_user_surah_experience(user_id, surah_number)
        current_points = current_xp.experience_points if current_xp else 0

        threshold = SurahProgressionService.get_unlock_threshold_for_surah(surah_number)
        total_possible = SurahProgressionService.get_total_possible_xp_for_surah(surah_number)

        # Get learning positions for better context
        current_position = PedagogicalProgressionService.get_learning_position_by_surah(surah_number)
        next_position = PedagogicalProgressionService.get_learning_position_by_surah(next_surah_number)

        return {
            "next_surah_number": next_surah_number,
            "current_surah_position": current_position,
            "next_surah_position": next_position,
            "current_xp": current_points,
            "threshold_xp": threshold,
            "total_possible_xp": total_possible,
            "xp_needed": max(0, threshold - current_points),
            "progress_percentage": (current_points / threshold * 100) if threshold > 0 else 0,
            "is_unlocked": current_points >= threshold,
        }

    @staticmethod
    def validate_surah_access(user_id: int, surah_number: int) -> bool:
        """Validate that user has access to the specified Surah."""
        if surah_number < 1 or surah_number > SurahProgressionService.TOTAL_SURAHS:
            return False

        return SurahProgressionService.is_surah_unlocked(user_id, surah_number)

    @staticmethod
    def get_user_progress_in_learning_order(user_id: int) -> List[dict]:
        """Get user's progress for all surahs in pedagogical learning order."""
        progress_list = []

        for position in range(1, SurahProgressionService.TOTAL_SURAHS + 1):
            surah_number = PedagogicalProgressionService.get_surah_by_learning_position(position)

            # Get user's experience for this surah
            surah_experience = ExperienceService.get_user_surah_experience(user_id, surah_number)
            current_xp = surah_experience.experience_points if surah_experience else 0

            # Check if surah is unlocked
            is_unlocked = SurahProgressionService.is_surah_unlocked(user_id, surah_number)

            # Get threshold and total possible XP
            threshold = SurahProgressionService.get_unlock_threshold_for_surah(surah_number)
            total_possible = SurahProgressionService.get_total_possible_xp_for_surah(surah_number)

            progress_info = {
                "learning_position": position,
                "surah_number": surah_number,
                "current_xp": current_xp,
                "threshold_xp": threshold,
                "total_possible_xp": total_possible,
                "is_unlocked": is_unlocked,
                "progress_percentage": (current_xp / threshold * 100) if threshold > 0 else 0,
                "is_completed": current_xp >= threshold,
                "is_skipped": surah_experience.is_skipped if surah_experience else False,
            }

            progress_list.append(progress_info)

        return progress_list

    @staticmethod
    @transaction.atomic
    def skip_surah(user_id: int, surah_number: int) -> dict:
        """Skip a surah in the pedagogical progression without awarding XP."""
        # Validate user exists
        if not User.objects.filter(id=user_id).exists():
            raise NotFound("User not found")

        # Validate surah exists
        if not models.Surah.objects.filter(surah_number=surah_number).exists():
            raise NotFound(f"Surah {surah_number} not found")

        # Get user's current surah in pedagogical progression
        current_surah = SurahProgressionService.get_user_current_surah(user_id)

        # Validate that the requested surah is the user's current surah
        if surah_number != current_surah:
            if SurahProgressionService.is_surah_unlocked(user_id, surah_number):
                # Check if this surah was previously skipped - if so, allow re-skipping
                surah_experience = ExperienceService.get_user_surah_experience(user_id, surah_number)
                if not (surah_experience and surah_experience.is_skipped):
                    raise ValidationError(
                        f"Surah {surah_number} is already completed or accessible. Only the current surah can be skipped."
                    )
            else:
                raise PermissionDenied(
                    f"Surah {surah_number} is not your current surah. You can only skip your current surah ({current_surah})."
                )

        # Get the next surah in pedagogical sequence
        next_surah_number = PedagogicalProgressionService.get_next_surah_in_sequence(surah_number)
        if next_surah_number is None:
            raise ValidationError("Cannot skip the last surah in the sequence.")

        # Mark the surah as skipped without awarding any XP
        ExperienceService.mark_surah_as_skipped(user_id, surah_number)

        # Synchronize user's level with their current progression status (including the newly skipped surah)
        ProgressService.synchronize_user_level(user_id)

        # Get updated progression information
        new_current_surah = SurahProgressionService.get_user_current_surah(user_id)
        next_unlock_info = SurahProgressionService.get_next_unlock_info(user_id, next_surah_number)

        # Get learning positions
        skipped_position = PedagogicalProgressionService.get_learning_position_by_surah(surah_number)
        next_position = PedagogicalProgressionService.get_learning_position_by_surah(next_surah_number)

        return {
            "skipped_surah": {
                "surah_number": surah_number,
                "learning_position": skipped_position,
                "is_skipped": True,
            },
            "next_surah": {
                "surah_number": next_surah_number,
                "learning_position": next_position,
                "is_unlocked": SurahProgressionService.is_surah_unlocked(user_id, next_surah_number),
            },
            "new_current_surah": new_current_surah,
            "next_unlock_info": next_unlock_info,
        }

    @staticmethod
    def get_user_progression_summary(user_id: int) -> dict:
        """Get a comprehensive summary of user's progression."""
        current_surah = SurahProgressionService.get_user_current_surah(user_id)
        unlocked_surahs = []

        # Check all surahs in pedagogical order to include skipped ones
        for position in range(1, SurahProgressionService.TOTAL_SURAHS + 1):
            surah_number = PedagogicalProgressionService.get_surah_by_learning_position(position)
            if SurahProgressionService.is_surah_unlocked(user_id, surah_number):
                unlocked_surahs.append(surah_number)

        next_unlock = SurahProgressionService.get_next_unlock_info(user_id, current_surah)

        # Get user progress summary
        user_progress = ProgressService.get_user_progress_summary(user_id)

        return {
            "current_surah": current_surah,
            "unlocked_surahs": unlocked_surahs,
            "total_unlocked": len(unlocked_surahs),
            "next_unlock_info": next_unlock,
            "completion_percentage": (len(unlocked_surahs) / SurahProgressionService.TOTAL_SURAHS) * 100,
            "user_progress": user_progress,
        }


class ArabicLetterProgressService:
    """Service for Arabic letter progress tracking operations."""

    VALID_PROGRESS_STATUSES = ["New", "In progress", "Completed"]

    @staticmethod
    def get_user_letter_progress(user_id: int, letter_id: int) -> Optional[transports.UserArabicProgress]:
        """Get user progress for a specific Arabic letter."""
        return repositories.UserArabicProgressRepository.get_user_arabic_progress(user_id, letter_id)

    @staticmethod
    def get_all_user_progress(user_id: int) -> List[transports.UserArabicProgress]:
        """Get all Arabic letter progress for a user."""
        return repositories.UserArabicProgressRepository.get_all_user_arabic_progress(user_id)

    @staticmethod
    @transaction.atomic
    def update_letter_progress(user_id: int, letter_id: int, progress_status: str) -> transports.UserArabicProgress:
        """Update user progress for an Arabic letter."""
        # Validate progress status
        if progress_status not in ArabicLetterProgressService.VALID_PROGRESS_STATUSES:
            raise ValidationError(
                f"Invalid progress status. Must be one of: {ArabicLetterProgressService.VALID_PROGRESS_STATUSES}"
            )

        # Validate that the letter exists
        from quran.repositories import ArabicLetterRepository

        letter = ArabicLetterRepository.get_letter_by_id(letter_id)
        if not letter:
            raise NotFound(f"Arabic letter with ID {letter_id} not found")

        # Validate that the user exists
        try:
            User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound(f"User with ID {user_id} not found")

        # Update or create progress
        return repositories.UserArabicProgressRepository.create_or_update_progress(user_id, letter_id, progress_status)

    @staticmethod
    def get_progress_statistics(user_id: int) -> transports.ArabicLetterProgressStats:
        """Get detailed progress statistics for a user."""
        stats_dict = repositories.UserArabicProgressRepository.get_progress_stats(user_id)

        return transports.ArabicLetterProgressStats(
            total_available_letters=stats_dict["total_available_letters"],
            total_tracked_letters=stats_dict["total_tracked_letters"],
            new_letters=stats_dict["new_letters"],
            in_progress_letters=stats_dict["in_progress_letters"],
            completed_letters=stats_dict["completed_letters"],
            completion_percentage=stats_dict["completion_percentage"],
        )

    @staticmethod
    def get_enhanced_user_progress(user_id: int) -> List[transports.EnhancedUserArabicProgress]:
        """Get enhanced progress information including audio availability."""
        progress_list = repositories.UserArabicProgressRepository.get_all_user_arabic_progress(user_id)
        enhanced_progress = []

        for progress in progress_list:
            # Check if letter has audio
            has_audio = progress.arabic.audio_url is not None and progress.arabic.audio_url.strip() != ""

            # For now, practice_sessions is set to 0 - this could be enhanced later
            # by tracking actual practice sessions in a separate model
            practice_sessions = 0

            enhanced = transports.EnhancedUserArabicProgress(
                id=progress.id,
                progress_status=progress.progress_status,
                user_id=progress.user_id,
                arabic=progress.arabic,
                created_at=progress.created_at,
                has_audio=has_audio,
                practice_sessions=practice_sessions,
            )
            enhanced_progress.append(enhanced)

        return enhanced_progress

    @staticmethod
    def initialize_user_progress(user_id: int) -> List[transports.UserArabicProgress]:
        """Initialize progress tracking for all Arabic letters for a new user."""
        from quran.repositories import ArabicLetterRepository

        # Get all Arabic letters
        all_letters = ArabicLetterRepository.get_all_letters()

        # Create progress entries for letters that don't have progress yet
        created_progress = []
        for letter in all_letters:
            existing_progress = repositories.UserArabicProgressRepository.get_user_arabic_progress(user_id, letter.id)
            if not existing_progress:
                progress = repositories.UserArabicProgressRepository.create_or_update_progress(
                    user_id, letter.id, "New"
                )
                created_progress.append(progress)

        return created_progress

    @staticmethod
    @transaction.atomic
    def get_all_letters_progress_with_initialization(user_id: int) -> tuple[List[transports.UserArabicProgress], int]:
        """
        Get progress for all Arabic letters, auto-initializing missing ones.

        Returns:
            tuple: (list of progress records, count of newly initialized letters)
        """
        from quran.repositories import ArabicLetterRepository

        # Validate that the user exists
        try:
            User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound(f"User with ID {user_id} not found")

        # Get all Arabic letters
        all_letters = ArabicLetterRepository.get_all_letters()

        # Get existing progress
        existing_progress = repositories.UserArabicProgressRepository.get_all_user_arabic_progress(user_id)
        existing_letter_ids = {progress.arabic.id for progress in existing_progress}

        # Initialize missing progress records
        initialized_count = 0
        for letter in all_letters:
            if letter.id not in existing_letter_ids:
                repositories.UserArabicProgressRepository.create_or_update_progress(user_id, letter.id, "New")
                initialized_count += 1

        # Get all progress after initialization
        all_progress = repositories.UserArabicProgressRepository.get_all_user_arabic_progress(user_id)

        # Sort by letter ID to ensure consistent ordering
        all_progress.sort(key=lambda p: p.arabic.id)

        return all_progress, initialized_count

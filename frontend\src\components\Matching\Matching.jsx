import React, { useState, useEffect, useRef } from "react";
import "src/components/Matching/Matching.css";
import image from "src/assets/image_memory.svg";
import LoadingSpinner from "src/components/LoadingSpinner";
import { useParams } from "react-router-dom";
import "react-toastify/dist/ReactToastify.css";
import axiosInstance from "src/utils/axios";
import { toast, ToastContainer, Flip } from "react-toastify";
import GameCompletedBar from "src/components/GameCompletedBar/GameCompletedBar";
import { BACKEND_URL } from "src/utils/settings";
import useLanguageStore from "src/hooks/languageStore";

const Matching = ({ selectedAyah, updateProgress, backToLearn }) => {
  const [selectedText, setSelectedText] = useState(null);
  const [pairs, setPairs] = useState({});
  const [isCorrect, setIsCorrect] = useState(null);
  const canvasRef = useRef(null);
  const { id, ayahId } = useParams();
  const [loading, setLoading] = useState(true);

  const [textItems, setTextItems] = useState([]);
  const [imageItems, setImageItems] = useState([]);
  const [correctPairs, setCorrectPairs] = useState({});

  const language = useLanguageStore((state) => state.language);

  const resizeCanvas = () => {
    const canvas = canvasRef.current;
    const gameGrid = document.querySelector(".game-grid");

    if (gameGrid && canvas) {
      const gridRect = gameGrid.getBoundingClientRect();
      canvas.width = gridRect.width; // Автоматическая ширина
      canvas.height = gridRect.height; // Автоматическая высота
    }
  };

  // Отслеживание изменений размера контейнера
  useEffect(() => {
    const fetchGameData = async () => {
      try {
        const response = await axiosInstance.post("/games/matching/", {
          id,
          ayahId,
          selectedAyah,
        });

        setTextItems(response.data.textItems);
        setImageItems(response.data.imageItems);
        setLocalizedTextItems();
        setCorrectPairs(response.data.correctPairs);
        setLoading(false);
      } catch (error) {
        toast.error("Error fetching game data", error);
        setLoading(false);
      }
    };

    fetchGameData();
  }, [id, ayahId, selectedAyah]);

  useEffect(() => {
    const handleResize = () => {
      resizeCanvas(); // Изменяем размеры канваса при изменении размеров окна
    };

    window.addEventListener("resize", handleResize);

    // Убираем обработчик событий при размонтировании компонента
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    resizeCanvas(); // Устанавливаем размер канваса при загрузке
  }, [textItems, imageItems]);

  useEffect(() => {
    if (textItems.length > 0) {
      setLocalizedTextItems(); // Update localized text when language changes
    }
  }, [language]); // Only depends on language

  const setLocalizedTextItems = () => {
    setTextItems((prevItems) =>
      prevItems.map((item) => {
        const localizedContent =
          language === "en" ? item.english_text : item.russian_text;

        return { ...item, content: localizedContent };
        // return item;
      })
    );
  };

  const handleTextClick = (id) => {
    setSelectedText(id);
  };

  const handleImageClick = (imageId) => {
    if (selectedText) {
      const newPairs = { ...pairs };

      Object.keys(newPairs).forEach((textId) => {
        if (newPairs[textId] === imageId) {
          delete newPairs[textId];
        }
      });

      newPairs[selectedText] = imageId;
      setPairs(newPairs);
      setSelectedText(null);
    }
  };

  const handleSubmit = () => {
    let correct = true;
    Object.keys(correctPairs).forEach((textId) => {
      if (pairs[textId] !== correctPairs[textId]) {
        correct = false;
      }
    });
    setIsCorrect(correct);
    if (correct) {
      // toast.success("Correct!");
      updateProgress().catch((error) =>
        console.error("Error updating progress", error)
      );
    } else {
      // toast.error("Try Again!");
    }
  };

  const drawLines = () => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    Object.keys(pairs).forEach((textId) => {
      const textElement = document.getElementById(`text-${textId}`);
      const imageId = pairs[textId];
      const imageElement = document.getElementById(`image-${imageId}`);

      if (textElement && imageElement) {
        const textRect = textElement.getBoundingClientRect();
        const textCenterX = textRect.left + textRect.width;
        const textCenterY = textRect.top + textRect.height / 2;

        const imageRect = imageElement.getBoundingClientRect();
        const imageCenterX = imageRect.left;
        const imageCenterY = imageRect.top + imageRect.height / 2;

        const canvasRect = canvas.getBoundingClientRect();
        const startX = textCenterX - canvasRect.left;
        const startY = textCenterY - canvasRect.top;
        const endX = imageCenterX - canvasRect.left;
        const endY = imageCenterY - canvasRect.top;

        ctx.beginPath();
        ctx.moveTo(startX, startY);
        ctx.lineTo(endX, endY);
        ctx.strokeStyle = "green";
        ctx.lineWidth = 2;
        ctx.stroke();
      }
    });
  };

  useEffect(() => {
    if (!loading) {
      drawLines();
    }
  }, [pairs, loading]);

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <>
      <ToastContainer
        position="top-center"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />
      <div className="game-container">
        <div className="game-grid">
          {/* Canvas для отрисовки линий */}
          <canvas ref={canvasRef} className="canvas"></canvas>

          {/* Текстовые элементы */}
          <ul className="text-list">
            {textItems.map((item) => (
              <div
                key={item.id}
                id={`text-${item.id}`}
                className={`text-item ${
                  selectedText === item.id ? "selected" : ""
                }`}
                onClick={() => handleTextClick(item.id)}
              >
                <div className="ayah-text">{item.arabic_text}</div>
                <div className="translation-text">{item.content}</div>
                {/* {item.content} */}
              </div>
            ))}
          </ul>

          {/* Элементы изображений */}
          <ul className="image-list">
            {imageItems.map((item) => (
              <li
                key={item.id}
                id={`image-${item.id}`}
                className="image-item"
                onClick={() => handleImageClick(item.id)}
              >
                <img src={BACKEND_URL + item.src} alt={`Image ${item.id}`} />
              </li>
            ))}
          </ul>
        </div>
      </div>
      {isCorrect === true ? (
        <GameCompletedBar result={true} backToLearn={backToLearn} />
      ) : isCorrect === false ? (
        <>
          <GameCompletedBar result={false} backToLearn={backToLearn} />
        </>
      ) : (
        <></>
      )}
      {isCorrect === null ? (
        <div className="submit-container">
          <button className="submit-matching" onClick={handleSubmit}>
            Submit
          </button>
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default Matching;

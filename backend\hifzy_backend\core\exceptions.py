"""
Custom exceptions and error handling for the hifzy_backend project.

This module provides consistent error handling across the application
and custom exception classes for specific business logic errors.
"""

from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from rest_framework.exceptions import ValidationError, NotFound, PermissionDenied
from django.core.exceptions import ObjectDoesNotExist
from django.db import IntegrityError
import logging

from .utils import create_error_response

logger = logging.getLogger(__name__)


class BusinessLogicError(Exception):
    """Base exception for business logic errors."""
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code or 'business_logic_error'
        super().__init__(self.message)


class InsufficientPermissionsError(BusinessLogicError):
    """Exception raised when user lacks required permissions."""
    def __init__(self, message: str = "Insufficient permissions"):
        super().__init__(message, 'insufficient_permissions')


class ResourceNotFoundError(BusinessLogicError):
    """Exception raised when a requested resource is not found."""
    def __init__(self, message: str = "Resource not found"):
        super().__init__(message, 'resource_not_found')


class InvalidOperationError(BusinessLogicError):
    """Exception raised when an operation is invalid in the current context."""
    def __init__(self, message: str = "Invalid operation"):
        super().__init__(message, 'invalid_operation')


class DataIntegrityError(BusinessLogicError):
    """Exception raised when data integrity constraints are violated."""
    def __init__(self, message: str = "Data integrity violation"):
        super().__init__(message, 'data_integrity_error')


class ExternalServiceError(BusinessLogicError):
    """Exception raised when external service calls fail."""
    def __init__(self, message: str = "External service error"):
        super().__init__(message, 'external_service_error')


def custom_exception_handler(exc, context):
    """
    Custom exception handler that provides consistent error responses.
    
    This handler catches various types of exceptions and converts them
    to standardized error responses with appropriate HTTP status codes.
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # If DRF handled the exception, modify the response format
    if response is not None:
        custom_response_data = create_error_response(
            message=str(exc),
            errors=response.data if isinstance(response.data, dict) else None
        )
        response.data = custom_response_data
        return response
    
    # Handle custom business logic exceptions
    if isinstance(exc, BusinessLogicError):
        if isinstance(exc, ResourceNotFoundError):
            status_code = status.HTTP_404_NOT_FOUND
        elif isinstance(exc, InsufficientPermissionsError):
            status_code = status.HTTP_403_FORBIDDEN
        elif isinstance(exc, InvalidOperationError):
            status_code = status.HTTP_400_BAD_REQUEST
        elif isinstance(exc, DataIntegrityError):
            status_code = status.HTTP_409_CONFLICT
        elif isinstance(exc, ExternalServiceError):
            status_code = status.HTTP_502_BAD_GATEWAY
        else:
            status_code = status.HTTP_400_BAD_REQUEST
        
        return Response(
            create_error_response(exc.message, {'code': exc.code}),
            status=status_code
        )
    
    # Handle Django ORM exceptions
    if isinstance(exc, ObjectDoesNotExist):
        return Response(
            create_error_response("Resource not found"),
            status=status.HTTP_404_NOT_FOUND
        )
    
    if isinstance(exc, IntegrityError):
        logger.error(f"Database integrity error: {exc}")
        return Response(
            create_error_response("Data integrity violation"),
            status=status.HTTP_409_CONFLICT
        )
    
    # Handle unexpected exceptions
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return Response(
        create_error_response("An unexpected error occurred"),
        status=status.HTTP_500_INTERNAL_SERVER_ERROR
    )


class ValidationMixin:
    """Mixin class that provides common validation methods."""
    
    @staticmethod
    def validate_surah_id(surah_id: int) -> None:
        """Validate that surah_id is within valid range."""
        if not isinstance(surah_id, int) or surah_id < 1 or surah_id > 114:
            raise ValidationError("surah_id must be between 1 and 114")
    
    @staticmethod
    def validate_ayah_id(ayah_id: int) -> None:
        """Validate that ayah_id is positive."""
        if not isinstance(ayah_id, int) or ayah_id < 1:
            raise ValidationError("ayah_id must be a positive integer")
    
    @staticmethod
    def validate_progress_points(points: int) -> None:
        """Validate that progress points are within reasonable range."""
        if not isinstance(points, int) or points < 1 or points > 1000:
            raise ValidationError("progress_points must be between 1 and 1000")
    
    @staticmethod
    def validate_user_id(user_id: int) -> None:
        """Validate that user_id is positive."""
        if not isinstance(user_id, int) or user_id < 1:
            raise ValidationError("user_id must be a positive integer")
    
    @staticmethod
    def validate_game_type(game_type: str) -> None:
        """Validate that game_type is one of the allowed values."""
        valid_types = ['shuffling', 'matching', 'memory', 'audio']
        if game_type not in valid_types:
            raise ValidationError(f"game_type must be one of: {', '.join(valid_types)}")
    
    @staticmethod
    def validate_ayah_count(count: int) -> None:
        """Validate that ayah count is within reasonable range."""
        if not isinstance(count, int) or count < 1 or count > 50:
            raise ValidationError("ayah_count must be between 1 and 50")


def handle_service_errors(func):
    """
    Decorator that converts service layer exceptions to appropriate HTTP responses.
    
    This decorator should be used on view methods to ensure consistent
    error handling across the API.
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except BusinessLogicError as e:
            # Business logic errors are already properly formatted
            raise e
        except ValidationError as e:
            # DRF validation errors
            raise e
        except ObjectDoesNotExist as e:
            raise ResourceNotFoundError(str(e))
        except IntegrityError as e:
            logger.error(f"Database integrity error in {func.__name__}: {e}")
            raise DataIntegrityError("Data integrity violation")
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {e}", exc_info=True)
            raise BusinessLogicError("An unexpected error occurred")
    
    return wrapper


class ErrorCodes:
    """Constants for error codes used throughout the application."""
    
    # Authentication and authorization errors
    AUTHENTICATION_REQUIRED = 'authentication_required'
    INSUFFICIENT_PERMISSIONS = 'insufficient_permissions'
    INVALID_TOKEN = 'invalid_token'
    
    # Validation errors
    INVALID_INPUT = 'invalid_input'
    MISSING_REQUIRED_FIELD = 'missing_required_field'
    INVALID_FORMAT = 'invalid_format'
    
    # Business logic errors
    RESOURCE_NOT_FOUND = 'resource_not_found'
    RESOURCE_ALREADY_EXISTS = 'resource_already_exists'
    INVALID_OPERATION = 'invalid_operation'
    OPERATION_NOT_ALLOWED = 'operation_not_allowed'
    
    # Data errors
    DATA_INTEGRITY_VIOLATION = 'data_integrity_violation'
    FOREIGN_KEY_VIOLATION = 'foreign_key_violation'
    UNIQUE_CONSTRAINT_VIOLATION = 'unique_constraint_violation'
    
    # External service errors
    EXTERNAL_SERVICE_UNAVAILABLE = 'external_service_unavailable'
    EXTERNAL_SERVICE_ERROR = 'external_service_error'
    
    # System errors
    INTERNAL_SERVER_ERROR = 'internal_server_error'
    DATABASE_ERROR = 'database_error'

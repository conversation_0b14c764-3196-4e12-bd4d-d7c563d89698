from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .forms import HifzyUserCreationForm
from .models import Hifzy<PERSON><PERSON>, AyahReaction, AyahReport


class HifzyUserAdmin(UserAdmin):
    add_form = HifzyUserCreationForm
    model = HifzyUser
    fieldsets = (
        *UserAdmin.fieldsets,
        ("Extra Fields", {"fields": ("profile_photo", "total_xp", "current_level", "day_streak", "league")}),
    )
    list_display = [
        "email",
        "username",
        "total_xp",
        "current_level",
        "day_streak",
    ]


admin.site.register(HifzyUser, HifzyUserAdmin)


@admin.register(AyahReaction)
class AyahReactionAdmin(admin.ModelAdmin):
    list_display = ["user", "ayah", "reaction_type", "created_at", "updated_at"]
    list_filter = ["reaction_type", "created_at", "ayah__surah"]
    search_fields = ["user__username", "user__email"]
    ordering = ["-created_at"]


@admin.register(AyahReport)
class AyahReportAdmin(admin.ModelAdmin):
    list_display = ["user", "ayah", "complaint_type", "created_at"]
    list_filter = ["complaint_type", "created_at", "ayah__surah"]
    search_fields = ["user__username", "user__email", "description"]
    ordering = ["-created_at"]
    readonly_fields = ["created_at"]

from rest_framework import serializers


class GameRequestSerializer(serializers.Serializer):
    """
    Enhanced serializer for game request data with flexible ayah selection.

    Allows users to specify a starting ayah and request consecutive ayahs for games.
    """

    surah_id = serializers.IntegerField(required=True, min_value=1, max_value=114, help_text="ID of the Surah (1-114)")
    start_ayah_number = serializers.IntegerField(
        required=True, min_value=1, help_text="The ayah number to start from within the specified Surah"
    )
    count = serializers.IntegerField(
        required=True,
        min_value=1,
        max_value=50,
        help_text="Number of consecutive ayahs to include in the game (max 50)",
    )

    def validate_surah_id(self, value):
        """Validate surah ID is within valid range."""
        if value < 1 or value > 114:
            raise serializers.ValidationError("Surah ID must be between 1 and 114")
        return value

    def validate_start_ayah_number(self, value):
        """Validate starting ayah number is positive."""
        if value < 1:
            raise serializers.ValidationError("Starting ayah number must be positive")
        return value

    def validate_count(self, value):
        """Validate count is within reasonable limits."""
        if value < 1:
            raise serializers.ValidationError("Count must be at least 1")
        if value > 50:
            raise serializers.ValidationError("Count cannot exceed 50 ayahs per game")
        return value

    def validate(self, attrs):
        """Cross-field validation to ensure ayah range is valid."""
        # Note: We'll do detailed surah-specific validation in the service layer
        # where we have access to the actual surah data
        return attrs


# Keep the old serializer for backward compatibility
class LegacyGameRequestSerializer(serializers.Serializer):
    """
    Legacy serializer for backward compatibility.

    Validates and processes the input data for various Quran-based games.
    """
    id = serializers.IntegerField(required=True, help_text="ID of the Surah")
    ayah_id = serializers.IntegerField(required=True, help_text="ID of the Ayah within the Surah")
    selectedAyah = serializers.IntegerField(required=True, help_text="Number of Ayahs to include in the game")




class WordAudioRequestSerializer(serializers.Serializer):
    """
    Serializer for word audio request.
    
    Validates the input data for retrieving the audio URL for a specific word in the Quran.
    """
    surah_number = serializers.IntegerField(required=True, help_text="Number of the Surah (1-114)")
    ayah_number = serializers.IntegerField(required=True, help_text="Number of the Ayah within the Surah")
    
    def validate_surah_number(self, value):
        if value < 1 or value > 114:
            raise serializers.ValidationError("Surah number must be between 1 and 114")
        return value
    
    def validate_ayah_number(self, value):
        if value < 1:
            raise serializers.ValidationError("Ayah number must be positive")
        return value


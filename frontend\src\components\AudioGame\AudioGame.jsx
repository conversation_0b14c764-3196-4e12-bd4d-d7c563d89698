import React, { useState, useEffect, useRef } from "react";
import "src/components/AudioGame/AudioGame.css";
import { useParams } from "react-router-dom";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import axiosInstance from "src/utils/axios";
import LoadingSpinner from "src/components/LoadingSpinner";
import playSound from "src/assets/play-audio.svg";
import pauseSound from "src/assets/pause-icon.svg";
import dynamicIcon from "src/assets/sound-icon.svg";
import mutedIcon from "src/assets/muted-icon.svg";
import GameCompletedBar from "src/components/GameCompletedBar/GameCompletedBar";

function shuffleArray(array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}

function AudioGame({ selectedAyah, updateProgress, backTo<PERSON>earn }) {
  const { id, ayahId } = useParams();
  const [loading, setLoading] = useState(true);
  const [audioLoading, setAudioLoading] = useState(true);
  const [gameData, setGameData] = useState(null);
  const [selectedText, setSelectedText] = useState(null);
  const [isCorrectAnswer, setIsCorrectAnswer] = useState(null);
  const [isGameOver, setIsGameOver] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [randomLevels, setRandomLevels] = useState([]);
  const [currentLevelIndex, setCurrentLevelIndex] = useState(0);

  const audioRef = useRef(null);
  const progressRef = useRef(null);

  useEffect(() => {
    // Initialize random levels
    const levels = Array.from({ length: selectedAyah }, (_, i) => i + 1);
    const shuffledLevels = shuffleArray(levels);
    setRandomLevels(shuffledLevels);
    setCurrentLevelIndex(0);
  }, [selectedAyah]);

  useEffect(() => {
    const fetchGameData = async () => {
      setLoading(true);
      try {
        const response = await axiosInstance.post("/games/audio/", {
          id,
          ayahId: randomLevels[currentLevelIndex],
          selectedAyah,
        });
        setGameData(response.data);
        setLoading(false);
        setAudioLoading(true);
        setIsCorrectAnswer(null);
      } catch (error) {
        toast.error("Error fetching data from API");
        console.log(error);
        setLoading(false);
      }
    };

    if (randomLevels.length > 0) {
      fetchGameData();
    }
  }, [id, ayahId, currentLevelIndex, randomLevels]);

  const handlePlayPause = () => {
    if (audioRef.current.paused) {
      audioRef.current.play();
      setIsPlaying(true);
    } else {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const handleVolumeChange = () => {
    if (audioRef.current.volume === 1) {
      audioRef.current.volume = 0;
      setIsMuted(true);
    } else {
      audioRef.current.volume = 1;
      setIsMuted(false);
    }
  };

  const handleProgress = () => {
    if (audioRef.current && progressRef.current) {
      const progress =
        (audioRef.current.currentTime / audioRef.current.duration) * 100;
      progressRef.current.style.width = `${progress}%`;
    }
  };

  const handleTextClick = (text) => {
    if (isCorrectAnswer !== null) return;

    setSelectedText(text);
    const isCorrect = text === gameData.correct_answer;
    setIsCorrectAnswer(isCorrect);

    if (isCorrect) {
      if (currentLevelIndex + 1 >= randomLevels.length) {
        setIsGameOver(true);
        updateProgress().catch((error) =>
          console.error("Error updating progress", error)
        );
      }
    }
  };

  const handleNextLevel = () => {
    setCurrentLevelIndex((prev) => prev + 1);
    setSelectedText(null);
    setIsCorrectAnswer(null);
  };

  const handleRestartGame = () => {
    const shuffledLevels = shuffleArray(
      Array.from({ length: selectedAyah }, (_, i) => i + 1)
    );
    setRandomLevels(shuffledLevels);
    setCurrentLevelIndex(0);
    setIsGameOver(false);
    setSelectedText(null);
    setIsCorrectAnswer(null);
  };

  const handleRetry = () => {
    handleRestartGame();
  };

  const handleAudioLoadStart = () => {
    setAudioLoading(true);
  };

  const handleAudioCanPlay = () => {
    setAudioLoading(false);
  };

  const handleAudioError = () => {
    toast.error("Ошибка загрузки аудио");
    setAudioLoading(false);
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <>
      <ToastContainer
        position="top-center"
        autoClose={5000}
        hideProgressBar={false}
        theme="colored"
      />
      <section className="audio-game">
        <div className="audio-text-container">
          {audioLoading && <LoadingSpinner />}
          <div className="audio-player">
            <div className="play-button" onClick={handlePlayPause}>
              <img src={isPlaying ? pauseSound : playSound} alt="" />
            </div>
            <div className="volume-button" onClick={handleVolumeChange}>
              <img src={isMuted ? mutedIcon : dynamicIcon} alt="" />
            </div>
            <div className="progress-bar">
              <div className="progress" ref={progressRef}></div>
            </div>
          </div>

          <audio
            ref={audioRef}
            onLoadStart={handleAudioLoadStart}
            onCanPlayThrough={handleAudioCanPlay}
            onError={handleAudioError}
            onTimeUpdate={handleProgress}
            src={gameData.audio_url}
            style={{ display: "none" }}
          />

          {gameData.options.map((option, index) => (
            <div
              key={index}
              className={`audio-text ${
                selectedText === option
                  ? isCorrectAnswer
                    ? "correct"
                    : "incorrect"
                  : ""
              }`}
              onClick={() => handleTextClick(option)}
            >
              <div className="audio-text-content">
                <a className="audio-text-number">{index + 1}</a>
                <a className="audio-text-text">{option}</a>
              </div>
            </div>
          ))}

          {isCorrectAnswer !== null && !isGameOver && (
            <div
              className={`result-container ${
                isCorrectAnswer ? "correct" : "incorrect"
              }`}
            >
              <p>{isCorrectAnswer ? "Correct answer!" : "Incorrect answer!"}</p>
              {isCorrectAnswer ? (
                <button
                  onClick={handleNextLevel}
                  style={{ backgroundColor: "#A5DEA0" }}
                >
                  Continue
                </button>
              ) : (
                <button
                  onClick={handleRetry}
                  style={{ backgroundColor: "#E27878" }}
                >
                  Try again
                </button>
              )}
            </div>
          )}

          {isGameOver && (
            <GameCompletedBar result={true} backToLearn={backToLearn} />
          )}
        </div>
      </section>
    </>
  );
}

export default AudioGame;

"""
Transport objects for the games app.

These dataclasses represent the API response models, decoupled from Django models.
They define the structure of data that will be serialized to JSON and sent to clients.
"""

from dataclasses import dataclass
from typing import List, Optional



@dataclass(frozen=True)
class GamePhrase:
    """A phrase used in games."""
    id: str
    text: str


@dataclass(frozen=True)
class GameMetadata:
    """Metadata for game responses."""

    surah_id: int
    surah_name: str
    start_ayah_number: int
    end_ayah_number: int
    total_ayahs_requested: int
    total_ayahs_returned: int
    total_ayahs_in_surah: int
    max_xp_awarded: int


@dataclass(frozen=True)
class EnhancedAyahData:
    """Enhanced ayah data for games."""

    ayah_number: int
    arabic_text: str
    transliteration: str
    translation: str
    image_url: Optional[str] = None


@dataclass(frozen=True)
class UnifiedAyahData:
    """Unified ayah data structure for memory and matching games."""

    ayah_number: int
    arabic_text: str
    translation: str
    transliteration: str
    image_url: Optional[str] = None


@dataclass(frozen=True)
class PairingImageObject:
    """Image object for unified pairing system in memory and matching games."""

    image_url: str
    pair_id: str


@dataclass(frozen=True)
class PairingTextObject:
    """Text object for unified pairing system in memory and matching games."""

    arabic_text: str
    translation: str
    transliteration: str
    pair_id: str
    ayah_number: int


@dataclass(frozen=True)
class ShufflingGameResponse:
    """Enhanced response for shuffling game with direct data structure."""

    metadata: GameMetadata
    sentence: str
    phrases: List[GamePhrase]
    correct_sequence: List[str]


@dataclass(frozen=True)
class UnifiedGameItem:
    """Unified game item for memory and matching games."""

    id: str
    ayah_data: UnifiedAyahData
    item_type: str  # 'text' or 'image'


@dataclass(frozen=True)
class MatchingGameResponse:
    """Enhanced response for matching game with unified pairing system."""

    metadata: GameMetadata
    images: List[PairingImageObject]
    texts: List[PairingTextObject]


@dataclass(frozen=True)
class MemoryGameResponse:
    """Enhanced response for memory game with unified pairing system."""

    metadata: GameMetadata
    images: List[PairingImageObject]
    texts: List[PairingTextObject]


# Legacy transport objects for backward compatibility
@dataclass(frozen=True)
class TextItem:
    """Text item for matching game (legacy)."""
    id: str
    arabic_text: str
    translated_text: str


@dataclass(frozen=True)
class ImageItem:
    """Image item for matching game (legacy)."""
    id: str
    image_url: str


@dataclass(frozen=True)
class MemoryCard:
    """Memory card for memory game (legacy)."""

    id: str
    content: str
    type: str  # 'text' or 'image'
    ayah_number: Optional[int] = None


@dataclass(frozen=True)
class GapsLevel:
    """Level for gaps game with complete word reconstruction."""

    level_number: int
    ayah_number: int
    total_gaps: int  # Total number of word slots to fill
    word_options: List[str]  # All words from the ayah (shuffled)
    correct_sequence: List[str]  # Complete ordered list of words forming the original ayah
    original_text: str  # Original ayah text for reference
    audio_url: str
    transliteration_text: str


@dataclass(frozen=True)
class AudioLevel:
    """Level for audio game with individual audio challenge."""

    level_number: int
    ayah_number: int
    audio_url: str
    options: List[str]
    correct_answer: str


@dataclass(frozen=True)
class GapsGameResponse:
    """Enhanced response for gaps game with multi-level support."""

    metadata: GameMetadata
    levels: List[GapsLevel]


@dataclass(frozen=True)
class AudioGameResponse:
    """Enhanced response for audio game with multi-level support."""

    metadata: GameMetadata
    levels: List[AudioLevel]


# Arabic Letter Game Transport Objects
@dataclass(frozen=True)
class ArabicLetterGameMetadata:
    """Metadata for Arabic letter games."""

    letter_id: int
    letter_title: str
    letter_transcription: str
    game_type: str
    total_options: int


@dataclass(frozen=True)
class TransliterationOption:
    """Transliteration option for audio recognition game."""

    id: str
    transliteration_text: str
    is_correct: bool


@dataclass(frozen=True)
class AudioRecognitionGameResponse:
    """Response for Arabic letter audio recognition game."""

    metadata: ArabicLetterGameMetadata
    target_letter: str
    target_audio_url: str
    transliteration_options: List[TransliterationOption]
    correct_transliteration: str


@dataclass(frozen=True)
class LetterOption:
    """Letter option for transcription matching game."""

    id: str
    letter_title: str
    letter_id: int
    is_correct: bool


@dataclass(frozen=True)
class TranscriptionMatchingGameResponse:
    """Response for Arabic letter transcription matching game."""

    metadata: ArabicLetterGameMetadata
    target_transcription: str
    letter_options: List[LetterOption]
    correct_letter_id: int

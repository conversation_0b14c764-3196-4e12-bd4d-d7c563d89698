.card {
  position: relative;
  width: 550px;
  height: 550px;
  overflow: hidden;
  border-radius: 20px;
  box-shadow: 15px 15px 35px 1px rgba(0, 0, 0, 0.4);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin-top: 50px;
}

.card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  opacity: 1;
  transition: opacity 0.3s ease;
}

.card:hover::after {
  opacity: 0;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
}

.card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  color: white;
  text-align: left;
}

.card-title {
  font-weight: 600;
  font-size: 32px;
  margin-bottom: 16px;
}

.card-text {
  font-weight: 200;
  font-size: 20px;
  width: 75%;
  margin-top: 8px;
}

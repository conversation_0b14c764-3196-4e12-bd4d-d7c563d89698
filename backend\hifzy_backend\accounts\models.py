from django.db import models
from django.contrib.auth.models import AbstractUser
from social.models import League
from core.settings.base import AUTH_USER_MODEL


class HifzyUser(AbstractUser):
    GENDER_CHOICES = [
        ("male", "Male"),
        ("female", "Female"),
    ]

    profile_photo = models.CharField(max_length=500, blank=True, null=True, help_text="Profile photo URL")
    total_xp = models.IntegerField(default=0, help_text="Total experience points")
    current_level = models.IntegerField(default=1, help_text="Current user level")
    day_streak = models.IntegerField(default=0, help_text="Current day streak")
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, blank=True, null=True, help_text="User's gender")
    league_pk = models.ForeignKey(
        League,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="users",
        help_text="User's current league",
    )

    def __str__(self):
        return self.username


class AyahReaction(models.Model):
    """Model for user reactions (likes/dislikes) to ayahs."""

    REACTION_CHOICES = [
        ("like", "Like"),
        ("dislike", "Dislike"),
    ]

    user = models.ForeignKey(AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="ayah_reactions")
    ayah = models.ForeignKey("quran.Ayah", on_delete=models.CASCADE, related_name="reactions")
    reaction_type = models.CharField(max_length=10, choices=REACTION_CHOICES, help_text="Type of reaction")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("user", "ayah")
        verbose_name = "Ayah Reaction"
        verbose_name_plural = "Ayah Reactions"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.user.username} - {self.ayah} - {self.reaction_type}"


class AyahReport(models.Model):
    """Model for user reports on ayahs."""

    COMPLAINT_CHOICES = [
        ("inappropriate_content", "Inappropriate content"),
        ("translation_error", "Translation error"),
        ("audio_quality_issues", "Audio quality issues"),
        ("technical_problems", "Technical problems"),
        ("other", "Other"),
    ]

    user = models.ForeignKey(AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="ayah_reports")
    ayah = models.ForeignKey("quran.Ayah", on_delete=models.CASCADE, related_name="reports")
    complaint_type = models.CharField(max_length=30, choices=COMPLAINT_CHOICES, help_text="Type of complaint")
    description = models.TextField(blank=True, null=True, help_text="Optional detailed description")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("user", "ayah")
        verbose_name = "Ayah Report"
        verbose_name_plural = "Ayah Reports"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.user.username} - {self.ayah} - {self.complaint_type}"

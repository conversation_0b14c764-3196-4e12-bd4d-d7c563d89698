import React from "react";
import Logo from "src/components/Logo";
import "src/pages/Register/RegisterPage.css";
import {
  InputField,
  RadioButton,
  GoogleButton,
  SubmitButton,
} from "src/components/AuthInputs";

import { Link } from "react-router-dom";

function RegisterPage() {
  return (
    <>
      <div className="body-container">
        <div className="logo-container">
          <Logo />
        </div>
        <div className="relative-container">
          <a className="main">Create an account</a>
          <div className="subtitle">
            Already have an account? <a href="#">Log in</a>
          </div>
          <form>
            <div className="radio-group">
              <RadioButton
                name="gender"
                value="male"
                label="Male"
                required={true}
              />

              <RadioButton
                name="gender"
                value="female"
                label="Female"
                required={true}
              />
            </div>
            <InputField id="username" label="Username" type="text" />
            <InputField id="email" label="Email address" type="email" />
            <InputField id="password" label="Password" type="password" />
            <SubmitButton text="Create an account" />
          </form>
          <div className="or-text">Or</div>
          <div className="google-container">
            <Link to="/pickpath">
              <GoogleButton />
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}

export default RegisterPage;

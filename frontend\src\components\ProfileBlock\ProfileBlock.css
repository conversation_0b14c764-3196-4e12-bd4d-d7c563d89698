.my-profile-container {
  height: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.horizontal-my-profile {
  display: flex;
  justify-content: flex-start;
  flex-direction: row;
  align-items: center;
  background-color: #fbfbfb;
  filter: drop-shadow(6px 6px 15px rgba(134, 134, 134, 0.2));
  border-radius: 30px;
  padding: 20px;
  column-gap: 20px;
  height: 100%;
}

.main-profile-img {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
}

.profile-info {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-direction: column;
}

.top-profile-info {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.profile-title {
  font-size: 20px;
  font-weight: 500;
}

.extra-info {
  font-size: 16px;
  color: #828282;
}

.date-profile {
  font-size: 16px;
  color: #616161;
}

.follow-profile-info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  color: #97cb9c;
  gap: 20px;
}

.profile-field {
  display: flex;
  align-items: center;
}

.edit-icon {
  margin-left: 8px;
  cursor: pointer;
}

.save-btn,
.cancel-btn {
  margin-left: 8px;
  cursor: pointer;
  border-radius: 10px;
  font-size: 15px;
  padding-left: 10px;
  padding-right: 10px;
}

.save-btn {
  background-color: #97cb9c;
}

.cancel-btn {
  background-color: #828282;
}

.name-input {
  border: none;
  border-bottom: 1px solid #97cb9c;
  background-color: transparent;
  outline: none;
  padding: 5px;
  font-size: 16px;
  font-weight: 500;
}

.surname-input {
  border: none;
  border-bottom: 1px solid #97cb9c;
  background-color: transparent;
  outline: none;
  padding: 5px;
  font-size: 16px;
  font-weight: 500;
}

.profile-image-container {
  position: relative;
  width: 150px;
  height: 150px;
}

.main-profile-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* .profile-image-container:hover .main-profile-img {
  opacity: 0.5; 
} */

.upload-photo-btn {
  display: none; /* Hidden by default */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%; /* Match the profile image size */
  height: 100%; /* Match the profile image size */

  color: transparent;
  border: none;
  cursor: pointer;
  border-radius: 50%; /* Circular shape */
  font-size: 16px; /* Adjust font size to fit better */
  display: flex;
  justify-content: center;
  align-items: center; /* Center the text inside the button */
  text-align: center;
  padding: 0;
}

.profile-image-container:hover .upload-photo-btn {
  display: flex;
  transition: background-color 0.2s ease-in-out;
  background-color: rgba(31, 121, 26, 0.7);
  color: white;
}

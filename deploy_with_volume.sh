#!/bin/bash

# Hifzy Django Application Deployment Script
# This script helps deploy the Hifzy application using Docker Compose

set -e

echo "🚀 Starting Hifzy Django Application Deployment"

# Check if Docker and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Stop and remove existing containers
echo "🛑 Stopping and removing existing containers..."
docker-compose down

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from template..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration before continuing."
    echo "   Especially update DJANGO_SECRET_KEY and DB_PASSWORD"
    read -p "Press Enter to continue after editing .env file..."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p backend/hifzy_backend/staticfiles
mkdir -p backend/hifzy_backend/media

# Build and start services
echo "🔨 Building Docker images..."
docker-compose build

echo "🚀 Starting services..."
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Collect static
echo "Collecting static files..."
docker-compose exec web python hifzy_backend/manage.py collectstatic --noinput --env=docker

# Run makemigrations
echo "🗃️  Making migrations..."
docker-compose exec web python hifzy_backend/manage.py makemigrations --env=docker

# Run migrations
echo "🗃️  Running database migrations..."
docker-compose exec web python hifzy_backend/manage.py migrate --env=docker

echo "✅ Deployment completed successfully!"
echo ""
echo "🌐 Your application is now running at:"
echo "   - Main application: http://localhost"
echo "   - Django admin: http://localhost/admin/"
echo "   - API documentation: http://localhost/swagger/"
echo ""
echo "📊 To view logs:"
echo "   docker-compose logs -f"
echo ""
echo "🛑 To stop the application:"
echo "   docker-compose down"
echo ""
echo "🔄 To restart the application:"
echo "   docker-compose restart"

from django.urls import path
from .views import (
    CompleteStepByStepGameView,
    CompleteGameView,
    GetSurahProgressView,
    UserProgressPedagogicalOrderView,
    SkipSurahView,
    GetUserProgressionSummaryView,
    ArabicLetterProgressView,
    UpdateArabicLetterProgressView,
    ArabicLetterProgressStatsView,
    AllArabicLettersProgressView,
)

urlpatterns = [
    # Experience System
    path("experience/complete-step-by-step/", CompleteStepByStepGameView.as_view(), name="complete-step-by-step-game"),
    path("experience/complete-game/", CompleteGameView.as_view(), name="complete-game"),
    path("experience/surah-progress/", GetSurahProgressView.as_view(), name="get-surah-progress"),
    path(
        "experience/pedagogical-progress/",
        UserProgressPedagogicalOrderView.as_view(),
        name="user-progress-pedagogical-order",
    ),
    path("experience/skip-surah/", SkipSurahView.as_view(), name="skip-surah"),
    path(
        "experience/progression-summary/", GetUserProgressionSummaryView.as_view(), name="get-user-progression-summary"
    ),
    # Arabic Letter Progress System
    path("arabic-letters/", ArabicLetterProgressView.as_view(), name="arabic-letter-progress"),
    path("update-arabic-letters/", UpdateArabicLetterProgressView.as_view(), name="update-arabic-letter-progress"),
    path("arabic-letters/all/", AllArabicLettersProgressView.as_view(), name="all-arabic-letters-progress"),
    path("arabic-letters/stats/", ArabicLetterProgressStatsView.as_view(), name="arabic-letter-progress-stats"),
]

import React from "react";

export const InputField = ({ id, label, type }) => (
  <div
    className="input-container"
    style={{ width: "75%", margin: "10px auto", textAlign: "left" }}
  >
    <label
      htmlFor={id}
      style={{
        display: "block",
        marginBottom: "5px",
        fontSize: "14px",
        color: "#666",
      }}
    >
      {label}
    </label>
    <input
      type={type}
      id={id}
      required
      style={{
        width: "100%",
        padding: "12px",
        border: "1.5px solid #ddd",
        borderRadius: "10px",
        // boxSizing: "border-box",
        backgroundColor: "#fafafa",
        color: "#111111",
      }}
    />
  </div>
);

export const RadioButton = ({ name, value, label, required }) => (
  <label>
    <input type="radio" name={name} value={value} required={required} /> {label}
  </label>
);

export const GoogleButton = () => (
  <button
    className="google-btn"
    style={{
      display: "inline-flex",
      alignItems: "center",
      backgroundColor: "white",
      border: "none",
      borderRadius: "24px",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
      padding: "10px 20px",
      fontSize: "16px",
      color: "#3c4043",
      cursor: "pointer",
      transition: "0.2s",
      transform: "scale(1)",
    }}
    onMouseOver={(e) => (e.currentTarget.style.transform = "scale(1.05)")}
    onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
  >
    <svg
      className="google-icon"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 48 48"
    >
      <path
        fill="#EA4335"
        d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
      />
      <path
        fill="#4285F4"
        d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
      />
      <path
        fill="#FBBC05"
        d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
      />
      <path
        fill="#34A853"
        d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
      />
      <path fill="none" d="M0 0h48v48H0z" />
    </svg>
    Log in with Google
  </button>
);

export const SubmitButton = ({ text }) => (
  <button
    type="submit"
    style={{
      width: "75%",
      padding: "15px",
      backgroundColor: "#666",
      color: "white",
      border: "none",
      borderRadius: "24px",
      cursor: "pointer",
      marginTop: "20px",
      transition: "0.2s",
      transform: "scale(1)",
    }}
    onMouseOver={(e) => (e.currentTarget.style.transform = "scale(1.05)")}
    onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
  >
    {text}
  </button>
);

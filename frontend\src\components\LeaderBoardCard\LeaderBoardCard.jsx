import React from "react";
import demoProfilePic from "src/assets/demo_profile_pic.png";
import demoProfilePic2 from "src/assets/demo_profile_pic2.png";
import demoProfilePic3 from "src/assets/demo_profile_pic3.png";
import "src/components/LeaderBoardCard/LeaderBoardCard.css";
import { useTranslation } from "react-i18next";

function LeaderBoardCard() {
  const { t } = useTranslation();
  const competitors = [
    { rank: 1, name: "al<PERSON> muku<PERSON>", xp: "620xp", img: demoProfilePic },
    { rank: 2, name: "user2", xp: "570xp", img: demoProfilePic2 },
    {
      rank: 3,
      name: "user3",
      xp: "500xp",
      img: demoProfilePic3,
    },
  ];

  return (
    <div className="leader-boards-container">
      <a className="lb-title">{t("leaderboards_left_menu").toUpperCase()}</a>
      <div className="competitors-container">
        {competitors.map((competitor, index) => (
          <div key={index} className="competitor-container">
            <div className="left-part-com-cont">
              <a className="lb-numb">{competitor.rank}</a>
              <img src={competitor.img} alt="profile pic" />
            </div>
            <a href="" className="comp-name">
              {competitor.name}
            </a>
            <a href="" className="xp">
              {competitor.xp}
            </a>
          </div>
        ))}
      </div>
      <button className="show-more">{t("show_more_btn")}</button>
    </div>
  );
}

export default LeaderBoardCard;

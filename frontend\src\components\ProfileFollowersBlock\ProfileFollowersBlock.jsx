import React from "react";
import demoProfilePic from "src/assets/demo_profile_pic.png";
import demoProfilePic2 from "src/assets/demo_profile_pic2.png";
import demoProfilePic3 from "src/assets/demo_profile_pic3.png";
import "src/components/ProfileFollowersBlock/ProfileFollowersBlock.css";
import { useTranslation } from "react-i18next";
function ProfileFollowersBlock() {
  const { t } = useTranslation();
  const profiles = [
    { name: "alina", pic: demoProfilePic },
    { name: "oleg", pic: demoProfilePic2 },
    {
      name: "zlata",
      pic: demoProfilePic3,
    },
  ];
  return (
    <div className="follow-container">
      <div className="follower-followers">
        <a className="ff-text ff-chosen">{t("profile_page_following")}</a>
        <a className="ff-text">{t("profile_page_followers")}</a>
      </div>
      {profiles.map((profile, index) => (
        <div key={index} className="ff-profile">
          <img
            src={profile.pic}
            className="ff-profile-pic"
            alt={`${profile.name}'s profile`}
          />
          <a className="ff-profile-name">{profile.name}</a>
        </div>
      ))}
    </div>
  );
}

export default ProfileFollowersBlock;

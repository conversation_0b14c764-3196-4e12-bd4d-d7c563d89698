import { create } from "zustand";
import { persist } from "zustand/middleware";
import i18n from "i18next";

const detectDefaultLanguage = () => {
  const detectedLanguage =
    i18n.language || navigator.language.split("-")[0] || "en";
  return detectedLanguage;
};

const useLanguageStore = create(
  persist(
    (set) => ({
      language: detectDefaultLanguage(),
      setLanguage: (lang) => {
        i18n.changeLanguage(lang);
        set({ language: lang });
      },
    }),
    {
      name: "language-storage",
      getStorage: () => localStorage,
    }
  )
);

export default useLanguageStore;

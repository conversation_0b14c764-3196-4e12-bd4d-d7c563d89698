# Hifzy Django Application - Docker Deployment Guide

This guide will help you deploy the Hifzy Django application using Docker Compose on an Ubuntu server.

## Prerequisites

- Ubuntu Server (18.04 or later)
- Docker and Docker Compose installed
- At least 2GB RAM and 10GB disk space

## Quick Start

1. **Clone the repository** (if not already done):
   ```bash
   git clone <your-repo-url>
   cd hifzy
   ```

2. **Install Docker and Docker Compose** (if not already installed):
   ```bash
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   sudo usermod -aG docker $USER
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   
   # Logout and login again to apply group changes
   ```

3. **Configure environment variables**:
   ```bash
   cp .env.example .env
   nano .env  # Edit with your configuration
   ```

4. **Run the deployment script**:
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

## Manual Deployment Steps

If you prefer to deploy manually:

1. **Create environment file**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   DB_NAME=hifzy_db
   DB_USER=hifzyuser
   DB_PASSWORD=your_secure_password_here
   DJANGO_SECRET_KEY=your_very_secret_key_here
   ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
   ```

2. **Build and start services**:
   ```bash
   docker-compose up -d --build
   ```

3. **Run migrations**:
   ```bash
   docker-compose exec web python hifzy_backend/manage.py migrate --env=docker
   ```

4. **Collect static files**:
   ```bash
   docker-compose exec web python hifzy_backend/manage.py collectstatic --noinput --env=production
   ```

5. **Create superuser** (optional):
   ```bash
   docker-compose exec web python hifzy_backend/manage.py createsuperuser --env=docker
   ```

## Services Overview

The Docker Compose setup includes:

- **PostgreSQL Database** (`db`): Stores application data
- **Django Application** (`web`): Main backend application
- **Nginx Reverse Proxy** (`nginx`): Handles static files and proxies requests

## Accessing the Application

- **Main Application**: http://your-server-ip
- **Django Admin**: http://your-server-ip/admin/
- **API Documentation**: http://your-server-ip/swagger/

## Common Commands

### View logs:
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f web
docker-compose logs -f db
docker-compose logs -f nginx
```

### Restart services:
```bash
# All services
docker-compose restart

# Specific service
docker-compose restart web
```

### Stop services:
```bash
docker-compose down
```

### Update application:
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose down
docker-compose up -d --build

# Run migrations if needed
docker-compose exec web python hifzy_backend/manage.py migrate --env=docker
```

### Database backup:
```bash
docker-compose exec db pg_dump -U hifzyuser hifzy_db > backup.sql
```

### Database restore:
```bash
docker-compose exec -T db psql -U hifzyuser hifzy_db < backup.sql
```

## Troubleshooting

### Database connection issues:
- Check if database service is running: `docker-compose ps`
- Check database logs: `docker-compose logs db`
- Verify environment variables in `.env`

### Static files not loading:
- Run: `docker-compose exec web python hifzy_backend/manage.py collectstatic --noinput --env=production`
- Check nginx logs: `docker-compose logs nginx`

### Permission issues:
- Ensure proper file permissions: `sudo chown -R $USER:$USER .`

### Port conflicts:
- If port 80 is in use, modify `docker-compose.yml` to use different ports
- Update nginx service ports section: `"8080:80"`

## Security Considerations

1. **Change default passwords** in `.env`
2. **Use strong secret key** for Django
3. **Configure firewall** to only allow necessary ports
4. **Regular backups** of database and media files
5. **Keep Docker images updated**

## Production Optimizations

For production deployment, consider:

1. **SSL/HTTPS**: Add SSL certificates and configure nginx for HTTPS
2. **Domain configuration**: Update ALLOWED_HOSTS with your domain
3. **Resource limits**: Add resource constraints in docker-compose.yml
4. **Monitoring**: Add logging and monitoring solutions
5. **Backup strategy**: Implement automated backups

## Support

If you encounter issues:
1. Check the logs using `docker-compose logs`
2. Verify your `.env` configuration
3. Ensure all prerequisites are met
4. Check Docker and Docker Compose versions

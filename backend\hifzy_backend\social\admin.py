from django.contrib import admin
from .models import League, UserFollowMM


@admin.register(League)
class LeagueAdmin(admin.ModelAdmin):
    list_display = ["name", "rank", "start_time", "end_time"]
    list_filter = ["rank"]
    ordering = ["start_time"]


@admin.register(UserFollowMM)
class UserFollowMMAdmin(admin.ModelAdmin):
    list_display = ["follower", "following", "created_at"]
    ordering = ["-created_at"]

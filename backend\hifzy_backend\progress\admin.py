from django.contrib import admin
from .models import UserAyahMM, UserSurahMM, SurahGameCompletionMM, UserArabicMM


@admin.register(UserAyahMM)
class UserAyahMMAdmin(admin.ModelAdmin):
    list_display = ["user", "ayah", "is_completed", "is_bookmarked", "created_at"]
    list_filter = ["is_completed", "is_bookmarked", "ayah__surah"]
    ordering = ["ayah__surah_id", "ayah__ayah_number"]


@admin.register(UserSurahMM)
class UserSurahMMAdmin(admin.ModelAdmin):
    list_display = ["user", "surah", "experience_points", "is_completed", "is_bookmarked", "is_skipped"]
    list_filter = ["is_completed", "is_bookmarked", "is_skipped"]
    ordering = ["surah__surah_number"]


@admin.register(SurahGameCompletionMM)
class SurahGameCompletionMMAdmin(admin.ModelAdmin):
    list_display = ["user", "game_type", "progress_ayah_count", "created_at"]
    list_filter = ["game_type"]
    ordering = ["game_type"]


@admin.register(UserArabicMM)
class UserArabicMMAdmin(admin.ModelAdmin):
    list_display = ["user", "arabic", "progress_status", "created_at"]
    list_filter = ["progress_status"]
    ordering = ["arabic__title"]

"""
Repository layer

This module contains repository classes that abstract database operations
and convert Django models to transport objects for game-related data.
"""

from typing import List, Optional
from django.db.models import QuerySet
from django.contrib.auth import get_user_model
import json
from core.settings.base import BASE_DIR
from . import models
from . import transports


User = get_user_model()


class SurahRepository:
    """Repository for Surah operations."""

    @staticmethod
    def _make_surah_queryset() -> QuerySet[models.Surah]:
        """Create optimized queryset for surah operations."""
        return models.Surah.objects.all()

    @staticmethod
    def _make_surah_transport(surah: models.Surah) -> transports.Surah:
        """Convert Django surah model to transport object."""
        return transports.Surah(
            surah_id=surah.surah_number,
            ayah_count=surah.ayah_count,
            xp_per_ayah=surah.xp_per_ayah,
            is_pre_bismillah=surah.is_pre_bismillah,
            is_meccan=surah.is_meccan,
            is_medinian=surah.is_medinian,
            created_at=surah.created_at,
        )

    @staticmethod
    def get_surah_by_number(surah_number: int) -> Optional[transports.Surah]:
        """Get surah by number."""
        try:
            surah = SurahRepository._make_surah_queryset().get(surah_number=surah_number)
            return SurahRepository._make_surah_transport(surah)
        except models.Surah.DoesNotExist:
            return None

    @staticmethod
    def get_all_surahs() -> List[transports.Surah]:
        """Get all surahs."""
        surahs = SurahRepository._make_surah_queryset().order_by("surah_number")
        return [SurahRepository._make_surah_transport(surah) for surah in surahs]


class AyahRepository:
    """Repository for Ayah operations."""

    @staticmethod
    def _make_ayah_queryset() -> QuerySet[models.Ayah]:
        """Create optimized queryset for ayah operations."""
        return models.Ayah.objects.all()

    @staticmethod
    def _make_ayah_transport(ayah: models.Ayah) -> transports.Ayah:
        """Convert Django ayah model to transport object."""
        return transports.Ayah(
            ayah_number=ayah.ayah_number,
            surah_id=ayah.surah.surah_number,
            image=ayah.image,
        )

    @staticmethod
    def get_ayah_by_surah_and_number(surah_id: int, ayah_number: int) -> Optional[transports.Ayah]:
        """Get ayah by surah ID and ayah number."""
        try:
            surah_number = int(surah_id)
            ayah = AyahRepository._make_ayah_queryset().get(surah__surah_number=surah_number, ayah_number=ayah_number)
            return AyahRepository._make_ayah_transport(ayah)
        except (models.Ayah.DoesNotExist, ValueError):
            return None

    @staticmethod
    def get_ayahs_by_surah(surah_id: str) -> List[transports.Ayah]:
        """Get all ayahs for a surah."""
        try:
            surah_number = int(surah_id)
            ayahs = (
                AyahRepository._make_ayah_queryset().filter(surah__surah_number=surah_number).order_by("ayah_number")
            )
            return [AyahRepository._make_ayah_transport(ayah) for ayah in ayahs]
        except ValueError:
            return []


class QuranData:
    """Repository for Quran text data operations."""

    @staticmethod
    def _load_quran_data() -> dict:
        """Load Quran data from JSON file."""
        try:
            with open(f"{BASE_DIR}/quran.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError("Quran data file not found")
        except json.JSONDecodeError:
            raise ValueError("Invalid Quran data file format")

    # @staticmethod
    # def get_surah_info(surah_id: int) -> Optional[transports.SurahInfo]:
    #     """Get information about a specific Surah."""
    #     try:
    #         quran_data = QuranData._load_quran_data()
    #         # Surah ID is 1-indexed in the data
    #         if surah_id < 1 or surah_id > len(quran_data["data"]):
    #             return None

    #         surah_data = quran_data["data"][surah_id - 1]

    #         # Process revelation data
    #         revelation_data = surah_data.get("revelation", {})
    #         is_meccan = revelation_data.get("en") == "Meccan"
    #         is_medinian = revelation_data.get("en") == "Medinan"

    #         # Process preBismillah data
    #         pre_bismillah_data = surah_data.get("preBismillah")
    #         is_pre_bismillah = pre_bismillah_data is not None and isinstance(pre_bismillah_data, dict)

    #         return transports.SurahInfo(
    #             surah_id=surah_id,
    #             name_simple=surah_data["name"]["transliteration"]["en"],
    #             verses_count=surah_data["numberOfVerses"],
    #             is_pre_bismillah=is_pre_bismillah,
    #             is_meccan=is_meccan,
    #             is_medinian=is_medinian,
    #         )
    #     except (KeyError, IndexError):
    #         return None

    @staticmethod
    def get_ayah_text(surah_id: int, ayah_id: int) -> Optional[transports.AyahText]:
        """Get text for a specific Ayah."""
        try:
            quran_data = QuranData._load_quran_data()

            if surah_id < 1 or surah_id > len(quran_data["data"]):
                return None

            surah_data = quran_data["data"][surah_id - 1]

            if ayah_id < 1 or ayah_id > len(surah_data["verses"]):
                return None

            ayah_data = surah_data["verses"][ayah_id - 1]

            return transports.AyahText(
                surah_id=surah_id,
                ayah_id=ayah_id,
                arabic_text=ayah_data["text"]["arab"],
                transliteration=ayah_data["text"]["transliteration"]["en"],
                translation=ayah_data["translation"]["en"],
            )
        except (KeyError, IndexError):
            return None

    @staticmethod
    def get_ayahs_range(surah_id: int, start_ayah: int, count: int) -> List[transports.AyahText]:
        """Get a range of Ayahs from a Surah."""
        ayahs = []

        for i in range(count):
            ayah_id = start_ayah + i
            ayah = QuranData.get_ayah_text(surah_id, ayah_id)
            if ayah:
                ayahs.append(ayah)
            else:
                break  # Stop if we reach the end of the Surah

        return ayahs

    @staticmethod
    def get_surah_ayahs(surah_id: int) -> List[transports.AyahText]:
        """Get all Ayahs for a specific Surah."""
        try:
            quran_data = QuranData._load_quran_data()

            if surah_id < 1 or surah_id > len(quran_data["data"]):
                return []

            surah_data = quran_data["data"][surah_id - 1]
            ayahs = []

            for i, ayah_data in enumerate(surah_data["verses"], 1):
                ayah = transports.AyahText(
                    surah_id=surah_id,
                    ayah_id=i,
                    arabic_text=ayah_data["text"]["arab"],
                    transliteration=ayah_data["text"]["transliteration"]["en"],
                    translation=ayah_data["translation"]["en"],
                )
                ayahs.append(ayah)

            return ayahs
        except (KeyError, IndexError):
            return []


class AudioRecordingRepository:
    """Repository for AudioRecording operations."""

    @staticmethod
    def _make_audio_recording_queryset() -> QuerySet[models.AudioRecording]:
        """Create optimized queryset for audio recording operations."""
        return models.AudioRecording.objects.select_related("ayah", "ayah__surah")

    @staticmethod
    def _make_audio_recording_transport(audio_recording: models.AudioRecording) -> transports.AudioRecording:
        """Convert Django audio recording model to transport object."""
        return transports.AudioRecording(
            id=audio_recording.id,
            ayah_id=audio_recording.ayah.id,
            audio_url=audio_recording.audio_url,
            reader_name=audio_recording.reader_name,
            reader_slug=audio_recording.reader_slug,
            audio_format=audio_recording.audio_format,
            duration_seconds=audio_recording.duration_seconds,
            file_size_bytes=audio_recording.file_size_bytes,
            created_at=audio_recording.created_at,
            updated_at=audio_recording.updated_at,
        )

    @staticmethod
    def get_audio_recordings_by_ayah(ayah_id: int) -> List[transports.AudioRecording]:
        """Get all audio recordings for a specific ayah."""
        audio_recordings = AudioRecordingRepository._make_audio_recording_queryset().filter(ayah_id=ayah_id)
        return [AudioRecordingRepository._make_audio_recording_transport(recording) for recording in audio_recordings]

    @staticmethod
    def get_audio_recording_by_ayah_and_reader(ayah_id: int, reader_slug: str) -> Optional[transports.AudioRecording]:
        """Get audio recording for a specific ayah and reader."""
        try:
            audio_recording = AudioRecordingRepository._make_audio_recording_queryset().get(
                ayah_id=ayah_id, reader_slug=reader_slug
            )
            return AudioRecordingRepository._make_audio_recording_transport(audio_recording)
        except models.AudioRecording.DoesNotExist:
            return None

    @staticmethod
    def get_audio_recordings_by_reader(reader_slug: str) -> List[transports.AudioRecording]:
        """Get all audio recordings for a specific reader."""
        audio_recordings = AudioRecordingRepository._make_audio_recording_queryset().filter(reader_slug=reader_slug)
        return [AudioRecordingRepository._make_audio_recording_transport(recording) for recording in audio_recordings]

    @staticmethod
    def get_readers_for_surah(surah_id: int) -> List[transports.ReaderInfo]:
        """Get all readers who have audio recordings for a specific surah."""
        from django.db.models import Count

        # Get readers with their recording counts for the specific surah
        readers_data = (
            AudioRecordingRepository._make_audio_recording_queryset()
            .filter(ayah__surah__surah_number=surah_id)
            .values("reader_name", "reader_slug")
            .annotate(recording_count=Count("id"))
            .order_by("reader_name")
        )

        return [
            transports.ReaderInfo(
                reader_name=reader["reader_name"],
                reader_slug=reader["reader_slug"],
                recording_count=reader["recording_count"],
            )
            for reader in readers_data
        ]

    @staticmethod
    def get_all_available_readers() -> List[transports.ReaderInfo]:
        """Get all readers who have audio recordings in the system."""
        from django.db.models import Count

        # Get all readers with their total recording counts
        readers_data = (
            AudioRecordingRepository._make_audio_recording_queryset()
            .values("reader_name", "reader_slug")
            .annotate(recording_count=Count("id"))
            .order_by("reader_name")
        )

        return [
            transports.ReaderInfo(
                reader_name=reader["reader_name"],
                reader_slug=reader["reader_slug"],
                recording_count=reader["recording_count"],
            )
            for reader in readers_data
        ]


class LanguageRepository:
    """Repository for language operations."""

    @staticmethod
    def get_available_languages() -> List[transports.LanguageInfo]:
        """Get all available languages with translation and transliteration support."""
        # Get all unique locales from AyahTranslation
        locales = models.AyahTranslation.objects.values_list("locale", flat=True).distinct()

        # Language mapping for human-readable names
        language_names = {
            "en": "English",
            "ar": "Arabic",
            "id": "Indonesian",
            "ur": "Urdu",
            "tr": "Turkish",
            "fr": "French",
            "de": "German",
            "es": "Spanish",
            "ru": "Russian",
            "zh": "Chinese",
            "hi": "Hindi",
            "bn": "Bengali",
            "fa": "Persian",
            "ms": "Malay",
            "ta": "Tamil",
            "te": "Telugu",
            "th": "Thai",
            "vi": "Vietnamese",
            "ko": "Korean",
            "ja": "Japanese",
        }

        # Group locales by base language
        language_data = {}

        for locale in locales:
            if locale.endswith("_transliteration"):
                # This is a transliteration locale
                base_lang = locale.replace("_transliteration", "")
                if base_lang not in language_data:
                    language_data[base_lang] = {"has_translation": False, "has_transliteration": True}
                else:
                    language_data[base_lang]["has_transliteration"] = True
            else:
                # This is a translation locale
                if locale not in language_data:
                    language_data[locale] = {"has_translation": True, "has_transliteration": False}
                else:
                    language_data[locale]["has_translation"] = True

        # Convert to transport objects
        languages = []
        for lang_code, data in language_data.items():
            language_name = language_names.get(lang_code, lang_code.title())
            languages.append(
                transports.LanguageInfo(
                    language_code=lang_code,
                    language_name=language_name,
                    has_translation=data["has_translation"],
                    has_transliteration=data["has_transliteration"],
                )
            )

        return sorted(languages, key=lambda x: x.language_name)

    @staticmethod
    def get_ayah_translation_by_locale(ayah_id: int, locale: str) -> Optional[str]:
        """Get ayah translation text for a specific locale."""
        try:
            translation = models.AyahTranslation.objects.get(ayah_id=ayah_id, locale=locale)
            return translation.text
        except models.AyahTranslation.DoesNotExist:
            return None

    @staticmethod
    def get_ayah_transliteration_by_locale(ayah_id: int, locale: str) -> Optional[str]:
        """Get ayah transliteration text for a specific locale."""
        transliteration_locale = f"{locale}_transliteration"
        try:
            transliteration = models.AyahTranslation.objects.get(ayah_id=ayah_id, locale=transliteration_locale)
            return transliteration.text
        except models.AyahTranslation.DoesNotExist:
            return None

    @staticmethod
    def has_translation_for_locale(locale: str) -> bool:
        """Check if translation exists for a given locale."""
        return models.AyahTranslation.objects.filter(locale=locale).exists()

    @staticmethod
    def has_transliteration_for_locale(locale: str) -> bool:
        """Check if transliteration exists for a given locale."""
        transliteration_locale = f"{locale}_transliteration"
        return models.AyahTranslation.objects.filter(locale=transliteration_locale).exists()

    @staticmethod
    def get_surah_title_by_locale(surah_id: int, locale: str) -> Optional[str]:
        """Get surah title for a specific locale."""
        try:
            surah_translation = models.SurahTranslation.objects.get(surah_id=surah_id, locale=locale)
            return surah_translation.title
        except models.SurahTranslation.DoesNotExist:
            return None


class ArabicLetterRepository:
    """Repository for ArabicLetter operations."""

    @staticmethod
    def _make_arabic_letter_queryset() -> QuerySet[models.ArabicLetter]:
        """Create optimized queryset for Arabic letter operations."""
        return models.ArabicLetter.objects.all()

    @staticmethod
    def _make_arabic_letter_transport(letter: models.ArabicLetter) -> transports.ArabicLetter:
        """Convert Django Arabic letter model to transport object."""
        return transports.ArabicLetter(
            id=letter.id,
            title=letter.title,
            transcription=letter.transcription,
            audio_url=letter.audio_url,
        )

    @staticmethod
    def get_all_letters() -> List[transports.ArabicLetter]:
        """Get all Arabic letters."""
        letters = ArabicLetterRepository._make_arabic_letter_queryset().order_by("id")
        return [ArabicLetterRepository._make_arabic_letter_transport(letter) for letter in letters]

    @staticmethod
    def get_letter_by_id(letter_id: int) -> Optional[transports.ArabicLetter]:
        """Get Arabic letter by ID."""
        try:
            letter = ArabicLetterRepository._make_arabic_letter_queryset().get(id=letter_id)
            return ArabicLetterRepository._make_arabic_letter_transport(letter)
        except models.ArabicLetter.DoesNotExist:
            return None

    @staticmethod
    def get_letters_with_audio() -> List[transports.ArabicLetter]:
        """Get all Arabic letters that have audio URLs."""
        letters = (
            ArabicLetterRepository._make_arabic_letter_queryset()
            .filter(audio_url__isnull=False)
            .exclude(audio_url="")
            .order_by("id")
        )
        return [ArabicLetterRepository._make_arabic_letter_transport(letter) for letter in letters]

import React from "react";

// Inline styles for full-screen overlay and spinner
const overlayStyle = {
  position: "fixed",
  top: 0,
  left: 0,
  width: "100%",
  height: "100%",
  backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent background
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  zIndex: 1000, // Ensure it's on top of other content
};

const spinnerStyle = {
  border: "4px solid rgba(0, 0, 0, 0.1)",
  borderLeftColor: "#09f",
  width: "50px",
  height: "50px",
  borderRadius: "50%",
  animation: "spin 1s linear infinite",
};

// Keyframes for spinner animation (inline style)
const spinnerAnimation = `
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

const LoadingSpinner = () => {
  return (
    <>
      <style>{spinnerAnimation}</style>
      <div style={overlayStyle}>
        <div style={spinnerStyle}></div>
      </div>
    </>
  );
};

export default LoadingSpinner;

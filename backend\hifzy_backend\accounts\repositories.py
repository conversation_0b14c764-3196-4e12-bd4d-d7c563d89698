"""
Repository layer

This module contains repository classes that abstract database operations
and convert Django models to transport objects for game-related data.
"""

from typing import Optional
from django.db.models import QuerySet
from django.contrib.auth import get_user_model
from . import models
from . import transports
from core.transports import CompactUser

User = get_user_model()


class Users:
    """Repository for user-related operations."""

    @staticmethod
    def _make_user_queryset() -> QuerySet[models.HifzyUser]:
        """Create optimized queryset for user operations."""
        return models.HifzyUser.objects.select_related()

    @staticmethod
    def _make_compact_user_transport(user: models.HifzyUser) -> CompactUser:
        """Convert Django user model to compact transport object."""
        return CompactUser(
            id=user.id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name,
            total_xp=user.total_xp,
            current_level=user.current_level,
            day_streak=user.day_streak,
            gender=user.gender,
        )

    @staticmethod
    def _make_user_profile_transport(user: models.HifzyUser) -> transports.UserProfile:
        """Convert Django user model to full profile transport object."""
        # Handle league relationship
        league_transport = None
        if user.league_pk:
            league_transport = transports.League(
                id=user.league_pk.id,
                rank=user.league_pk.rank,
                end_time=user.league_pk.end_time,
                start_time=user.league_pk.start_time,
                icon=user.league_pk.icon,
                name=user.league_pk.name,
            )

        return transports.UserProfile(
            id=user.id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name,
            email=user.email,
            profile_photo=user.profile_photo,
            date_joined=user.date_joined,
            total_xp=user.total_xp,
            current_level=user.current_level,
            day_streak=user.day_streak,
            gender=user.gender,
            league=league_transport,
        )

    @staticmethod
    def get_user_by_id(user_id: int) -> Optional[transports.UserProfile]:
        """Get user profile by ID."""
        try:
            user = Users._make_user_queryset().get(id=user_id)
            return Users._make_user_profile_transport(user)
        except models.HifzyUser.DoesNotExist:
            return None

    @staticmethod
    def get_user_by_username(username: str) -> Optional[transports.UserProfile]:
        """Get user profile by username."""
        try:
            user = Users._make_user_queryset().get(username=username)
            return Users._make_user_profile_transport(user)
        except models.HifzyUser.DoesNotExist:
            return None

    @staticmethod
    def get_compact_user_by_id(user_id: int) -> Optional[CompactUser]:
        """Get compact user representation by ID."""
        try:
            user = Users._make_user_queryset().get(id=user_id)
            return Users._make_compact_user_transport(user)
        except models.HifzyUser.DoesNotExist:
            return None

.stat-blocks-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  height: 100%;
  row-gap: 20px;
}

.stat-block-img {
  width: 50px;
  margin-left: 20px;
}

.horizontal-stat-block-container {
  background-color: #fbfbfb;
  filter: drop-shadow(6px 6px 15px rgba(134, 134, 134, 0.2));;
  border-radius: 30px;
  padding: 15px 0px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: row;
  column-gap: 20px;
  width: 100%;
}

.stat-block-info {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.stat-block-value {
  font-size: 20px;
  font-weight: 600;
}

.stat-block-name {
  font-size: 16px;
  font-weight: 200;
}

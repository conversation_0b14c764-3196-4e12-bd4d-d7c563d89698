"""
Repository layer

This module contains repository classes that abstract database operations
and convert Django models to transport objects for game-related data.
"""

from typing import List, Optional
from django.db.models import QuerySet
from django.contrib.auth import get_user_model
from quran.repositories import SurahRepository, AyahRepository, ArabicLetterRepository

from . import models
from . import transports


User = get_user_model()


class UserSurahRepository:
    """Repository for UserSurahMM operations."""

    @staticmethod
    def _make_user_surah_queryset() -> QuerySet[models.UserSurahMM]:
        """Create optimized queryset for user-surah operations."""
        return models.UserSurahMM.objects.select_related("user", "surah")

    @staticmethod
    def _make_user_surah_transport(user_surah: models.UserSurahMM) -> transports.UserSurahRelation:
        """Convert Django user-surah model to transport object."""
        surah_transport = SurahRepository._make_surah_transport(user_surah.surah)
        return transports.UserSurahRelation(
            user_id=user_surah.user.id,
            surah=surah_transport,
            experience_points=user_surah.experience_points,
            is_completed=user_surah.is_completed,
            is_bookmarked=user_surah.is_bookmarked,
            is_skipped=user_surah.is_skipped,
            created_at=user_surah.created_at,
        )

    @staticmethod
    def get_user_surah_relation(user_id: int, surah_number: int) -> Optional[transports.UserSurahRelation]:
        """Get user-surah relationship."""
        try:
            user_surah = UserSurahRepository._make_user_surah_queryset().get(
                user_id=user_id, surah__surah_number=surah_number
            )
            return UserSurahRepository._make_user_surah_transport(user_surah)
        except models.UserSurahMM.DoesNotExist:
            return None

    @staticmethod
    def get_user_surah_relations(user_id: int) -> List[transports.UserSurahRelation]:
        """Get all user-surah relationships for a user."""
        user_surahs = (
            UserSurahRepository._make_user_surah_queryset().filter(user_id=user_id).order_by("surah__surah_number")
        )
        return [UserSurahRepository._make_user_surah_transport(us) for us in user_surahs]


class UserAyahRepository:
    """Repository for UserAyahMM operations."""

    @staticmethod
    def _make_user_ayah_queryset() -> QuerySet[models.UserAyahMM]:
        """Create optimized queryset for user-ayah operations."""
        return models.UserAyahMM.objects.select_related("user", "ayah")

    @staticmethod
    def _make_user_ayah_transport(user_ayah: models.UserAyahMM) -> transports.UserAyahRelation:
        """Convert Django user-ayah model to transport object."""
        ayah_transport = AyahRepository._make_ayah_transport(user_ayah.ayah)
        return transports.UserAyahRelation(
            ayah=ayah_transport,
            ayah_id=user_ayah.ayah.id,
            user_id=user_ayah.user.id,
            is_completed=user_ayah.is_completed,
            is_bookmarked=user_ayah.is_bookmarked,
            created_at=user_ayah.created_at,
        )

    @staticmethod
    def get_user_ayah_relation(user_id: int, ayah_id: int) -> Optional[transports.UserAyahRelation]:
        """Get user-ayah relationship."""
        try:
            user_ayah = UserAyahRepository._make_user_ayah_queryset().get(user_id=user_id, ayah_id=ayah_id)
            return UserAyahRepository._make_user_ayah_transport(user_ayah)
        except models.UserAyahMM.DoesNotExist:
            return None

    @staticmethod
    def get_user_bookmarks(user_id: int) -> List[transports.UserAyahRelation]:
        """Get all bookmarked ayahs for a user."""
        user_ayahs = (
            UserAyahRepository._make_user_ayah_queryset()
            .filter(user_id=user_id, is_bookmarked=True)
            .order_by("ayah__surah_id", "ayah__ayah_number")
        )
        return [UserAyahRepository._make_user_ayah_transport(ua) for ua in user_ayahs]

    @staticmethod
    def get_user_completed_ayahs(user_id: int) -> List[transports.UserAyahRelation]:
        """Get all completed ayahs for a user."""
        user_ayahs = (
            UserAyahRepository._make_user_ayah_queryset()
            .filter(user_id=user_id, is_completed=True)
            .order_by("ayah__surah_id", "ayah__ayah_number")
        )
        return [UserAyahRepository._make_user_ayah_transport(ua) for ua in user_ayahs]


class SurahGameCompletionRepository:
    """Repository for SurahGameCompletionMM operations."""

    @staticmethod
    def _make_game_completion_queryset() -> QuerySet[models.SurahGameCompletionMM]:
        """Create optimized queryset for game completion operations."""
        return models.SurahGameCompletionMM.objects.select_related("user", "surah")

    @staticmethod
    def _make_game_completion_transport(completion: models.SurahGameCompletionMM) -> transports.SurahGameCompletion:
        """Convert Django game completion model to transport object."""
        return transports.SurahGameCompletion(
            id=completion.id,
            user_id=completion.user.id,
            game_type=completion.game_type,
            progress_ayah_count=completion.progress_ayah_count,
            new_ayahs=completion.new_ayahs,
            created_at=completion.created_at,
        )

    @staticmethod
    def get_user_game_completions(user_id: int) -> List[transports.SurahGameCompletion]:
        """Get all game completions for a user."""
        completions = (
            SurahGameCompletionRepository._make_game_completion_queryset()
            .filter(user_id=user_id)
            .order_by("surah__surah_number", "game_type")
        )
        return [SurahGameCompletionRepository._make_game_completion_transport(gc) for gc in completions]

    @staticmethod
    def get_user_surah_game_completions(user_id: int, surah_number: int) -> List[transports.SurahGameCompletion]:
        """Get game completions for a specific surah."""
        completions = (
            SurahGameCompletionRepository._make_game_completion_queryset()
            .filter(user_id=user_id, surah__surah_number=surah_number)
            .order_by("game_type")
        )
        return [SurahGameCompletionRepository._make_game_completion_transport(gc) for gc in completions]


class UserArabicProgressRepository:
    """Repository for UserArabicMM operations."""

    @staticmethod
    def _make_user_arabic_queryset() -> QuerySet[models.UserArabicMM]:
        """Create optimized queryset for user-Arabic letter operations."""
        return models.UserArabicMM.objects.select_related("user", "arabic")

    @staticmethod
    def _make_user_arabic_transport(user_arabic: models.UserArabicMM) -> transports.UserArabicProgress:
        """Convert Django user-Arabic model to transport object."""
        arabic_transport = ArabicLetterRepository._make_arabic_letter_transport(user_arabic.arabic)
        return transports.UserArabicProgress(
            id=user_arabic.id,
            progress_status=user_arabic.progress_status,
            user_id=user_arabic.user.id,
            arabic=arabic_transport,
            created_at=user_arabic.created_at,
        )

    @staticmethod
    def get_user_arabic_progress(user_id: int, letter_id: int) -> Optional[transports.UserArabicProgress]:
        """Get user progress for a specific Arabic letter."""
        try:
            user_arabic = UserArabicProgressRepository._make_user_arabic_queryset().get(
                user_id=user_id, arabic_id=letter_id
            )
            return UserArabicProgressRepository._make_user_arabic_transport(user_arabic)
        except models.UserArabicMM.DoesNotExist:
            return None

    @staticmethod
    def get_all_user_arabic_progress(user_id: int) -> List[transports.UserArabicProgress]:
        """Get all Arabic letter progress for a user."""
        user_arabic_list = (
            UserArabicProgressRepository._make_user_arabic_queryset().filter(user_id=user_id).order_by("arabic__id")
        )
        return [UserArabicProgressRepository._make_user_arabic_transport(ua) for ua in user_arabic_list]

    @staticmethod
    def create_or_update_progress(user_id: int, letter_id: int, progress_status: str) -> transports.UserArabicProgress:
        """Create or update user progress for an Arabic letter."""
        user_arabic, created = models.UserArabicMM.objects.update_or_create(
            user_id=user_id, arabic_id=letter_id, defaults={"progress_status": progress_status}
        )
        return UserArabicProgressRepository._make_user_arabic_transport(user_arabic)

    @staticmethod
    def get_progress_stats(user_id: int) -> dict:
        """Get progress statistics for a user."""
        from django.db.models import Count, Q

        stats = models.UserArabicMM.objects.filter(user_id=user_id).aggregate(
            total_letters=Count("id"),
            new_letters=Count("id", filter=Q(progress_status="New")),
            in_progress_letters=Count("id", filter=Q(progress_status="In progress")),
            completed_letters=Count("id", filter=Q(progress_status="Completed")),
        )

        total_available_letters = 28  # Total Arabic letters
        total_tracked = stats["total_letters"] or 0
        completed = stats["completed_letters"] or 0

        completion_percentage = (completed / total_available_letters * 100) if total_available_letters > 0 else 0

        return {
            "total_available_letters": total_available_letters,
            "total_tracked_letters": total_tracked,
            "new_letters": stats["new_letters"] or 0,
            "in_progress_letters": stats["in_progress_letters"] or 0,
            "completed_letters": completed,
            "completion_percentage": round(completion_percentage, 2),
        }

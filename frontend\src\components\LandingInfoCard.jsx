import { useState } from "react";
function LandingInfoCard(props) {
  const [isHover, setIsHover] = useState(false);

  const handleMouseEnter = () => {
    setIsHover(true);
  };

  const handleMouseLeave = () => {
    setIsHover(false);
  };
  return (
    <>
      <div
        style={{
          backgroundColor: props.backgroundColor,
          width: "250px",
          height: "175px",
          borderRadius: "30px",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
          padding: "20px",
          transform: isHover ? "scale(1.05)" : "scale(1)",
          transitionDuration: "0.2s",
          cursor: "pointer",
        }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
          }}
        >
          <img src={props.image} style={{ width: "90px", height: "auto" }} />
          <img src={props.arrow} style={{ width: "15px", height: "auto" }} />
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "flex-end",
            color: props.textColor,
          }}
        >
          <a style={{ fontSize: "20px", fontWeight: "600" }}>{props.title}</a>
          <p
            style={{
              fontSize: "15px",
              margin: "0px",
              lineHeight: "20px",
            }}
          >
            {props.descrtiption}
          </p>
        </div>
      </div>
    </>
  );
}

export default LandingInfoCard;

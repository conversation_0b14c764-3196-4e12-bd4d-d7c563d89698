import { useState, React, useEffect, useRef } from "react";
import playSound from "src/assets/play-audio.svg";
import pauseSound from "src/assets/pause-icon.svg";
import dynamicIcon from "src/assets/sound-icon.svg";
import mutedIcon from "src/assets/muted-icon.svg";
import leftArrowLearnSvg from "src/assets/left-arrow-learn.svg";
import rightArrowDownSvg from "src/assets/right-arrow-learn.svg";
import settingsSvg from "src/assets/settings.svg";
import bookmarkSvg from "src/assets/bookmark.svg";
import "src/components/LearnCard/LearnCard.css";
import StepByStep from "src/components/StepByStep/StepByStep";
import axios from "axios";
import LoadingSpinner from "src/components/LoadingSpinner";
import axiosInstance from "src/utils/axios";
import { BACKEND_URL } from "src/utils/settings";
import ReactCardFlip from "react-card-flip";
import { useTranslation } from "react-i18next";
import useLanguageStore from "src/hooks/languageStore";

function LearnCard({ nextAyah, prevAyah, id, ayahId }) {
  const [loading, setLoading] = useState(true);
  const [sbsEnabled, setSbsEnabled] = useState(false);
  const [ayahText, setAyahText] = useState("");
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioProgress, setAudioProgress] = useState(0);
  const [audioTime, setAudioTime] = useState({ currentTime: 0, duration: 0 });
  const [volume, setVolume] = useState(1); // 1 = full volume
  const [isMuted, setIsMuted] = useState(false);
  const [audioUrl, setAudioUrl] = useState("");
  const [switchState, setSwitchState] = useState("full"); // New state for switch
  const [isFlipped, setIsFlipped] = useState(false);
  const [cardData, setCardData] = useState(null);

  const audioRef = useRef(null);

  const { t } = useTranslation();

  const language = useLanguageStore((state) => state.language);

  useEffect(() => {
    setLoading(true);
    // Запрос для получения текста и аудио
    axiosInstance
      .post("/games/ayah_text/", { surah_id: id, ayah_id: ayahId })
      .then((response) => {
        const text = response.data.text;
        const globalAyahNumber = response.data.globalNumber;
        setAyahText(text);
        setAudioUrl(
          `https://cdn.islamic.network/quran/audio/128/ar.alafasy-2/${globalAyahNumber}.mp3`
        );
      })
      .catch((error) => {
        console.error(error);
      });

    // Запрос для получения URL изображения mainCard
    axiosInstance
      .post("/games/ayah_image/", { surah_id: id, ayah_id: ayahId })
      .then((response) => {
        setCardData(response.data);
        setLoading(false);
      })
      .catch((error) => {
        console.error(error);
        setLoading(false);
      });
  }, [ayahId, id]);

  const handleCardFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleAudioPlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        if (audioRef.current.readyState >= 3) {
          audioRef.current.play();
        } else {
          audioRef.current.addEventListener("canplay", () => {
            audioRef.current.play();
          });
        }
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      const currentTime = audioRef.current.currentTime;
      const duration = audioRef.current.duration;
      setAudioTime({ currentTime, duration });
      setAudioProgress((currentTime / duration) * 100);
    }
  };

  const handleAudioProgressClick = (event) => {
    const progressBar = event.target;
    const clickX = event.nativeEvent.offsetX;
    const progressBarWidth = progressBar.clientWidth;
    const newTime = (clickX / progressBarWidth) * audioRef.current.duration;
    audioRef.current.currentTime = newTime;
  };

  const handleVolumeClick = (event) => {
    const volumeBar = event.target;
    const clickX = event.nativeEvent.offsetX;
    const volumeBarWidth = volumeBar.clientWidth;
    const newVolume = clickX / volumeBarWidth;
    setVolume(newVolume);
    audioRef.current.volume = newVolume;
  };

  const handleMuteToggle = () => {
    if (audioRef.current) {
      audioRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60)
      .toString()
      .padStart(2, "0");
    return `${minutes}:${seconds}`;
  };

  const toggleSwitch = () => {
    setSwitchState(switchState === "full" ? "flash" : "full");
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  let activeComponent = sbsEnabled ? (
    <StepByStep ayahText={ayahText} />
  ) : (
    <>
      <img
        src={leftArrowLearnSvg}
        alt="Left Arrow"
        className="left-arrow-learn"
        onClick={prevAyah}
      />
      {switchState === "full" ? (
        <div className={`learn-card-img ${switchState}`}>
          <img
            src={BACKEND_URL + cardData.image}
            className={`card-img ${switchState}`}
            alt=""
          />
          <div className={`learn-card-info ${switchState}`}>
            <a className="learn-card-text">{cardData.text}</a>
            <a className="learn-card-transliteration">
              {language === "en"
                ? cardData.transliteration_text
                : cardData.transliteration_text_ru}
            </a>
            <a className="learn-card-english">
              {" "}
              {language === "en"
                ? cardData.english_text
                : cardData.russian_text}
            </a>
          </div>
        </div>
      ) : (
        <>
          <ReactCardFlip isFlipped={isFlipped} flipDirection="horizontal">
            <div
              className={`learn-card-img ${switchState}`}
              onClick={handleCardFlip}
            >
              <img
                src={BACKEND_URL + cardData.image}
                className={`card-img ${switchState}`}
                alt=""
              />
            </div>

            <div
              className={`learn-card-img flipped ${switchState}`}
              onClick={handleCardFlip}
            >
              <div className={`learn-card-info ${switchState}`}>
                <a className="learn-card-text">{cardData.text}</a>
                <a className="learn-card-transliteration">
                  {language === "en"
                    ? cardData.transliteration_text
                    : cardData.transliteration_text_ru}
                </a>
                <a className="learn-card-english">
                  {" "}
                  {language === "en"
                    ? cardData.english_text
                    : cardData.russian_text}
                </a>
              </div>
            </div>
          </ReactCardFlip>
        </>
      )}
      <div className="right-learn-card-controls">
        <img src={settingsSvg} alt="Settings" className="settings-button" />
        <img src={bookmarkSvg} alt="Bookmark" className="bookmarks" />
        <img
          src={rightArrowDownSvg}
          alt="Right Arrow"
          className="right-arrow-learn"
          onClick={nextAyah}
        />
      </div>
    </>
  );

  return (
    <>
      {!sbsEnabled && (
        <div className="custom-switch-container">
          <div
            className={`custom-switch ${
              switchState === "full" ? "full-active" : "flash-active"
            }`}
            onClick={toggleSwitch}
          >
            <div className="switch-slider"></div>
            <span
              className={`switch-option ${
                switchState === "full" ? "active-switch" : ""
              }`}
            >
              {t("full_card")}
            </span>
            <span
              className={`switch-option ${
                switchState === "flash" ? "active-switch" : ""
              }`}
            >
              {t("flash_card")}
            </span>
          </div>
        </div>
      )}

      <div className="learn-card">{activeComponent}</div>

      {!sbsEnabled && (
        <>
          <div className="play-audio-info">
            <div className="left-audio-info">
              <img
                src={isPlaying ? pauseSound : playSound}
                alt={isPlaying ? "Pause Sound" : "Play Sound"}
                className="play-sound"
                onClick={handleAudioPlayPause}
              />
              <img
                src={isMuted ? mutedIcon : dynamicIcon}
                alt="Dynamic Icon"
                className="dynamic-icon"
                onClick={handleMuteToggle}
              />
              <div className="main-audio-pg-bar" onClick={handleVolumeClick}>
                <div
                  className="done-audio-pg-bar"
                  style={{ width: `${volume * 100}%` }}
                ></div>
              </div>
            </div>
            <div className="right-audio-info">
              <a className="audio-minutes-counter">
                {formatTime(audioTime.currentTime)} /{" "}
                {formatTime(audioTime.duration)}
              </a>
            </div>
          </div>
          <div className="audio-line" onClick={handleAudioProgressClick}>
            <div
              className="done-audio-line"
              style={{ width: `${audioProgress}%` }}
            ></div>
          </div>
        </>
      )}
      <div className="learn-card-buttons">
        <button
          className="learn button"
          onClick={() => setSbsEnabled(!sbsEnabled)}
        >
          {sbsEnabled ? t("stop") : t("step_by_step_btn")}
        </button>
        <a href="#games" className="practise button">
          {t("practise_with_games_btn")}
        </a>
      </div>
      <audio
        ref={audioRef}
        src={audioUrl}
        onTimeUpdate={handleTimeUpdate}
        onEnded={() => setIsPlaying(false)}
        volume={volume}
      />
    </>
  );
}

export default LearnCard;

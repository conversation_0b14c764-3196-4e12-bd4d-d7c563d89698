"""
Response schema serializers for the accounts app.

This module provides response serializers for all accounts-related endpoints
to ensure comprehensive API documentation in Swagger/OpenAPI.
"""

from rest_framework import serializers
from core.response_schemas import (
    BaseSuccessResponseSerializer,
    BaseErrorResponseSerializer,
    UserProfileSerializer,
    AuthTokenResponseSerializer,
    BookmarkSerializer,
    EnhancedBookmarkSerializer,
    LanguageMetadataSerializer,
    EnhancedUserProfileSerializer,
)


# Authentication Response Schemas
class GoogleLoginSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful Google OAuth login."""

    data = AuthTokenResponseSerializer(help_text="Authentication data")


class DevLoginSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful development login."""

    class DevLoginDataSerializer(serializers.Serializer):
        token = serializers.CharField(help_text="Authentication token")
        user = UserProfileSerializer(help_text="User profile information")
        note = serializers.Char<PERSON>ield(help_text="Usage instructions for the token")

    data = DevLoginDataSerializer(help_text="Development login data")


class UserRegistrationSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful user registration."""

    data = AuthTokenResponseSerializer(help_text="Registration data with auth token")


class UserLoginSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful user login."""

    data = AuthTokenResponseSerializer(help_text="Login data with auth token")


class PasswordChangeSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful password change."""

    # No additional data field needed, just success message
    pass


# User Profile Response Schemas
class UserProfileGetSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for getting user profile."""

    data = UserProfileSerializer(help_text="User profile information")


class UserProfileUpdateSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for updating user profile."""

    data = UserProfileSerializer(help_text="Updated user profile information")


class UserProfileCompleteGetSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for getting enhanced user profile with social, league, and bookmark information."""

    data = EnhancedUserProfileSerializer(help_text="Enhanced user profile information")


class UserProfileCompleteUpdateSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for updating complete user profile."""

    data = UserProfileSerializer(help_text="Updated complete user profile information")


# Bookmark Response Schemas
class BookmarkAddSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for adding a bookmark."""

    data = BookmarkSerializer(help_text="Added bookmark information")


class BookmarkRemoveSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for removing a bookmark."""

    # No additional data field needed, just success message
    pass


class BookmarkListSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for listing bookmarks with enhanced multi-language content."""

    class BookmarkListDataSerializer(serializers.Serializer):
        bookmarks = EnhancedBookmarkSerializer(
            many=True, help_text="List of user bookmarks with multi-language content"
        )
        count = serializers.IntegerField(help_text="Number of bookmarks")
        language_info = LanguageMetadataSerializer(help_text="Language selection metadata")

    data = BookmarkListDataSerializer(help_text="Enhanced bookmark list data")


# Error Response Schemas
class AuthenticationErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for authentication errors."""

    pass


class ValidationErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for validation errors."""

    pass


class NotFoundErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for not found errors."""

    pass


class ConflictErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for conflict errors (e.g., username already exists)."""

    pass


class ForbiddenErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for forbidden errors."""

    pass


class InternalServerErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for internal server errors."""

    pass


# Specific endpoint response schemas with examples
class GoogleLoginResponseSchemas:
    """Response schemas for Google login endpoint."""

    success_200 = GoogleLoginSuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer


class DevLoginResponseSchemas:
    """Response schemas for development login endpoint."""

    success_200 = DevLoginSuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_403 = ForbiddenErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer

# TODO clean up with unused old register view
# class UserRegistrationResponseSchemas:
#     """Response schemas for user registration endpoint."""

#     success_201 = UserRegistrationSuccessResponseSerializer
#     error_400 = ValidationErrorResponseSerializer
#     error_500 = InternalServerErrorResponseSerializer


class VerifiedUserRegistrationResponseSchemas:
    """Response schemas for verified user registration endpoint."""

    success_201 = UserRegistrationSuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer


class UserLoginResponseSchemas:
    """Response schemas for user login endpoint."""

    success_200 = UserLoginSuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer


class PasswordChangeResponseSchemas:
    """Response schemas for password change endpoint."""

    success_200 = PasswordChangeSuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_401 = AuthenticationErrorResponseSerializer
    error_404 = NotFoundErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer



class UserProfileCompleteResponseSchemas:
    """Response schemas for complete user profile endpoints."""

    get_success_200 = UserProfileCompleteGetSuccessResponseSerializer
    update_success_200 = UserProfileCompleteUpdateSuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_401 = AuthenticationErrorResponseSerializer
    error_404 = NotFoundErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer


class BookmarkResponseSchemas:
    """Response schemas for bookmark endpoints."""

    add_success_201 = BookmarkAddSuccessResponseSerializer
    add_success_200 = BookmarkAddSuccessResponseSerializer  # For existing bookmarks
    remove_success_200 = BookmarkRemoveSuccessResponseSerializer
    list_success_200 = BookmarkListSuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_401 = AuthenticationErrorResponseSerializer
    error_404 = NotFoundErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer


class EmailAvailabilitySuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful email availability check."""

    class EmailAvailabilityDataSerializer(serializers.Serializer):
        email = serializers.EmailField(help_text="Email address that was checked")
        is_available = serializers.BooleanField(help_text="Whether the email is available for registration")

    data = EmailAvailabilityDataSerializer(help_text="Email availability data")


class EmailVerificationRequestSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful email verification request."""

    class EmailVerificationRequestDataSerializer(serializers.Serializer):
        email = serializers.EmailField(help_text="Email address for which verification was requested")
        verification_required = serializers.BooleanField(help_text="Whether verification is required")
        message = serializers.CharField(help_text="Message about the verification request")

    data = EmailVerificationRequestDataSerializer(help_text="Email verification request data")


class EmailVerificationCodeSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful email verification code validation."""

    class EmailVerificationCodeDataSerializer(serializers.Serializer):
        email = serializers.EmailField(help_text="Email address that was verified")
        is_verified = serializers.BooleanField(help_text="Whether the email was successfully verified")
        message = serializers.CharField(help_text="Message about the verification result")

    data = EmailVerificationCodeDataSerializer(help_text="Email verification result data")


class PasswordResetRequestSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful password reset request."""

    class PasswordResetRequestDataSerializer(serializers.Serializer):
        email = serializers.EmailField(help_text="Email address for which password reset was requested")
        verification_required = serializers.BooleanField(help_text="Whether verification is required")
        message = serializers.CharField(help_text="Message about the password reset request")

    data = PasswordResetRequestDataSerializer(help_text="Password reset request data")


class PasswordResetCompleteSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful password reset completion."""

    class PasswordResetCompleteDataSerializer(serializers.Serializer):
        email = serializers.EmailField(help_text="Email address for which password was reset")
        is_reset = serializers.BooleanField(help_text="Whether the password was successfully reset")
        message = serializers.CharField(help_text="Message about the password reset result")

    data = PasswordResetCompleteDataSerializer(help_text="Password reset completion data")


class EmailAvailabilityResponseSchemas:
    """Response schemas for email availability check endpoint."""

    success_200 = EmailAvailabilitySuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer


class EmailVerificationResponseSchemas:
    """Response schemas for email verification endpoints."""

    request_success_200 = EmailVerificationRequestSuccessResponseSerializer
    verify_success_200 = EmailVerificationCodeSuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer


class AyahReactionSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful ayah reaction operations."""

    class AyahReactionDataSerializer(serializers.Serializer):
        surah_id = serializers.IntegerField(help_text="Surah ID")
        ayah_number = serializers.IntegerField(help_text="Ayah number within the surah")
        user_reaction = serializers.CharField(
            allow_null=True, help_text="User's current reaction ('like', 'dislike', or null)"
        )
        like_count = serializers.IntegerField(help_text="Total number of likes for this ayah")
        dislike_count = serializers.IntegerField(help_text="Total number of dislikes for this ayah")
        message = serializers.CharField(help_text="Operation result message")

    data = AyahReactionDataSerializer(help_text="Ayah reaction data")


class AyahReportSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful ayah report submission."""

    class AyahReportDataSerializer(serializers.Serializer):
        surah_id = serializers.IntegerField(help_text="Surah ID")
        ayah_number = serializers.IntegerField(help_text="Ayah number within the surah")
        complaint_type = serializers.CharField(help_text="Type of complaint submitted")
        is_reported = serializers.BooleanField(help_text="Whether the report was successfully submitted")
        message = serializers.CharField(help_text="Report submission result message")

    data = AyahReportDataSerializer(help_text="Ayah report data")


class PasswordResetResponseSchemas:
    """Response schemas for password reset endpoints."""

    request_success_200 = PasswordResetRequestSuccessResponseSerializer
    verify_success_200 = EmailVerificationCodeSuccessResponseSerializer
    complete_success_200 = PasswordResetCompleteSuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer


class AyahInteractionResponseSchemas:
    """Response schemas for ayah interaction endpoints."""

    reaction_success_200 = AyahReactionSuccessResponseSerializer
    report_success_200 = AyahReportSuccessResponseSerializer
    error_400 = ValidationErrorResponseSerializer
    error_401 = AuthenticationErrorResponseSerializer
    error_500 = InternalServerErrorResponseSerializer

import React, { useState } from "react";
import "src/components/AyahDropDown/AyahDropDown.css";
import arrowDownSvg from "src/assets/arrow_down.svg";

const AyahDropDown = ({ maxAyahs, onAyahChange }) => {
  const [selectedAyah, setSelectedAyah] = useState(1); // Default ayah
  const [isOpen, setIsOpen] = useState(false);

  // Array of ayah numbers (1 to maxAyahs)
  const ayahOptions = Array.from({ length: maxAyahs }, (_, i) => i + 1);

  // Handle click to toggle dropdown visibility
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    if (isOpen === false) {
      document.querySelector(".dropdown-button").style.borderBottomLeftRadius =
        "0px";
      document.querySelector(".dropdown-button").style.borderBottomRightRadius =
        "0px";
    } else {
      setTimeout(function () {
        if (isOpen) {
          document.querySelector(
            ".dropdown-button"
          ).style.borderBottomLeftRadius = "15px";
          document.querySelector(
            ".dropdown-button"
          ).style.borderBottomRightRadius = "15px";
        }
      }, 300);
    }
  };

  // Handle selection of an ayah
  const selectAyah = (ayah) => {
    setSelectedAyah(ayah);
    onAyahChange(ayah);
    setIsOpen(false);
    setTimeout(function () {
      if (isOpen) {
        document.querySelector(
          ".dropdown-button"
        ).style.borderBottomLeftRadius = "15px";
        document.querySelector(
          ".dropdown-button"
        ).style.borderBottomRightRadius = "15px";
      }
    }, 300);
  };

  return (
    <div className={`dropdown ${isOpen ? "open" : ""}`}>
      <button className="dropdown-button" onClick={toggleDropdown}>
        Number of ayahs: {selectedAyah}
        <img src={arrowDownSvg} alt="Arrow Down" />
      </button>

      {/* Conditionally render the dropdown menu */}
      <ul className="dropdown-menu">
        {ayahOptions.map((ayah) => (
          <li
            key={ayah}
            onClick={() => selectAyah(ayah)}
            className="dropdown-item"
          >
            {ayah}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default AyahDropDown;

"""
Service layer.

This module contains business logic for game-related operations.
Services handle game generation, validation, and coordination.
"""

from typing import List, Tuple
from django.contrib.auth import get_user_model
from django.db import transaction
from rest_framework.exceptions import ValidationError, NotFound
import logging

from . import models
from . import transports
from . import repositories
from core.transports import Compact<PERSON>ser
from accounts.repositories import Users

logger = logging.getLogger(__name__)
User = get_user_model()


class FollowService:
    """Service for user follow operations using UserFollowMM model."""

    @staticmethod
    @transaction.atomic
    def follow_user(follower_id: int, following_id: int) -> transports.UserFollowRelation:
        """Create a follow relationship between users."""
        if follower_id == following_id:
            raise ValidationError("Users cannot follow themselves")

        try:
            follower = User.objects.get(id=follower_id)
            following = User.objects.get(id=following_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        follow, created = models.UserFollowMM.objects.get_or_create(follower=follower, following=following)

        if not created:
            raise ValidationError("Already following this user")

        return repositories.UserFollowRepository._make_user_follow_transport(follow)

    @staticmethod
    @transaction.atomic
    def unfollow_user(follower_id: int, following_id: int) -> bool:
        """Remove a follow relationship between users."""
        try:
            follow = models.UserFollowMM.objects.get(follower_id=follower_id, following_id=following_id)
            follow.delete()
            return True
        except models.UserFollowMM.DoesNotExist:
            return False

    @staticmethod
    def get_user_followers(user_id: int) -> List[transports.UserFollowRelation]:
        """Get all followers for a user."""
        return repositories.UserFollowRepository.get_user_followers(user_id)

    @staticmethod
    def get_user_following(user_id: int) -> List[transports.UserFollowRelation]:
        """Get all users that a user is following."""
        return repositories.UserFollowRepository.get_user_following(user_id)

    @staticmethod
    def is_following(follower_id: int, following_id: int) -> bool:
        """Check if a user is following another user."""
        return repositories.UserFollowRepository.is_following(follower_id, following_id)

    @staticmethod
    def get_social_summary(user_id: int, limit: int = 10) -> Tuple[int, int, List[CompactUser], List[CompactUser]]:
        """Get social summary for a user including counts and limited lists."""
        # Get followers and following relationships
        followers_relations = repositories.UserFollowRepository.get_user_followers(user_id)
        following_relations = repositories.UserFollowRepository.get_user_following(user_id)

        # Extract counts
        followers_count = len(followers_relations)
        following_count = len(following_relations)

        # Extract compact user objects (limited)
        followers = [relation.follower for relation in followers_relations[:limit]]
        following = [relation.following for relation in following_relations[:limit]]

        return followers_count, following_count, followers, following

    @staticmethod
    def get_followers_count(user_id: int) -> int:
        """Get the count of followers for a user."""
        return models.UserFollowMM.objects.filter(following_id=user_id).count()

    @staticmethod
    def get_following_count(user_id: int) -> int:
        """Get the count of users that a user is following."""
        return models.UserFollowMM.objects.filter(follower_id=user_id).count()


class LeaderboardService:
    """Service for leaderboard operations."""

    @staticmethod
    def get_global_leaderboard(limit: int = 10) -> List[transports.LeaderboardEntry]:
        """Get global leaderboard."""
        return list(repositories.Leaderboards.get_global_leaderboard(limit))

    @staticmethod
    def get_followed_users_leaderboard(user_id: int, limit: int = 10) -> List[transports.LeaderboardEntry]:
        """Get leaderboard for users that the given user follows."""
        return list(repositories.Leaderboards.get_followed_users_leaderboard(user_id, limit))

*,
::after,
::before {
  box-sizing: border-box;
  font-family: "Montserrat";
}

html {
  scroll-behavior: smooth;
}

button {
  all: unset;
}

button:focus {
  outline: revert;
}

a {
  text-decoration: none;
  color: inherit;
}

.grid-container {
  display: grid;
  /* grid-template-columns: 0.7fr 4.2fr; */
  grid-template-rows: 11vh 89vh 89vh;
  /* margin-bottom: 20px; */

  grid-template-areas:
    "one top-nav"
    "one learn"
    "one game";
}
.game-mode {
  /* grid-template-rows: 0.16fr 700px; */
  grid-template-rows: 30vh 70vh;
  grid-template-columns: 215px 1fr;
  grid-template-areas:
    "one top-nav"
    "one learn"; /* Remove the 'game' grid area */
}

.left-menu-games {
  grid-area: one;
  position: sticky;
  top: 0%;
  height: 100vh;
}

.unmovable-header {
  position: sticky;
  top: 0%;
  grid-area: top-nav;
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 99;
}

.title-pg {
  font-size: 24px;
  font-weight: 600;
  color: #828282;
}

.right-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 0 20px;
  grid-area: learn;
  height: 100%;
  gap: 0.9vh;
  /* width: 100%; */
}

.arrow-down-svg {
  width: 25px;
  height: auto;
}

.top-main-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}

.main-pg-bar {
  /* filter: drop-shadow(5px 12px 5px #78787825); */
  background-color: #eee9e9;
  width: 80%;
  height: 20px;
  border-radius: 30px;
  /* border: 1px solid black; */
}

.done-pg-bar {
  background-color: #a5dea0;
  height: 100%;
  width: 0%;
  border-radius: 30px;
  transition: width 0.5s ease;
}

.games {
  margin-top: 20px;
  grid-area: game;
}

.games-flex-container {
  display: flex;
  justify-content: space-around;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.back-to-learn-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background-color: #97cb9c;
  filter: drop-shadow(12px 5px 5px #9a999925);
  border-radius: 50%;
  cursor: pointer;
  margin-top: 5px;
  margin-right: auto;
}

.back-to-learn-btn-area {
  width: 90%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  margin-top: 15px;
}

.back-to-learn-btn:hover {
  transform: scale(1.05);
  transition-duration: 0.2s;
}

.game-name {
  font-size: 24px;
  font-weight: 600;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.game-description {
  font-size: 16px;
  color: #7d7d7d;
  background-color: #f0f0f0;
  padding: 10px 15px;
  border-radius: 8px;
  text-align: center;
  max-width: 80%;
}

.profile-nav-block{
  margin-top: 0px;
}
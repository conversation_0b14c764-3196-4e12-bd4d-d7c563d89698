import { React, useState } from "react";
import useAuthStore from "src/hooks/authStore.js";
import axiosInstance from "src/utils/axios";
import LoadingSpinner from "src/components/LoadingSpinner";
import { toast, ToastContainer } from "react-toastify"; // TODO implement toast
import { useTranslation } from "react-i18next";
const Logout = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const logout = useAuthStore((state) => state.logout);
  const handleLogout = async () => {
    setLoading(true);
    const response = await axiosInstance.post("/auth/logout_knox/");
    logout();
    setLoading(false);
  };

  return (
    <div>
      {loading && <LoadingSpinner />}
      {!loading && <button onClick={handleLogout}>{t("logout_btn")}</button>}
    </div>
  );
};

export default Logout;

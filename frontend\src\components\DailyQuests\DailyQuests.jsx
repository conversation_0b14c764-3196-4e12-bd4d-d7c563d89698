import React from "react";
import questBookSvg from "src/assets/quest_book.svg";
import questClockSvg from "src/assets/quest_clock.svg";
import "src/components/DailyQuests/DailyQuests.css";
import { useTranslation } from "react-i18next";

function DailyQuests() {
  const { t } = useTranslation();
  const quests = [
    {
      imgSrc: questBookSvg,
      title: t("daily_quest1_demo"),
      progress: "1 / 1",
      progressClass: "pg-bar-done",
    },
    {
      imgSrc: questClockSvg,
      title: t("daily_quest2_demo"),
      progress: "4 / 10",
      progressClass: "pg-bar-done pg-bar-done-40",
    },
  ];

  return (
    <div className="daily-quests-container">
      <a href="" className="dq-title">
        {t("daily_quests").toUpperCase()}
      </a>
      {quests.map((quest, index) => (
        <div key={index} className="quest-container">
          <img src={quest.imgSrc} alt="quest icon" className="quest_pic" />
          <div className="quest-stat">
            <a className="quest-title">{quest.title}</a>
            <div className="pg-bar">
              <div className={quest.progressClass}>
                <a className="pg-text">{quest.progress}</a>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export default DailyQuests;

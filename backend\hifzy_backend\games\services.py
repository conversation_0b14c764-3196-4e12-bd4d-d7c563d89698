"""
Service layer for the games app.

This module contains business logic for game-related operations.
Services handle game generation, validation, and coordination.
"""

import random
from rest_framework.exceptions import ValidationError

from quran.transports import AyahText

from . import transports
from quran.utils import to_arabic_numerals
from quran.services import QuranService, LanguageService
from quran.repositories import AudioRecordingRepository, AyahRepository, SurahRepository


class GameService:
    """Service for game generation and management."""

    @staticmethod
    def validate_ayah_range(surah_id: int, start_ayah_number: int, count: int) -> tuple:
        """
        Validate ayah range and return surah info and actual count.

        Returns:
            tuple: (surah_info, actual_count) where actual_count may be adjusted
                   if the requested count exceeds available ayahs
        """
        # Validate surah exists
        surah_info = QuranService.get_surah_info(surah_id)
        if not surah_info:
            raise ValidationError(f"Surah with ID {surah_id} not found")

        # Validate starting ayah number
        if start_ayah_number < 1:
            raise ValidationError("Starting ayah number must be at least 1")

        if start_ayah_number > surah_info.verses_count:
            raise ValidationError(
                f"Starting ayah number {start_ayah_number} exceeds total ayahs in Surah {surah_id} "
                f"(total: {surah_info.verses_count})"
            )

        # Validate count
        if count < 1:
            raise ValidationError("Count must be at least 1")

        # Calculate maximum available ayahs from starting point
        max_available = surah_info.verses_count - start_ayah_number + 1

        if count > max_available:
            # Adjust count to maximum available instead of raising error
            actual_count = max_available
        else:
            actual_count = count

        return surah_info, actual_count

    @staticmethod
    def create_game_metadata(
        surah_info, start_ayah_number: int, requested_count: int, actual_count: int
    ) -> transports.GameMetadata:
        """Create metadata for game responses."""
        end_ayah_number = start_ayah_number + actual_count - 1
        surah = SurahRepository.get_surah_by_number(surah_info.surah_id)
        return transports.GameMetadata(
            surah_id=surah_info.surah_id,
            surah_name=surah_info.name_simple,
            start_ayah_number=start_ayah_number,
            end_ayah_number=end_ayah_number,
            total_ayahs_requested=requested_count,
            total_ayahs_returned=actual_count,
            total_ayahs_in_surah=surah_info.verses_count,
            max_xp_awarded=actual_count * surah.xp_per_ayah,
        )

    @staticmethod
    def convert_ayahs_to_enhanced_data(ayahs: list, surah_id: int, request=None) -> list:
        """Convert ayah data to enhanced format with absolute image URLs."""
        from quran.services import ImageService

        enhanced_ayahs = []

        for ayah in ayahs:
            # Get image URL if available
            ayah_obj = AyahRepository.get_ayah_by_surah_and_number(surah_id, ayah.ayah_id)

            # Build absolute image URL
            if ayah_obj and ayah_obj.image:
                if request:
                    image_url = ImageService.build_absolute_image_url(request, ayah_obj.image)
                else:
                    image_url = ImageService.build_absolute_image_url_from_settings(ayah_obj.image)
            else:
                image_url = None

            enhanced_ayah = transports.EnhancedAyahData(
                ayah_number=ayah.ayah_id,
                arabic_text=ayah.arabic_text,
                transliteration=ayah.transliteration,
                translation=ayah.translation,
                image_url=image_url,
            )
            enhanced_ayahs.append(enhanced_ayah)

        return enhanced_ayahs

    @staticmethod
    def convert_ayahs_to_unified_data(ayahs: list, surah_id: int, request=None) -> list:
        """Convert ayah data to unified format for memory and matching games with absolute image URLs."""
        from quran.services import ImageService

        unified_ayahs = []

        for ayah in ayahs:
            # Get image URL if available
            ayah_obj = AyahRepository.get_ayah_by_surah_and_number(surah_id, ayah.ayah_id)

            # Build absolute image URL
            if ayah_obj and ayah_obj.image:
                if request:
                    image_url = ImageService.build_absolute_image_url(request, ayah_obj.image)
                else:
                    image_url = ImageService.build_absolute_image_url_from_settings(ayah_obj.image)
            else:
                image_url = None

            unified_ayah = transports.UnifiedAyahData(
                ayah_number=ayah.ayah_id,
                arabic_text=ayah.arabic_text,
                translation=ayah.translation,
                transliteration=ayah.transliteration,
                image_url=image_url,
            )
            unified_ayahs.append(unified_ayah)

        return unified_ayahs

    @staticmethod
    def convert_ayahs_to_pairing_format(ayahs: list, surah_id: int, surah_name: str, request=None) -> tuple[list, list]:
        """Convert ayah data to unified pairing format for memory and matching games with absolute image URLs."""
        from quran.services import ImageService

        images = []
        texts = []

        for ayah in ayahs:
            # Get image URL if available
            ayah_obj = AyahRepository.get_ayah_by_surah_and_number(surah_id, ayah.ayah_id)

            # Build absolute image URL
            if ayah_obj and ayah_obj.image:
                if request:
                    image_url = ImageService.build_absolute_image_url(request, ayah_obj.image)
                else:
                    image_url = ImageService.build_absolute_image_url_from_settings(ayah_obj.image)
            else:
                image_url = None

            # Create unique pair_id for this ayah
            pair_id = f"ayah_{surah_id}_{ayah.ayah_id}"

            # Create image object
            image_obj = transports.PairingImageObject(image_url=image_url, pair_id=pair_id)
            images.append(image_obj)

            # Create text object
            text_obj = transports.PairingTextObject(
                arabic_text=ayah.arabic_text,
                translation=ayah.translation,
                transliteration=ayah.transliteration,
                pair_id=pair_id,
                ayah_number=ayah.ayah_id,
            )
            texts.append(text_obj)

        return images, texts

    @staticmethod
    def validate_game_request(surah_id: int, ayah_id: int, selected_ayah: int) -> None:
        """Legacy validation method for backward compatibility."""
        # Validate surah exists
        surah_info = QuranService.get_surah_info(surah_id)

        if ayah_id < 1 or ayah_id > surah_info.verses_count:
            raise ValidationError(f"Invalid ayah_id: {ayah_id}")

        if selected_ayah < 1:
            raise ValidationError("selected_ayah must be at least 1")

        # Check if we have enough ayahs from the starting point
        max_ayahs = surah_info.verses_count - ayah_id + 1
        if selected_ayah > max_ayahs:
            raise ValidationError(f"Not enough ayahs available. Maximum: {max_ayahs}")

    @staticmethod
    def generate_shuffling_game_enhanced(
        surah_id: int, start_ayah_number: int, count: int, language: str = "en"
    ) -> tuple[transports.ShufflingGameResponse, object]:
        """Generate an enhanced shuffling game for the specified ayah range with language support."""
        # Validate and get surah info
        surah_info, actual_count = GameService.validate_ayah_range(surah_id, start_ayah_number, count)

        # Get the ayahs with language support
        ayahs, language_metadata = LanguageService.get_surah_ayahs_with_language(surah_id, language)

        # Filter ayahs to the requested range
        filtered_ayahs = [
            ayah for ayah in ayahs if start_ayah_number <= ayah.ayah_id < start_ayah_number + actual_count
        ]

        if not filtered_ayahs:
            raise ValidationError("No ayahs found for the specified range")

        # Create metadata
        metadata = GameService.create_game_metadata(surah_info, start_ayah_number, count, actual_count)

        # Create the sentence with gaps (using Arabic numerals)
        sentence_parts = []
        for i, _ in enumerate(filtered_ayahs):
            sentence_parts.append(to_arabic_numerals(str(i + 1)))
        sentence_with_numbers = " ".join(sentence_parts)

        # Create shuffled phrases
        shuffled_ayahs = filtered_ayahs.copy()
        random.shuffle(shuffled_ayahs)

        phrases = [transports.GamePhrase(id=f"phrase-{ayah.ayah_id}", text=ayah.arabic_text) for ayah in shuffled_ayahs]

        # Create correct sequence
        correct_sequence = [f"phrase-{ayah.ayah_id}" for ayah in filtered_ayahs]

        game_response = transports.ShufflingGameResponse(
            metadata=metadata,
            sentence=sentence_with_numbers,
            phrases=phrases,
            correct_sequence=correct_sequence,
        )

        return game_response, language_metadata

    # @staticmethod
    # def generate_shuffling_game(surah_id: int, ayah_id: int, selected_ayah: int) -> transports.ShufflingGameResponse:
    #     """Legacy method for backward compatibility."""
    #     return GameService.generate_shuffling_game_enhanced(surah_id, ayah_id, selected_ayah)

    @staticmethod
    def generate_matching_game_enhanced(
        surah_id: int, start_ayah_number: int, count: int, language: str = "en"
    ) -> tuple[transports.MatchingGameResponse, object]:
        """Generate an enhanced matching game for the specified ayah range with unified pairing system."""
        # Validate and get surah info
        surah_info, actual_count = GameService.validate_ayah_range(surah_id, start_ayah_number, count)

        # Get the ayahs with language support
        ayahs, language_metadata = LanguageService.get_surah_ayahs_with_language(surah_id, language)

        # Filter ayahs to the requested range
        filtered_ayahs = [
            ayah for ayah in ayahs if start_ayah_number <= ayah.ayah_id < start_ayah_number + actual_count
        ]

        if not filtered_ayahs:
            raise ValidationError("No ayahs found for the specified range")

        # Create metadata
        metadata = GameService.create_game_metadata(surah_info, start_ayah_number, count, actual_count)

        # Convert to unified pairing format
        images, texts = GameService.convert_ayahs_to_pairing_format(filtered_ayahs, surah_id, surah_info.name_simple)

        game_response = transports.MatchingGameResponse(metadata=metadata, images=images, texts=texts)

        return game_response, language_metadata

    # @staticmethod
    # def generate_matching_game(surah_id: int, ayah_id: int, selected_ayah: int) -> transports.MatchingGameResponse:
    #     """Legacy method for backward compatibility."""
    #     return GameService.generate_matching_game_enhanced(surah_id, ayah_id, selected_ayah)

    @staticmethod
    def generate_memory_game_enhanced(
        surah_id: int, start_ayah_number: int, count: int, language: str = "en"
    ) -> tuple[transports.MemoryGameResponse, object]:
        """Generate an enhanced memory game for the specified ayah range with unified pairing system."""
        # Validate and get surah info
        surah_info, actual_count = GameService.validate_ayah_range(surah_id, start_ayah_number, count)

        # Get the ayahs with language support
        ayahs, language_metadata = LanguageService.get_surah_ayahs_with_language(surah_id, language)

        # Filter ayahs to the requested range
        filtered_ayahs = [
            ayah for ayah in ayahs if start_ayah_number <= ayah.ayah_id < start_ayah_number + actual_count
        ]

        if not filtered_ayahs:
            raise ValidationError("No ayahs found for the specified range")

        # Create metadata
        metadata = GameService.create_game_metadata(surah_info, start_ayah_number, count, actual_count)

        # Convert to unified pairing format
        images, texts = GameService.convert_ayahs_to_pairing_format(filtered_ayahs, surah_id, surah_info.name_simple)

        game_response = transports.MemoryGameResponse(metadata=metadata, images=images, texts=texts)

        return game_response, language_metadata

    # @staticmethod
    # def generate_memory_game(surah_id: int, ayah_id: int, selected_ayah: int) -> transports.MemoryGameResponse:
    #     """Legacy method for backward compatibility."""
    #     return GameService.generate_memory_game_enhanced(surah_id, ayah_id, selected_ayah)

    @staticmethod
    def generate_audio_game_enhanced(
        surah_id: int, start_ayah_number: int, count: int, language: str = "en"
    ) -> tuple[transports.AudioGameResponse, object]:
        """Generate an enhanced audio game with multi-level support for the specified ayah range with language support."""
        # Validate and get surah info
        surah_info, actual_count = GameService.validate_ayah_range(surah_id, start_ayah_number, count)

        # Get the ayahs with language support
        ayahs, language_metadata = LanguageService.get_surah_ayahs_with_language(surah_id, language)

        # Filter ayahs to the requested range
        filtered_ayahs = [
            ayah for ayah in ayahs if start_ayah_number <= ayah.ayah_id < start_ayah_number + actual_count
        ]

        if not filtered_ayahs:
            raise ValidationError("No ayahs found for the specified range")

        # Create metadata
        metadata = GameService.create_game_metadata(surah_info, start_ayah_number, count, actual_count)

        # Create levels for each ayah
        levels = []
        for i, ayah in enumerate(filtered_ayahs):
            level = GameService._create_audio_level(i + 1, ayah, filtered_ayahs, surah_id)
            levels.append(level)

        game_response = transports.AudioGameResponse(metadata=metadata, levels=levels)

        return game_response, language_metadata

    @staticmethod
    def _create_audio_level(
        level_number: int, target_ayah: AyahText, all_ayahs: list, surah_id: int
    ) -> transports.AudioLevel:
        """Create an audio level for a single ayah."""
        # Generate audio URL for the target ayah by ayah model via repositories
        audio_url = AudioRecordingRepository.get_audio_recording_by_ayah_and_reader(target_ayah.ayah_id, "alafasy")

        # Create options from all ayahs in the range
        options = [ayah.arabic_text for ayah in all_ayahs]

        # The correct answer is the target ayah
        correct_answer = target_ayah.arabic_text

        # Shuffle options to randomize the order
        random.shuffle(options)

        return transports.AudioLevel(
            level_number=level_number,
            ayah_number=target_ayah.ayah_id,
            audio_url=audio_url,
            options=options,
            correct_answer=correct_answer,
        )

    @staticmethod
    def generate_gaps_game_enhanced(
        surah_id: int, start_ayah_number: int, count: int, language: str = "en"
    ) -> tuple[transports.GapsGameResponse, object]:
        """Generate an enhanced gaps game for the specified ayah range with language support."""
        # Validate and get surah info
        surah_info, actual_count = GameService.validate_ayah_range(surah_id, start_ayah_number, count)

        # Get the ayahs with language support
        ayahs, language_metadata = LanguageService.get_surah_ayahs_with_language(surah_id, language)

        # Filter ayahs to the requested range
        filtered_ayahs = [
            ayah for ayah in ayahs if start_ayah_number <= ayah.ayah_id < start_ayah_number + actual_count
        ]

        if not filtered_ayahs:
            raise ValidationError("No ayahs found for the specified range")

        # Create metadata
        metadata = GameService.create_game_metadata(surah_info, start_ayah_number, count, actual_count)

        # Create levels for each ayah
        levels = []
        for i, ayah in enumerate(filtered_ayahs):
            level = GameService._create_gaps_level(i + 1, ayah)
            levels.append(level)

        game_response = transports.GapsGameResponse(metadata=metadata, levels=levels)

        return game_response, language_metadata

    @staticmethod
    def _create_gaps_level(level_number: int, ayah: AyahText) -> transports.GapsLevel:
        """Create a gaps level for complete word reconstruction of a single ayah."""
        # Split ayah into words
        words = ayah.arabic_text.split()
        word_count = len(words)

        # Create shuffled word options (all words from the ayah)
        word_options = words.copy()
        random.shuffle(word_options)

        # Create correct sequence (complete ordered list of words)
        correct_sequence = words.copy()

        audio = AudioRecordingRepository.get_audio_recording_by_ayah_and_reader(ayah.ayah_id, "alafasy")

        return transports.GapsLevel(
            level_number=level_number,
            ayah_number=ayah.ayah_id,
            total_gaps=word_count,
            word_options=word_options,
            correct_sequence=correct_sequence,
            original_text=ayah.arabic_text,
            audio_url=audio.audio_url,
            transliteration_text=ayah.transliteration,
        )

    # @staticmethod
    # def generate_audio_game(surah_id: int, ayah_id: int, selected_ayah: int) -> transports.AudioGameResponse:
    #     """Legacy method for backward compatibility."""
    #     return GameService.generate_audio_game_enhanced(surah_id, ayah_id, selected_ayah)


class ArabicLetterGameService:
    """Service for Arabic letter learning games."""

    @staticmethod
    def generate_audio_recognition_game(letter_id: int) -> transports.AudioRecognitionGameResponse:
        """Generate an audio recognition game for a specific Arabic letter."""
        from quran.repositories import ArabicLetterRepository
        import random

        # Get the target letter
        target_letter = ArabicLetterRepository.get_letter_by_id(letter_id)
        if not target_letter:
            raise ValidationError(f"Arabic letter with ID {letter_id} not found")

        # if not target_letter.audio_url:
        #     raise ValidationError(f"Arabic letter '{target_letter.title}' does not have audio pronunciation")

        # Get all letters for transliteration options
        all_letters = ArabicLetterRepository.get_all_letters()
        if len(all_letters) < 4:
            raise ValidationError("Not enough Arabic letters to generate game options")

        # Remove target letter from options pool
        other_letters = [letter for letter in all_letters if letter.id != letter_id]

        # Select 3 random incorrect options
        num_incorrect = min(3, len(other_letters))
        incorrect_letters = random.sample(other_letters, num_incorrect)

        # Create transliteration options
        transliteration_options = []

        # Add correct option
        correct_option = transports.TransliterationOption(
            id=f"option_{target_letter.id}",
            transliteration_text=target_letter.transcription,
            is_correct=True,
        )
        transliteration_options.append(correct_option)

        # Add incorrect options
        for letter in incorrect_letters:
            incorrect_option = transports.TransliterationOption(
                id=f"option_{letter.id}",
                transliteration_text=letter.transcription,
                is_correct=False,
            )
            transliteration_options.append(incorrect_option)

        # Shuffle options
        random.shuffle(transliteration_options)

        # Create metadata
        metadata = transports.ArabicLetterGameMetadata(
            letter_id=target_letter.id,
            letter_title=target_letter.title,
            letter_transcription=target_letter.transcription,
            game_type="audio_recognition",
            total_options=len(transliteration_options),
        )

        return transports.AudioRecognitionGameResponse(
            metadata=metadata,
            target_letter=target_letter.title,
            target_audio_url=target_letter.audio_url,
            transliteration_options=transliteration_options,
            correct_transliteration=target_letter.transcription,
        )

    @staticmethod
    def generate_transcription_matching_game(letter_id: int) -> transports.TranscriptionMatchingGameResponse:
        """Generate a transcription matching game for a specific Arabic letter."""
        from quran.repositories import ArabicLetterRepository
        import random

        # Get the target letter
        target_letter = ArabicLetterRepository.get_letter_by_id(letter_id)
        if not target_letter:
            raise ValidationError(f"Arabic letter with ID {letter_id} not found")

        # Get all letters for options
        all_letters = ArabicLetterRepository.get_all_letters()
        if len(all_letters) < 4:
            raise ValidationError("Not enough Arabic letters to generate game options")

        # Remove target letter from options pool
        other_letters = [letter for letter in all_letters if letter.id != letter_id]

        # Select 3 random incorrect options
        num_incorrect = min(3, len(other_letters))
        incorrect_letters = random.sample(other_letters, num_incorrect)

        # Create letter options
        letter_options = []

        # Add correct option
        correct_option = transports.LetterOption(
            id=f"option_{target_letter.id}",
            letter_title=target_letter.title,
            letter_id=target_letter.id,
            is_correct=True,
        )
        letter_options.append(correct_option)

        # Add incorrect options
        for letter in incorrect_letters:
            incorrect_option = transports.LetterOption(
                id=f"option_{letter.id}", letter_title=letter.title, letter_id=letter.id, is_correct=False
            )
            letter_options.append(incorrect_option)

        # Shuffle options
        random.shuffle(letter_options)

        # Create metadata
        metadata = transports.ArabicLetterGameMetadata(
            letter_id=target_letter.id,
            letter_title=target_letter.title,
            letter_transcription=target_letter.transcription,
            game_type="transcription_matching",
            total_options=len(letter_options),
        )

        return transports.TranscriptionMatchingGameResponse(
            metadata=metadata,
            target_transcription=target_letter.transcription,
            letter_options=letter_options,
            correct_letter_id=target_letter.id,
        )

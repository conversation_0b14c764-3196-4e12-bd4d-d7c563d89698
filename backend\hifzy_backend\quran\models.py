from django.db import models


class Surah(models.Model):
    """Model for Quran Surahs."""

    surah_number = models.IntegerField(unique=True, help_text="Surah number (1-114)")
    ayah_count = models.IntegerField(help_text="Number of ayahs in this surah")
    xp_per_ayah = models.IntegerField(default=30, help_text="Experience points per ayah")
    is_pre_bismillah = models.BooleanField(default=False, help_text="Whether the surah has a pre-bismillah")
    is_meccan = models.BooleanField(default=False, help_text="Whether the surah was revealed in Mecca")
    is_medinian = models.<PERSON><PERSON>anField(default=False, help_text="Whether the surah was revealed in Medina")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Surah"
        verbose_name_plural = "Surahs"
        ordering = ["surah_number"]

    def __str__(self):
        return f"Surah {self.surah_number}"


class SurahTranslation(models.Model):
    """Model for Surah translations."""

    surah = models.ForeignKey(Surah, on_delete=models.CASCADE, related_name="translations")
    locale = models.Char<PERSON>ield(max_length=50, help_text="Language locale (e.g., 'en', 'ru', 'en_transliteration')")
    title = models.CharField(max_length=200, help_text="Translated title")

    class Meta:
        unique_together = ("surah", "locale")
        verbose_name = "Surah Translation"
        verbose_name_plural = "Surah Translations"

    def __str__(self):
        return f"{self.surah} - {self.locale}: {self.title}"


class Ayah(models.Model):
    """Model for Quran Ayahs."""

    ayah_number = models.IntegerField(help_text="Ayah number within the surah")
    surah = models.ForeignKey(Surah, on_delete=models.CASCADE, related_name="ayahs")
    image = models.CharField(max_length=500, blank=True, null=True, help_text="Ayah image URL")

    class Meta:
        unique_together = ("surah", "ayah_number")
        verbose_name = "Ayah"
        verbose_name_plural = "Ayahs"
        ordering = ["surah__surah_number", "ayah_number"]

    def __str__(self):
        return f"Surah {self.surah.surah_number}, Ayah {self.ayah_number}"


class AyahTranslation(models.Model):
    """Model for Ayah translations."""

    ayah = models.ForeignKey(Ayah, on_delete=models.CASCADE, related_name="translations")
    locale = models.CharField(max_length=50, help_text="Language locale (e.g., 'en', 'ru', 'en_transliteration')")
    text = models.TextField(help_text="Translated text")
    description = models.TextField(blank=True, null=True, help_text="Additional description or tafsir")

    class Meta:
        unique_together = ("ayah", "locale")
        verbose_name = "Ayah Translation"
        verbose_name_plural = "Ayah Translations"

    def __str__(self):
        return f"{self.ayah} - {self.locale}"


class AudioRecording(models.Model):
    """Model for Ayah audio recordings."""

    ayah = models.ForeignKey(Ayah, on_delete=models.CASCADE, related_name="audio_recordings")
    audio_url = models.URLField(max_length=500, help_text="URL to the audio file")
    reader_name = models.CharField(max_length=200, help_text="Name of the Quran reader/reciter")
    reader_slug = models.SlugField(max_length=100, help_text="URL-friendly reader identifier")
    audio_format = models.CharField(max_length=10, default="mp3", help_text="Audio file format (e.g., mp3, wav)")
    duration_seconds = models.PositiveIntegerField(null=True, blank=True, help_text="Duration in seconds")
    file_size_bytes = models.PositiveIntegerField(null=True, blank=True, help_text="File size in bytes")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("ayah", "reader_slug")
        verbose_name = "Audio Recording"
        verbose_name_plural = "Audio Recordings"
        ordering = ["ayah__surah__surah_number", "ayah__ayah_number", "reader_name"]

    def __str__(self):
        return f"{self.ayah} - {self.reader_name}"


class ArabicLetter(models.Model):
    """Model for Arabic letters."""

    title = models.CharField(max_length=100, help_text="Arabic letter title")
    transcription = models.CharField(max_length=100, help_text="Letter transcription")
    audio_url = models.CharField(
        max_length=500, blank=True, null=True, help_text="URL/path to the audio file for letter pronunciation"
    )

    class Meta:
        verbose_name = "Arabic Letter"
        verbose_name_plural = "Arabic Letters"

    def __str__(self):
        return f"{self.title} ({self.transcription})"

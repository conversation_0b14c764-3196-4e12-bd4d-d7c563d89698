from django.urls import path
from .views import (
    FollowUserView,
    UnfollowUserView,
    ListFollowersView,
    ListFollowingView,
    CheckFollowStatusView,
    GlobalLeaderboardView,
    FollowedUsersLeaderboardView,
)

urlpatterns = [
    # Following/Followers
    path("follow/", FollowUserView.as_view(), name="follow-user"),
    path("unfollow/", UnfollowUserView.as_view(), name="unfollow-user"),
    path("followers/", ListFollowersView.as_view(), name="list-followers"),
    path("following/", ListFollowingView.as_view(), name="list-following"),
    path("follow-status/", CheckFollowStatusView.as_view(), name="check-follow-status"),
    path("leaderboard/global/", GlobalLeaderboardView.as_view(), name="global_leaderboard"),
    path("leaderboard/following/", FollowedUsersLeaderboardView.as_view(), name="followed_users_leaderboard"),
]

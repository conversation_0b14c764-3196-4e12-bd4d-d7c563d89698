"""
Core response schema serializers for API documentation.

This module provides base response serializers that are used across all apps
to ensure consistent API response documentation in Swagger/OpenAPI.
"""

from rest_framework import serializers


class BaseSuccessResponseSerializer(serializers.Serializer):
    """Base serializer for all successful API responses."""

    success = serializers.BooleanField(default=True, help_text="Indicates if the request was successful")
    message = serializers.CharField(help_text="Human-readable success message")


class BaseErrorResponseSerializer(serializers.Serializer):
    """Base serializer for all error API responses."""

    success = serializers.BooleanField(default=False, help_text="Indicates if the request was successful")
    message = serializers.CharField(help_text="Human-readable error message")
    errors = serializers.DictField(
        required=False, help_text="Detailed error information (field-specific validation errors)"
    )


class PaginationMetadataSerializer(serializers.Serializer):
    """Serializer for pagination metadata."""

    page = serializers.IntegerField(help_text="Current page number")
    per_page = serializers.IntegerField(help_text="Number of items per page")
    total_count = serializers.IntegerField(help_text="Total number of items")
    total_pages = serializers.IntegerField(help_text="Total number of pages")
    has_next = serializers.BooleanField(help_text="Whether there is a next page")
    has_prev = serializers.BooleanField(help_text="Whether there is a previous page")
    next_page = serializers.IntegerField(allow_null=True, help_text="Next page number (null if no next page)")
    prev_page = serializers.IntegerField(allow_null=True, help_text="Previous page number (null if no previous page)")


class PaginatedResponseSerializer(BaseSuccessResponseSerializer):
    """Base serializer for paginated responses."""

    data = serializers.DictField(help_text="Response data containing items and pagination")

    class Meta:
        abstract = True


class LanguageMetadataSerializer(serializers.Serializer):
    """Serializer for language selection metadata."""

    requested_language = serializers.CharField(help_text="Language code that was requested")
    used_language = serializers.CharField(help_text="Language code that was actually used")
    fallback_applied = serializers.BooleanField(help_text="Whether fallback to default language was applied")
    available_languages = serializers.ListField(
        child=serializers.CharField(), help_text="List of available language codes"
    )


class UserBasicInfoSerializer(serializers.Serializer):
    """Basic user information serializer for responses."""

    id = serializers.IntegerField(help_text="User ID")
    username = serializers.CharField(help_text="Username")
    first_name = serializers.CharField(help_text="User's first name")
    last_name = serializers.CharField(help_text="User's last name")
    total_xp = serializers.IntegerField(help_text="Total experience points")
    current_level = serializers.IntegerField(help_text="Current user level")
    day_streak = serializers.IntegerField(help_text="Current day streak")
    gender = serializers.CharField(allow_null=True, required=False, help_text="User's gender")


class UserProfileSerializer(UserBasicInfoSerializer):
    """Extended user profile information serializer."""

    profile_photo = serializers.URLField(allow_null=True, help_text="URL to user's profile photo")


class CompactUserSerializer(serializers.Serializer):
    """Compact user information serializer."""

    id = serializers.IntegerField(help_text="User ID")
    username = serializers.CharField(help_text="Username")
    first_name = serializers.CharField(help_text="First name")
    last_name = serializers.CharField(help_text="Last name")
    total_xp = serializers.IntegerField(help_text="Total experience points")
    current_level = serializers.IntegerField(help_text="Current user level")
    day_streak = serializers.IntegerField(help_text="Current day streak")
    gender = serializers.CharField(allow_null=True, help_text="User's gender")


class LeagueSerializer(serializers.Serializer):
    """League information serializer."""

    id = serializers.IntegerField(help_text="League ID")
    rank = serializers.CharField(help_text="League rank (Bronze, Silver, Gold)")
    end_time = serializers.DateTimeField(help_text="League end time")
    start_time = serializers.DateTimeField(help_text="League start time")
    icon = serializers.CharField(allow_null=True, help_text="League icon")
    name = serializers.CharField(help_text="League name")


class SocialSummarySerializer(serializers.Serializer):
    """Social information summary serializer."""

    followers_count = serializers.IntegerField(help_text="Number of followers")
    following_count = serializers.IntegerField(help_text="Number of users being followed")
    followers = CompactUserSerializer(many=True, help_text="List of followers (limited)")
    following = CompactUserSerializer(many=True, help_text="List of users being followed (limited)")


class RecentBookmarkSerializer(serializers.Serializer):
    """Recent bookmark information serializer."""

    id = serializers.IntegerField(help_text="Bookmark ID")
    surah_id = serializers.IntegerField(help_text="Surah ID")
    ayah_number = serializers.IntegerField(help_text="Ayah number")
    surah_title = serializers.CharField(allow_null=True, help_text="Surah title")
    arabic_text = serializers.CharField(allow_null=True, help_text="Arabic text of the ayah")
    created_at = serializers.DateTimeField(help_text="When the bookmark was created")


class BookmarkSummarySerializer(serializers.Serializer):
    """Bookmark summary information serializer."""

    bookmarks_count = serializers.IntegerField(help_text="Total number of bookmarks")
    recent_bookmarks = RecentBookmarkSerializer(many=True, help_text="List of recent bookmarks (limited)")


class LeaguePositionSerializer(serializers.Serializer):
    """User's position within their current league serializer."""

    league = LeagueSerializer(allow_null=True, help_text="Current league information")
    user_rank = serializers.IntegerField(allow_null=True, help_text="User's rank within the league")
    total_participants = serializers.IntegerField(allow_null=True, help_text="Total participants in the league")


class EnhancedUserProfileSerializer(serializers.Serializer):
    """Enhanced user profile serializer with social, league, and bookmark information."""

    # Basic profile fields
    id = serializers.IntegerField(help_text="User ID")
    username = serializers.CharField(help_text="Username")
    first_name = serializers.CharField(help_text="First name")
    last_name = serializers.CharField(help_text="Last name")
    email = serializers.EmailField(help_text="Email address")
    profile_photo = serializers.URLField(allow_null=True, help_text="Profile photo URL")
    date_joined = serializers.DateTimeField(help_text="Date when user joined")
    total_xp = serializers.IntegerField(help_text="Total experience points")
    current_level = serializers.IntegerField(help_text="Current user level")
    day_streak = serializers.IntegerField(help_text="Current day streak")
    gender = serializers.CharField(allow_null=True, help_text="User's gender")

    # Enhanced fields
    social_summary = SocialSummarySerializer(help_text="Social information summary")
    league_position = LeaguePositionSerializer(help_text="League position information")
    bookmark_summary = BookmarkSummarySerializer(help_text="Bookmark summary information")


class AuthTokenResponseSerializer(serializers.Serializer):
    """Serializer for authentication token responses."""

    token = serializers.CharField(help_text="Authentication token")
    user = UserProfileSerializer(help_text="User profile information")


class CountResponseSerializer(serializers.Serializer):
    """Serializer for responses that include a count."""

    count = serializers.IntegerField(help_text="Number of items")


class ExperienceAwardedSerializer(serializers.Serializer):
    """Serializer for experience points awarded."""

    experience_awarded = serializers.IntegerField(help_text="Amount of experience points awarded")


class SurahBasicInfoSerializer(serializers.Serializer):
    """Basic Surah information serializer."""

    surah_id = serializers.IntegerField(help_text="Surah ID")
    name = serializers.CharField(help_text="Surah name")
    name_simple = serializers.CharField(help_text="Simple Surah name")
    arabic_name = serializers.CharField(help_text="Original Arabic name of the surah")
    verses_count = serializers.IntegerField(help_text="Number of verses in the Surah")
    is_pre_bismillah = serializers.BooleanField(help_text="Whether the surah has a pre-bismillah")
    is_meccan = serializers.BooleanField(help_text="Whether the surah was revealed in Mecca")
    is_medinian = serializers.BooleanField(help_text="Whether the surah was revealed in Medina")


class AyahBasicInfoSerializer(serializers.Serializer):
    """Basic Ayah information serializer."""

    surah_id = serializers.IntegerField(help_text="Surah ID")
    ayah_id = serializers.IntegerField(help_text="Ayah ID within the Surah")
    arabic_text = serializers.CharField(help_text="Arabic text of the Ayah")
    transliteration = serializers.CharField(allow_null=True, help_text="Transliteration of the Ayah")
    translation = serializers.CharField(allow_null=True, help_text="Translation of the Ayah")


class AudioInfoSerializer(serializers.Serializer):
    """Audio information serializer."""

    audio_url = serializers.URLField(allow_null=True, help_text="URL to audio file")


class ReaderInfoSerializer(serializers.Serializer):
    """Audio reader information serializer."""

    id = serializers.IntegerField(help_text="Reader ID")
    name = serializers.CharField(help_text="Reader name")
    slug = serializers.CharField(help_text="Reader slug identifier")
    count = serializers.IntegerField(help_text="Number of available recordings")


class LanguageInfoSerializer(serializers.Serializer):
    """Language information serializer."""

    code = serializers.CharField(help_text="Language code")
    name = serializers.CharField(help_text="Language name")
    has_translation = serializers.BooleanField(help_text="Whether translations are available in this language")
    has_transliteration = serializers.BooleanField(help_text="Whether transliterations are available in this language")


# Game-related response schemas
class GameMetadataSerializer(serializers.Serializer):
    """Game metadata serializer."""

    surah_id = serializers.IntegerField(help_text="Surah ID")
    surah_name = serializers.CharField(help_text="Surah name")
    start_ayah_number = serializers.IntegerField(help_text="Starting ayah number")
    ayah_count = serializers.IntegerField(help_text="Number of ayahs in the game")
    total_ayahs_in_surah = serializers.IntegerField(help_text="Total ayahs in the surah")
    max_xp_awarded = serializers.IntegerField(help_text="Maximum XP that can be awarded for completing the game")


class GamePhraseSerializer(serializers.Serializer):
    """Game phrase serializer for shuffling games."""

    id = serializers.CharField(help_text="Phrase ID")
    text = serializers.CharField(help_text="Phrase text")


class GameCardSerializer(serializers.Serializer):
    """Game card serializer for memory games."""

    id = serializers.CharField(help_text="Card ID")
    type = serializers.CharField(help_text="Card type (text/image)")
    content = serializers.CharField(help_text="Card content")
    match_id = serializers.CharField(help_text="ID of the matching card")


class GameItemSerializer(serializers.Serializer):
    """Game item serializer for matching games."""

    id = serializers.CharField(help_text="Item ID")
    content = serializers.CharField(help_text="Item content")
    type = serializers.CharField(help_text="Item type")


class GameLevelSerializer(serializers.Serializer):
    """Game level serializer for multi-level games."""

    level_number = serializers.IntegerField(help_text="Level number")
    ayah_number = serializers.IntegerField(help_text="Ayah number for this level")
    total_gaps = serializers.IntegerField(help_text="Total number of gaps to fill")
    word_options = serializers.ListField(child=serializers.CharField(), help_text="Available word options")
    correct_sequence = serializers.ListField(child=serializers.CharField(), help_text="Correct sequence of words")
    original_text = serializers.CharField(help_text="Original ayah text")
    audio_url = serializers.URLField(help_text="URL to audio recording")
    transliteration_text = serializers.CharField(help_text="Transliteration of the ayah")


class UnifiedAyahDataSerializer(serializers.Serializer):
    """
    Unified ayah data serializer for memory and matching games.

    This serializer provides a consistent structure for ayah data across
    memory and matching games, containing all necessary information for
    game functionality including text, translations, and visual elements.
    """

    ayah_number = serializers.IntegerField(help_text="Ayah number within the surah (1-based)")
    arabic_text = serializers.CharField(help_text="Original Arabic text of the ayah")
    translation = serializers.CharField(help_text="Translation of the ayah in the requested language")
    transliteration = serializers.CharField(help_text="Transliteration of the ayah in Latin script")
    image_url = serializers.URLField(
        allow_null=True, required=False, help_text="URL to the ayah image for visual matching/memory games"
    )


class PairingImageObjectSerializer(serializers.Serializer):
    """Serializer for image objects in unified pairing system."""

    image_url = serializers.URLField(help_text="URL to the ayah image")
    pair_id = serializers.CharField(help_text="Unique identifier for pairing with corresponding text object")


class PairingTextObjectSerializer(serializers.Serializer):
    """Serializer for text objects in unified pairing system."""

    arabic_text = serializers.CharField(help_text="Arabic text of the ayah")
    translation = serializers.CharField(help_text="Translation of the ayah in requested language")
    transliteration = serializers.CharField(help_text="Transliteration of the ayah in requested language")
    pair_id = serializers.CharField(help_text="Unique identifier for pairing with corresponding image object")
    ayah_number = serializers.IntegerField(help_text="Ayah number within the surah")


# Progress-related response schemas
class ProgressSummarySerializer(serializers.Serializer):
    """User progress summary serializer."""

    total_xp = serializers.IntegerField(help_text="Total experience points")
    current_level = serializers.IntegerField(help_text="Current user level")
    day_streak = serializers.IntegerField(help_text="Current day streak")
    league = serializers.CharField(allow_null=True, help_text="Current league name")


class SurahProgressSerializer(serializers.Serializer):
    """Surah progress information serializer."""

    surah_id = serializers.IntegerField(help_text="Surah ID")
    name = serializers.CharField(help_text="Surah name")
    experience_points = serializers.IntegerField(help_text="Experience points earned")
    completed_ayahs = serializers.IntegerField(help_text="Number of completed ayahs")
    total_ayahs = serializers.IntegerField(help_text="Total ayahs in the surah")
    is_unlocked = serializers.BooleanField(help_text="Whether the surah is unlocked")


class UnlockInfoSerializer(serializers.Serializer):
    """Unlock information serializer."""

    is_unlocked = serializers.BooleanField(help_text="Whether the next surah is unlocked")
    required_xp = serializers.IntegerField(
        allow_null=True, help_text="XP required to unlock (null if already unlocked)"
    )
    current_xp = serializers.IntegerField(help_text="Current XP for the surah")
    next_surah_id = serializers.IntegerField(allow_null=True, help_text="ID of the next surah to unlock")


# Social-related response schemas
class FollowRelationshipSerializer(serializers.Serializer):
    """Follow relationship serializer."""

    id = serializers.IntegerField(help_text="Relationship ID")
    follower = UserBasicInfoSerializer(help_text="User who is following")
    following = UserBasicInfoSerializer(help_text="User being followed")
    created_at = serializers.DateTimeField(help_text="When the follow relationship was created")


class LeaderboardEntrySerializer(serializers.Serializer):
    """Leaderboard entry serializer."""

    user = UserProfileSerializer(help_text="User information")
    rank = serializers.IntegerField(help_text="User's rank in the leaderboard")
    total_xp = serializers.IntegerField(help_text="User's total experience points")


# Ayah information serializer for bookmarks
class AyahInfoSerializer(serializers.Serializer):
    """Ayah information serializer for bookmark responses."""

    ayah_number = serializers.IntegerField(help_text="Ayah number within the surah")
    surah_id = serializers.IntegerField(help_text="Surah ID")
    image = serializers.URLField(allow_null=True, help_text="URL to ayah image")


# Bookmark-related response schemas
class BookmarkSerializer(serializers.Serializer):
    """Basic bookmark serializer."""

    id = serializers.IntegerField(help_text="Bookmark ID")
    surah_id = serializers.IntegerField(help_text="Surah ID")
    ayah_number = serializers.IntegerField(help_text="Ayah ID")
    created_at = serializers.DateTimeField(help_text="When the bookmark was created")


class EnhancedBookmarkSerializer(serializers.Serializer):
    """Enhanced bookmark serializer with multi-language content."""
    user_id = serializers.IntegerField(help_text="User ID")
    ayah = AyahInfoSerializer(help_text="Ayah information")
    surah_title = serializers.CharField(allow_null=True, help_text="Surah title in requested language")
    arabic_text = serializers.CharField(allow_null=True, help_text="Arabic text of the ayah")
    translation = serializers.CharField(allow_null=True, help_text="Translation in requested language")
    transliteration = serializers.CharField(allow_null=True, help_text="Transliteration in requested language")
    image_url = serializers.URLField(allow_null=True, help_text="URL to ayah image")
    is_completed = serializers.BooleanField(help_text="Whether the ayah is completed by the user")
    is_bookmarked = serializers.BooleanField(help_text="Whether the ayah is bookmarked by the user")
    created_at = serializers.DateTimeField(help_text="When the bookmark was created")


# Example response data classes for documentation
class ResponseExamples:
    """Example response data for API documentation."""

    @staticmethod
    def get_success_response_example():
        return {
            "success": True,
            "message": "Operation completed successfully",
            "data": {"example_field": "example_value"},
        }

    @staticmethod
    def get_error_response_example():
        return {"success": False, "message": "Operation failed", "errors": {"field_name": ["This field is required."]}}

    @staticmethod
    def get_auth_token_example():
        return {
            "success": True,
            "message": "Authentication successful",
            "data": {
                "token": "abcd1234567890efgh",
                "user": {
                    "id": 1,
                    "username": "john_doe",
                    "first_name": "John",
                    "last_name": "Doe",
                    "total_xp": 1500,
                    "current_level": 2,
                    "day_streak": 5,
                    "gender": "male",
                    "profile_photo": "https://example.com/photos/john_doe.jpg",
                },
            },
        }

    @staticmethod
    def get_bookmark_list_example():
        return {
            "success": True,
            "message": "User bookmarks retrieved successfully",
            "data": {
                "bookmarks": [
                    {
                        "id": 1,
                        "user_id": 123,
                        "ayah": {
                            "id": 1,
                            "ayah_number": 1,
                            "surah_id": 1,
                            "image": "https://example.com/ayah-images/1-1.png",
                        },
                        "surah_title": "Al-Fatihah",
                        "arabic_text": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
                        "translation": "In the name of Allah, the Most Gracious, the Most Merciful",
                        "transliteration": "Bismillahir-Rahmanir-Raheem",
                        "image_url": "https://example.com/ayah-images/1-1.png",
                        "is_completed": True,
                        "is_bookmarked": True,
                        "created_at": "2024-01-15T10:30:00Z",
                    }
                ],
                "count": 1,
                "language_info": {"requested_language": "en", "used_language": "en", "fallback_applied": False},
            },
        }

    @staticmethod
    def get_enhanced_profile_example():
        return {
            "success": True,
            "message": "Enhanced profile retrieved successfully",
            "data": {
                "id": 123,
                "username": "john_doe",
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "profile_photo": "https://example.com/profiles/john_doe.jpg",
                "date_joined": "2024-01-01T00:00:00Z",
                "total_xp": 1500,
                "current_level": 5,
                "day_streak": 7,
                "gender": "male",
                "social_summary": {
                    "followers_count": 25,
                    "following_count": 18,
                    "followers": [
                        {
                            "id": 124,
                            "username": "jane_smith",
                            "first_name": "Jane",
                            "last_name": "Smith",
                            "total_xp": 1200,
                            "current_level": 4,
                            "day_streak": 3,
                            "gender": "female",
                        }
                    ],
                    "following": [
                        {
                            "id": 125,
                            "username": "ahmed_ali",
                            "first_name": "Ahmed",
                            "last_name": "Ali",
                            "total_xp": 2000,
                            "current_level": 7,
                            "day_streak": 12,
                            "gender": "male",
                        }
                    ],
                },
                "league_position": {
                    "league": {
                        "id": 1,
                        "rank": "Gold",
                        "end_time": "2024-02-01T00:00:00Z",
                        "start_time": "2024-01-01T00:00:00Z",
                        "icon": "gold_trophy.png",
                        "name": "Champions League",
                    },
                    "user_rank": 3,
                    "total_participants": 50,
                },
                "bookmark_summary": {
                    "bookmarks_count": 12,
                    "recent_bookmarks": [
                        {
                            "id": 1,
                            "surah_id": 1,
                            "ayah_number": 1,
                            "surah_title": "Al-Fatihah",
                            "arabic_text": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
                            "created_at": "2024-01-15T10:30:00Z",
                        }
                    ],
                },
            },
        }

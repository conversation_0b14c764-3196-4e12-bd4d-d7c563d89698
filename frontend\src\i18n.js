import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import useLanguageStore from "src/hooks/languageStore";
const resources = {
  en: {
    translation: {
      home_left_menu: "home",
      study_left_menu: "study",
      leaderboards_left_menu: "leaderboards",
      makhraj_left_menu: "makhraj",
      karaoke_left_menu: "karaoke",
      questions_left_menu: "questions",
      more_left_menu: "more",
      add_sbs: "Add",
      shuffling_description:
        "Practice your memory by restoring the correct order of the ayahs.",
      memory_description: "Memorize and match pairs of ayahs.",
      audio_description:
        "Listen and choose the correct ayah that matches the audio.",
      matching_description: "Match the ayah with its translation.",
      shuffling: "Shuffling",
      memory: "Memory",
      audio: "Audio",
      matching: "Matching",
      gaps: "Gaps",
      game_description_not_found: "Game description not found",
      select_number_of_ayahs: "Number of ayahs: ",
      full_card: "full cards",
      flash_card: "flash cards",
      step_by_step_btn: "Step-by-Step",
      start: "Start",
      stop: "Stop",
      practise_with_games_btn: "Practice with games",
      show_more_btn: "show more",
      daily_quests: "Daily Quests",
      daily_quest1_demo: "Memorize 1 ayah",
      daily_quest2_demo: "Spend 10 minutes learning",
      current_level_btn: "Current Level",
      landing_main_info1: "Master the <span>Quran</span>  ",
      landing_main_info2: "<span>Easily</span> and <span>Enjoyably</span>",
      landing_main_paragraph:
        "A guide to interactive Quran study that is effective and inspiring. Become a Hafiz by dedicating just 15 minutes a day.",
      get_started_btn: "Get Started",
      ask_imam_title: "Ask the Imam",
      ask_imam_description: "You can ask and get an instant answer",
      find_friends_title: "Find Friends",
      find_friends_description:
        "We will help you find brothers and sisters in faith",
      demo_landing_statistic: "A guide to interactive Quran study that",
      landing_quote_text:
        "Each person will only have what they endeavoured towards",
      landing_quote_info: "Quran (53:39)",
      landing_home_btn: "home",
      landing_methods_btn: "methods",
      landing_about_us_btn: "about us",
      landing_contacts_btn: "contacts",
      profile_btn: "Profile",
      logout_btn: "Logout",
      my_profile_title: "My Profile",
      progress_title: "Progress",
      statistics_title: "Statistics",
      bookmarks_title: "Bookmarks",
      profile_page_following: "Following",
      profile_page_followers: "Followers",
      game_completed: "Game Completed!",
      try_again: "Try Again",
      back_to_games: "Back to Games",
      joined_march_2024: "Joined March 2024",
      save_text: "Save",
      cancel_text: "Cancel",
      upload_text: "Upload",
      day_streak: "Day streak",
      weekly_goals: "Weekly goals",
      total_points: "Total points",
    },
  },
  ru: {
    translation: {
      home_left_menu: "домой",
      study_left_menu: "учить",
      leaderboards_left_menu: "лидеры",
      makhraj_left_menu: "махрадж",
      karaoke_left_menu: "караоке",
      questions_left_menu: "вопросы",
      more_left_menu: "еще",
      add_sbs: "Добавить",
      shuffling_description: "Расставь аяты в правильном порядке",
      memory_description: "Запомни и сопоставь пары аятов",
      audio_description:
        "Послушай и выбери правильный аят, соответствующий аудио",
      matching_description: "Сопоставь аят с его переводом",
      shuffling: "Восстановление порядка",
      memory: "Найди пару",
      audio: "Аудио",
      matching: "Сопоставление",
      gaps: "Пропуски",
      game_description_not_found: "Описание не найдено",
      select_number_of_ayahs: "Количество аятов: ",
      full_card: "целиком",
      flash_card: "флеш",
      step_by_step_btn: "Учить шаг за шагом",
      start: "Начать",
      stop: "Остановить",
      practise_with_games_btn: "Отработать в играх",
      show_more_btn: "показать больше",
      daily_quests: "Задания",
      daily_quest1_demo: "Запомнить 1 аят",
      daily_quest2_demo: "Потратить 10 минут на изучение",
      current_level_btn: "Текущий уровень",
      landing_main_info1: "Изучай <span>Коран</span> ",
      landing_main_info2: "<span>Легко</span> и <span>Удобно</span>",
      landing_main_paragraph:
        "Подробное руководство по изучению Корана, эффективное и вдохновляющее. Стань Хафизом всего за 15 минут в день.",
      get_started_btn: "Начать",
      ask_imam_title: "Спросите Имама",
      ask_imam_description: "Вы можете спросить и получить ответ мгновенно",
      find_friends_title: "Найдите друзей",
      find_friends_description: "Мы поможем вам найти братьев и сестер в вере",
      demo_landing_statistic:
        "Более 1000 человек уже используют наше приложение",
      landing_quote_text: "Человек получит только то, к чему он стремился.",
      landing_quote_info: "Коран (53:39)",
      landing_home_btn: "домой",
      landing_methods_btn: "методы",
      landing_about_us_btn: "о нас",
      landing_contacts_btn: "контакты",
      profile_btn: "Профиль",
      logout_btn: "Выход",
      my_profile_title: "Мой профиль",
      progress_title: "Прогресс",
      statistics_title: "Статистика",
      bookmarks_title: "Закладки",
      profile_page_following: "Подписки",
      profile_page_followers: "Подписчики",
      game_completed: "Игра пройдена!",
      try_again: "Попробуйте снова",
      back_to_games: "Вернуться к играм",
      joined_march_2024: "Присоединился 3 марта 2024",
      save_text: "Сохранить",
      cancel_text: "Отмена",
      upload_text: "Загрузить",
      day_streak: "Дневная серия",
      weekly_goals: "Еженедельные цели",
      total_points: "Всего очков",
    },
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: "en",
    interpolation: {
      escapeValue: false,
    },
  });

const { language } = useLanguageStore.getState();
i18n.changeLanguage(language);

export default i18n;

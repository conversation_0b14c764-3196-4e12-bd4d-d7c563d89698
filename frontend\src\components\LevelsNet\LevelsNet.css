.middle-image-container {
  margin-top: 15px;
  display: flex;
  justify-content: center;
  align-content: center;
  overflow: hidden;
}

.middle-image-container img {
  width: 840px;
  height: auto;
}

.middle-image-container svg {
  width: 840px;
  height: auto;
  transform-origin: center center;
  transition: transform 0.1s;
}


/* Стиль для элементов с классом level-done при наведении */
.middle-image-container path.level-done:hover,
.middle-image-container circle.level-done:hover {
  cursor: pointer;
  fill: #47af51; /* Ярко-зеленый */
  stroke: #47af51;
}

/* Стиль для элементов без класса level-done при наведении */
.middle-image-container path:not(.level-done):hover,
.middle-image-container circle:not(.level-done):hover {
  cursor: pointer;
  fill: #828282; /* Серый */
  stroke: #828282;
}

/* Основной стиль для элементов с классом level-done */
.level-done {
  fill: #97CB9C;
  stroke: #97CB9C;
}
"""
Clean architecture views
These views follow the best practices outlined in the article:
- Views only handle HTTP request/response concerns
- Business logic is delegated to the service layer
- Data access is handled through repositories
- Transport objects are used for API responses
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.exceptions import NotFound
from rest_framework.permissions import IsAuthenticated
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from . import services
from . import serializers
from core.utils import serialize_dataclass, create_success_response, create_error_response, serialize_dataclass_list
from .response_schemas import (
    SurahInfoResponseSchemas,
    AyahTextResponseSchemas,
    AvailableReadersResponseSchemas,
    AvailableLanguagesResponseSchemas,
    AllSurahsPreviewResponseSchemas,
)


class SurahInfoView(APIView):
    """View for Surah information operations."""

    @swagger_auto_schema(
        operation_description="Get comprehensive information about a specific Surah including ayahs with audio recordings",
        request_body=serializers.SurahInfoRequestSerializer,
        manual_parameters=[
            openapi.Parameter(
                "Reader-Slug",
                openapi.IN_HEADER,
                description="Preferred audio reader slug (e.g., 'alafasy', 'sudais'). Defaults to 'alafasy' if not provided.",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "Language-Code",
                openapi.IN_HEADER,
                description="Preferred language for translations and transliterations (e.g., 'en', 'id', 'ar'). Defaults to 'en' if not provided.",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                "Surah information with audio recordings retrieved successfully", SurahInfoResponseSchemas.success_200
            ),
            400: openapi.Response("Invalid request data", SurahInfoResponseSchemas.error_400),
            404: openapi.Response("Surah not found", SurahInfoResponseSchemas.error_404),
            500: openapi.Response("Internal server error", SurahInfoResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get Surah information."""
        serializer = serializers.SurahInfoRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]

            # Get reader preference from header
            reader_slug = request.headers.get("Reader-Slug", "alafasy")  # Default to alafasy

            # Get language preference from headers
            requested_language = services.LanguageService.get_language_from_header(request)

            surah_info = services.QuranService.get_surah_info(surah_id)
            # Get all ayahs with language support and absolute image URLs
            ayahs, language_metadata = services.LanguageService.get_surah_ayahs_with_language(
                surah_id, requested_language, request
            )

            # Enhance ayahs with audio URLs for the specified reader
            enhanced_ayahs = []
            for ayah in ayahs:
                ayah_dict = serialize_dataclass(ayah)

                # Try to get audio recording for this ayah and reader
                try:
                    audio_recording = services.AudioRecordingService.get_ayah_audio_by_reader(
                        surah_id, ayah.ayah_id, reader_slug
                    )
                    ayah_dict["audio_url"] = audio_recording.audio_url
                except NotFound:
                    # If specified reader not found, try default reader (alafasy)
                    if reader_slug != "alafasy":
                        try:
                            audio_recording = services.AudioRecordingService.get_ayah_audio_by_reader(
                                surah_id, ayah.ayah_id, "alafasy"
                            )
                            ayah_dict["audio_url"] = audio_recording.audio_url
                        except NotFound:
                            ayah_dict["audio_url"] = None
                    else:
                        ayah_dict["audio_url"] = None

                enhanced_ayahs.append(ayah_dict)

            # modified response with serialized ayahs, surah_info, and language metadata
            response_data = {
                "surah_info": serialize_dataclass(surah_info),
                "ayahs": enhanced_ayahs,
                "language_info": serialize_dataclass(language_metadata),
            }

            return Response(
                create_success_response(
                    response_data,
                    "Surah information with audio recordings retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve Surah information: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AyahTextView(APIView):
    """View for Ayah text operations."""

    @swagger_auto_schema(
        operation_description="Retrieve complete text data of a specific Ayah including Arabic text, translation, and transliteration in the requested language",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["surah_id", "ayah_id"],
            properties={
                "surah_id": openapi.Schema(type=openapi.TYPE_INTEGER, description="ID of the Surah"),
                "ayah_id": openapi.Schema(type=openapi.TYPE_INTEGER, description="ID of the Ayah within the Surah"),
            },
        ),
        manual_parameters=[
            openapi.Parameter(
                "Language-Code",
                openapi.IN_HEADER,
                description="Preferred language for translations and transliterations (e.g., 'en', 'id', 'ar'). Defaults to 'en' if not provided.",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                "Successfully retrieved Ayah text with translation and transliteration",
                AyahTextResponseSchemas.success_200,
            ),
            400: openapi.Response("Missing required parameters", AyahTextResponseSchemas.error_400),
            404: openapi.Response("Ayah not found", AyahTextResponseSchemas.error_404),
            500: openapi.Response("Internal server error", AyahTextResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Retrieve the text of a specific Ayah."""
        surah_id = request.data.get("surah_id")
        ayah_id = request.data.get("ayah_id")

        if not surah_id or not ayah_id:
            return Response(create_error_response("Missing surah_id or ayah_id"), status=status.HTTP_400_BAD_REQUEST)

        try:
            surah_id = int(surah_id)
            ayah_id = int(ayah_id)
        except (ValueError, TypeError):
            return Response(
                create_error_response("Invalid surah_id or ayah_id format"), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get language preference from headers
            requested_language = services.LanguageService.get_language_from_header(request)

            # Get ayah text with language support
            ayah_text_data, language_metadata = services.LanguageService.get_ayah_text_with_language(
                surah_id, ayah_id, requested_language
            )

            from django.conf import settings
            import json

            with open(f"{settings.BASE_DIR}/quran.json", "r", encoding="utf-8") as f:
                quran_data = json.load(f)

            ayah_global_number = quran_data["data"][surah_id - 1]["verses"][ayah_id - 1]["number"]["inQuran"]

            response_data = {
                "surah_id": surah_id,
                "ayah_id": ayah_id,
                "arabic_text": ayah_text_data.arabic_text,
                "transliteration": ayah_text_data.transliteration,
                "translation": ayah_text_data.translation,
                "globalNumber": ayah_global_number,
                "language_info": serialize_dataclass(language_metadata),
            }

            return Response(
                create_success_response(response_data, "Ayah text retrieved successfully"), status=status.HTTP_200_OK
            )
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve Ayah text: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AvailableReadersView(APIView):
    """View for retrieving available audio readers/reciters."""

    @swagger_auto_schema(
        operation_description="Get list of all available audio readers/reciters in the system",
        responses={
            200: openapi.Response(
                "Available readers retrieved successfully", AvailableReadersResponseSchemas.success_200
            ),
            500: openapi.Response("Internal server error", AvailableReadersResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get list of all available audio readers."""
        try:
            # Get all available readers from the service
            readers = services.AudioRecordingService.get_all_available_readers()

            # Prepare response data
            response_data = {"readers": serialize_dataclass_list(readers)}

            return Response(
                create_success_response(
                    response_data,
                    "Available readers retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve available readers: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AvailableLanguagesView(APIView):
    """View for retrieving available languages for translations and transliterations."""

    @swagger_auto_schema(
        operation_description="Get list of all available languages that have translations and/or transliterations in the system",
        responses={
            200: openapi.Response(
                "Available languages retrieved successfully", AvailableLanguagesResponseSchemas.success_200
            ),
            500: openapi.Response("Internal server error", AvailableLanguagesResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get list of all available languages."""
        try:
            # Get all available languages from the service
            languages = services.LanguageService.get_available_languages()

            # Prepare response data
            response_data = {"languages": serialize_dataclass_list(languages)}

            return Response(
                create_success_response(
                    response_data,
                    "Available languages retrieved successfully",
                ),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve available languages: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SurahPreviewView(APIView):
    """View for getting preview data for all 114 surahs with first 3 ayah images each."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Get preview data for all 114 surahs with first 3 ayah images each and multi-language support.",
        request_body=serializers.SurahPreviewRequestSerializer,
        manual_parameters=[
            openapi.Parameter(
                "Language-Code",
                openapi.IN_HEADER,
                description="Preferred language for surah titles (e.g., 'en', 'id', 'ar'). Defaults to 'en' if not provided.",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                "All surahs preview retrieved successfully",
                AllSurahsPreviewResponseSchemas.success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "All surahs preview retrieved successfully",
                        "data": {
                            "surahs": [
                                {
                                    "surah_id": 1,
                                    "surah_title": "Al-Fatihah",
                                    "arabic_name": "الفاتحة",
                                    "ayah_previews": [
                                        {"ayah_number": 1, "image_url": "https://example.com/images/001_001.png"},
                                        {"ayah_number": 2, "image_url": "https://example.com/images/001_002.png"},
                                        {"ayah_number": 3, "image_url": "https://example.com/images/001_003.png"},
                                    ],
                                    "total_ayahs_in_surah": 7,
                                    "returned_ayahs_count": 3,
                                    "is_pre_bismillah": False,
                                    "is_meccan": True,
                                    "is_medinian": False,
                                    "position_in_order": 1,
                                    "learning_position": 1,
                                },
                                {
                                    "surah_id": 2,
                                    "surah_title": "Al-Baqarah",
                                    "arabic_name": "البقرة",
                                    "ayah_previews": [
                                        {"ayah_number": 1, "image_url": "https://example.com/images/002_001.png"},
                                        {"ayah_number": 2, "image_url": "https://example.com/images/002_002.png"},
                                        {"ayah_number": 3, "image_url": "https://example.com/images/002_003.png"},
                                    ],
                                    "total_ayahs_in_surah": 286,
                                    "returned_ayahs_count": 3,
                                    "is_pre_bismillah": True,
                                    "is_meccan": False,
                                    "is_medinian": True,
                                    "position_in_order": 2,
                                    "learning_position": 2,
                                },
                                "... (112 more surahs)",
                            ],
                            "total_surahs_count": 114,
                            "ordering": "pedagogical",
                            "language_info": {
                                "requested_language": "en",
                                "used_language": "en",
                                "fallback_applied": False,
                            },
                        },
                    }
                },
            ),
            401: openapi.Response("Authentication required", AllSurahsPreviewResponseSchemas.error_401),
            500: openapi.Response("Internal server error", AllSurahsPreviewResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Get preview data for all 114 surahs with first 3 ayah images each and multi-language support."""
        try:
            # Parse request data for ordering preference
            serializer = serializers.SurahPreviewRequestSerializer(data=request.data)
            if serializer.is_valid():
                ordering = serializer.validated_data.get("ordering", "pedagogical")
            else:
                ordering = "pedagogical"  # Default fallback

            # Get language from header
            requested_language = request.headers.get("Language-Code", "en")

            # Get all surahs preview from service with specified ordering and absolute image URLs
            use_pedagogical_order = ordering == "pedagogical"
            surah_previews, language_metadata = services.QuranService.get_all_surahs_preview(
                requested_language, use_pedagogical_order=use_pedagogical_order, request=request
            )

            # Enhance surah previews with learning position information
            enhanced_surahs = []
            for i, surah_preview in enumerate(surah_previews):
                surah_dict = serialize_dataclass(surah_preview)
                # Add learning position (1-based index in pedagogical order)
                surah_dict["learning_position"] = i + 1
                # Add traditional surah number for reference
                # surah_dict["traditional_surah_number"] = surah_preview.surah_id
                enhanced_surahs.append(surah_dict)

            # Prepare response data
            response_data = {
                "surahs": enhanced_surahs,
                "total_surahs_count": len(surah_previews),
                "ordering": ordering,  # Indicate the ordering used
                "language_info": serialize_dataclass(language_metadata),
            }

            return Response(
                create_success_response(
                    response_data, f"All surahs preview retrieved successfully in {ordering} order"
                ),
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                create_error_response(f"Failed to retrieve all surahs preview: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

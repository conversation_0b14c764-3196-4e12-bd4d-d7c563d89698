from django.urls import path
from .views import AyahShuffleView, AyahMatchingView, AyahMemoryView, AudioGameView, GapsGameView
from .views import WordAudioView, ArabicLetterAudioRecognitionView, ArabicLetterTranscriptionMatchingView

urlpatterns = [
    path("shuffling/", AyahShuffleView.as_view(), name="ayah-shuffle"),
    path("matching/", AyahMatchingView.as_view(), name="ayah-matching"),
    path("memory/", AyahMemoryView.as_view(), name="ayah-memory"),
    path("audio/", AudioGameView.as_view(), name="audio-game"),
    path("gaps/", GapsGameView.as_view(), name="gaps-game"),
    path("word_audio/", WordAudioView.as_view(), name="word-audio"),
    # Arabic Letter Learning Games
    path(
        "arabic-letters/audio-recognition/",
        ArabicLetterAudioRecognitionView.as_view(),
        name="arabic-letter-audio-recognition",
    ),
    path(
        "arabic-letters/transcription-matching/",
        ArabicLetterTranscriptionMatchingView.as_view(),
        name="arabic-letter-transcription-matching",
    ),
]

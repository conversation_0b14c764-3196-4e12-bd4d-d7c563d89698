"""
Response schema serializers for the games app.

This module provides response serializers for all game-related endpoints
to ensure comprehensive API documentation in Swagger/OpenAPI.

UNIFIED PAIRING SYSTEM UPDATE:
Memory and matching games now use a unified pairing system with separate 'images' and 'texts' arrays
that are connected via 'pair_id' fields. This provides flexible pairing for frontend implementation.

Current structure:
- Both games: 'images' array with PairingImageObject containing image_url and pair_id
- Both games: 'texts' array with PairingTextObject containing Arabic text, translation,
  transliteration, pair_id, and ayah/surah information

Previous structure (deprecated):
- Unified structure: 'ayahs' array with complete ayah data objects
- Earlier structure: separate 'text_items' and 'image_items' arrays

The pair_id field serves as the unique identifier that connects matching image and text objects.
"""

from rest_framework import serializers
from core.response_schemas import (
    BaseSuccessResponseSerializer,
    BaseErrorResponseSerializer,
    GameMetadataSerializer,
    GamePhraseSerializer,
    GameLevelSerializer,
    AyahBasicInfoSerializer,
    LanguageMetadataSerializer,
    # UnifiedAyahDataSerializer,
    PairingImageObjectSerializer,
    PairingTextObjectSerializer,
)


# Enhanced Ayah serializer for games with additional fields
class GameAyahSerializer(AyahBasicInfoSerializer):
    """Enhanced Ayah serializer for game responses."""

    audio_url = serializers.URLField(allow_null=True, help_text="URL to audio file for this ayah")
    image_url = serializers.URLField(allow_null=True, help_text="URL to image for this ayah")


# Shuffling Game Response Schemas
class ShufflingGameDataSerializer(serializers.Serializer):
    """Data structure for shuffling game response."""

    metadata = GameMetadataSerializer(help_text="Game metadata")
    sentence = serializers.CharField(help_text="Sentence with numbered placeholders for ayah arrangement")
    phrases = GamePhraseSerializer(many=True, help_text="Shuffled ayah phrases to be arranged")
    correct_sequence = serializers.ListField(child=serializers.CharField(), help_text="Correct order of phrase IDs")
    language_info = LanguageMetadataSerializer(required=False, help_text="Language selection metadata")


class ShufflingGameSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful shuffling game generation."""

    data = ShufflingGameDataSerializer(help_text="Shuffling game data")


# Matching Game Response Schemas
class MatchingGameDataSerializer(serializers.Serializer):
    """
    Data structure for matching game response with unified pairing system.

    This uses the new unified pairing system with separate image and text arrays
    that are connected via pair_id for flexible matching game implementation.
    """

    metadata = GameMetadataSerializer(help_text="Game metadata")
    images = PairingImageObjectSerializer(many=True, help_text="Array of image objects with pair_id for matching")
    texts = PairingTextObjectSerializer(
        many=True, help_text="Array of text objects with pair_id for matching with corresponding images"
    )
    language_info = LanguageMetadataSerializer(required=False, help_text="Language selection metadata")


class MatchingGameSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful matching game generation with unified pairing system."""

    data = MatchingGameDataSerializer(help_text="Matching game data with unified pairing system")


# Memory Game Response Schemas
class MemoryGameDataSerializer(serializers.Serializer):
    """
    Data structure for memory game response with unified pairing system.

    This uses the new unified pairing system with separate image and text arrays
    that are connected via pair_id for flexible memory game implementation.
    """

    metadata = GameMetadataSerializer(help_text="Game metadata")
    images = PairingImageObjectSerializer(
        many=True, help_text="Array of image objects with pair_id for memory matching"
    )
    texts = PairingTextObjectSerializer(
        many=True, help_text="Array of text objects with pair_id for memory matching with corresponding images"
    )
    language_info = LanguageMetadataSerializer(required=False, help_text="Language selection metadata")


class MemoryGameSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful memory game generation with unified pairing system."""

    data = MemoryGameDataSerializer(help_text="Memory game data with unified pairing system")


# Audio Game Response Schemas
class AudioGameDataSerializer(serializers.Serializer):
    """Data structure for audio game response."""

    metadata = GameMetadataSerializer(help_text="Game metadata")
    levels = GameLevelSerializer(many=True, help_text="Game levels with audio challenges")
    language_info = LanguageMetadataSerializer(required=False, help_text="Language selection metadata")


class AudioGameSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful audio game generation."""

    data = AudioGameDataSerializer(help_text="Audio game data")


# Gaps Game Response Schemas
class GapsGameDataSerializer(serializers.Serializer):
    """Data structure for gaps/word reconstruction game response."""

    metadata = GameMetadataSerializer(help_text="Game metadata")
    levels = GameLevelSerializer(many=True, help_text="Game levels with complete word reconstruction challenges")
    language_info = LanguageMetadataSerializer(required=False, help_text="Language selection metadata")


class GapsGameSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful gaps game generation."""

    data = GapsGameDataSerializer(help_text="Word reconstruction game data")


# Word Audio Response Schemas
class WordAudioDataSerializer(serializers.Serializer):
    """Data structure for word audio response."""

    audio_urls = serializers.ListField(child=serializers.URLField(), help_text="List of URLs to the word audio files")


class WordAudioSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful word audio URL generation."""

    data = WordAudioDataSerializer(help_text="Word audio URLs")


# Error Response Schemas
class GameValidationErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for game validation errors."""

    pass


class GameNotFoundErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for game not found errors."""

    pass


class GameForbiddenErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for game forbidden errors (surah not unlocked)."""

    pass


class GameInternalServerErrorResponseSerializer(BaseErrorResponseSerializer):
    """Response schema for game internal server errors."""

    pass


# Endpoint-specific response schema collections
class ShufflingGameResponseSchemas:
    """Response schemas for shuffling game endpoint."""

    success_200 = ShufflingGameSuccessResponseSerializer
    error_400 = GameValidationErrorResponseSerializer
    error_403 = GameForbiddenErrorResponseSerializer
    error_404 = GameNotFoundErrorResponseSerializer
    error_500 = GameInternalServerErrorResponseSerializer


class MatchingGameResponseSchemas:
    """Response schemas for matching game endpoint."""

    success_200 = MatchingGameSuccessResponseSerializer
    error_400 = GameValidationErrorResponseSerializer
    error_403 = GameForbiddenErrorResponseSerializer
    error_404 = GameNotFoundErrorResponseSerializer
    error_500 = GameInternalServerErrorResponseSerializer


class MemoryGameResponseSchemas:
    """Response schemas for memory game endpoint."""

    success_200 = MemoryGameSuccessResponseSerializer
    error_400 = GameValidationErrorResponseSerializer
    error_403 = GameForbiddenErrorResponseSerializer
    error_404 = GameNotFoundErrorResponseSerializer
    error_500 = GameInternalServerErrorResponseSerializer


class AudioGameResponseSchemas:
    """Response schemas for audio game endpoint."""

    success_200 = AudioGameSuccessResponseSerializer
    error_400 = GameValidationErrorResponseSerializer
    error_403 = GameForbiddenErrorResponseSerializer
    error_404 = GameNotFoundErrorResponseSerializer
    error_500 = GameInternalServerErrorResponseSerializer


class GapsGameResponseSchemas:
    """Response schemas for gaps/word reconstruction game endpoint."""

    success_200 = GapsGameSuccessResponseSerializer
    error_400 = GameValidationErrorResponseSerializer
    error_403 = GameForbiddenErrorResponseSerializer
    error_404 = GameNotFoundErrorResponseSerializer
    error_500 = GameInternalServerErrorResponseSerializer


class WordAudioResponseSchemas:
    """Response schemas for word audio endpoint."""

    success_200 = WordAudioSuccessResponseSerializer
    error_400 = GameValidationErrorResponseSerializer
    error_500 = GameInternalServerErrorResponseSerializer


# Example response data for documentation
class GameResponseExamples:
    """Example response data for game endpoints."""

    @staticmethod
    def get_shuffling_game_example():
        return {
            "success": True,
            "message": "Shuffling game generated successfully",
            "data": {
                "metadata": {
                    "surah_id": 1,
                    "surah_name": "Al-Fatihah",
                    "start_ayah_number": 1,
                    "ayah_count": 2,
                    "total_ayahs_in_surah": 7,
                },
                "sentence": "Arrange the phrases: {1} {2} {3}",
                "phrases": [{"id": "phrase_1", "text": "بِسْمِ اللَّهِ"}, {"id": "phrase_2", "text": "الرَّحْمَٰنِ الرَّحِيمِ"}],
                "correct_sequence": ["phrase_1", "phrase_2"],
                "language_info": {
                    "requested_language": "en",
                    "used_language": "en",
                    "fallback_applied": False,
                    "available_languages": ["en", "id", "ar"],
                },
            },
        }

    @staticmethod
    def get_matching_game_example():
        return {
            "success": True,
            "message": "Matching game generated successfully",
            "data": {
                "metadata": {
                    "surah_id": 1,
                    "surah_name": "Al-Fatihah",
                    "start_ayah_number": 1,
                    "ayah_count": 2,
                    "total_ayahs_in_surah": 7,
                },
                "images": [
                    {
                        "image_url": "https://example.com/images/001_001.png",
                        "pair_id": "ayah_1_1",
                    },
                    {
                        "image_url": "https://example.com/images/001_002.png",
                        "pair_id": "ayah_1_2",
                    },
                ],
                "texts": [
                    {
                        "arabic_text": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
                        "translation": "In the name of Allah, the Most Gracious, the Most Merciful",
                        "transliteration": "Bismillahir-Rahmanir-Raheem",
                        "pair_id": "ayah_1_1",
                        "ayah_number": 1,
                        "surah_number": 1,
                        "surah_name": "Al-Fatihah",
                    },
                    {
                        "arabic_text": "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ",
                        "translation": "All praise is due to Allah, Lord of the worlds",
                        "transliteration": "Alhamdu lillahi rabbil alameen",
                        "pair_id": "ayah_1_2",
                        "ayah_number": 2,
                        "surah_number": 1,
                        "surah_name": "Al-Fatihah",
                    },
                ],
                "language_info": {
                    "requested_language": "en",
                    "used_language": "en",
                    "fallback_applied": False,
                    "available_languages": ["en", "id", "ar"],
                },
            },
        }

    @staticmethod
    def get_memory_game_example():
        return {
            "success": True,
            "message": "Memory game generated successfully",
            "data": {
                "metadata": {
                    "surah_id": 1,
                    "surah_name": "Al-Fatihah",
                    "start_ayah_number": 1,
                    "ayah_count": 2,
                    "total_ayahs_in_surah": 7,
                },
                "images": [
                    {
                        "image_url": "https://example.com/images/001_001.png",
                        "pair_id": "ayah_1_1",
                    },
                    {
                        "image_url": "https://example.com/images/001_002.png",
                        "pair_id": "ayah_1_2",
                    },
                ],
                "texts": [
                    {
                        "arabic_text": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
                        "translation": "In the name of Allah, the Most Gracious, the Most Merciful",
                        "transliteration": "Bismillahir-Rahmanir-Raheem",
                        "pair_id": "ayah_1_1",
                        "ayah_number": 1,
                        "surah_number": 1,
                        "surah_name": "Al-Fatihah",
                    },
                    {
                        "arabic_text": "الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ",
                        "translation": "All praise is due to Allah, Lord of the worlds",
                        "transliteration": "Alhamdu lillahi rabbil alameen",
                        "pair_id": "ayah_1_2",
                        "ayah_number": 2,
                        "surah_number": 1,
                        "surah_name": "Al-Fatihah",
                    },
                ],
                "language_info": {
                    "requested_language": "en",
                    "used_language": "en",
                    "fallback_applied": False,
                    "available_languages": ["en", "id", "ar"],
                },
            },
        }

    @staticmethod
    def get_word_audio_example():
        return {
            "success": True,
            "message": "Audio URL generated successfully",
            "data": {
                "audio_urls": [
                    "https://audios.quranwbw.com/words/1/001_001_001.mp3",
                    "https://audios.quranwbw.com/words/1/001_001_002.mp3",
                    "https://audios.quranwbw.com/words/1/001_001_003.mp3",
                    "https://audios.quranwbw.com/words/1/001_001_004.mp3",
                ]
            },
        }


# Arabic Letter Game Response Schemas
class ArabicLetterGameMetadataSerializer(serializers.Serializer):
    """Serializer for Arabic letter game metadata."""

    letter_id = serializers.IntegerField(help_text="Target Arabic letter ID")
    letter_title = serializers.CharField(help_text="Target Arabic letter character")
    letter_transcription = serializers.CharField(help_text="Target letter transcription")
    game_type = serializers.CharField(help_text="Type of game (audio_recognition, transcription_matching)")
    total_options = serializers.IntegerField(help_text="Total number of options in the game")


class TransliterationOptionSerializer(serializers.Serializer):
    """Serializer for transliteration options in audio recognition game."""

    id = serializers.CharField(help_text="Option ID")
    transliteration_text = serializers.CharField(help_text="Transliteration text")
    is_correct = serializers.BooleanField(help_text="Whether this is the correct option")


class LetterOptionSerializer(serializers.Serializer):
    """Serializer for transcription matching game options."""

    id = serializers.CharField(help_text="Option ID")
    letter_title = serializers.CharField(help_text="Arabic letter character")
    letter_id = serializers.IntegerField(help_text="Arabic letter ID")
    is_correct = serializers.BooleanField(help_text="Whether this is the correct option")


class AudioRecognitionGameRequestSerializer(serializers.Serializer):
    """Request serializer for audio recognition game."""

    letter_id = serializers.IntegerField(help_text="Arabic letter ID to generate game for")


class TranscriptionMatchingGameRequestSerializer(serializers.Serializer):
    """Request serializer for transcription matching game."""

    letter_id = serializers.IntegerField(help_text="Arabic letter ID to generate game for")


class AudioRecognitionGameDataSerializer(serializers.Serializer):
    """Data structure for audio recognition game response."""

    metadata = ArabicLetterGameMetadataSerializer(help_text="Game metadata")
    target_letter = serializers.CharField(help_text="Target Arabic letter character")
    target_audio_url = serializers.URLField(help_text="Target letter audio pronunciation URL")
    transliteration_options = TransliterationOptionSerializer(many=True, help_text="Transliteration options")
    correct_transliteration = serializers.CharField(help_text="Correct transliteration text")


class TranscriptionMatchingGameDataSerializer(serializers.Serializer):
    """Data structure for transcription matching game response."""

    metadata = ArabicLetterGameMetadataSerializer(help_text="Game metadata")
    target_transcription = serializers.CharField(help_text="Target transcription to match")
    letter_options = LetterOptionSerializer(many=True, help_text="Arabic letter options")
    correct_letter_id = serializers.IntegerField(help_text="Correct letter ID")


class AudioRecognitionGameSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful audio recognition game generation."""

    data = AudioRecognitionGameDataSerializer(help_text="Audio recognition game data")


class TranscriptionMatchingGameSuccessResponseSerializer(BaseSuccessResponseSerializer):
    """Response schema for successful transcription matching game generation."""

    data = TranscriptionMatchingGameDataSerializer(help_text="Transcription matching game data")


# Response Schema Collections
class AudioRecognitionGameResponseSchemas:
    """Response schemas for audio recognition game endpoint."""

    success_200 = AudioRecognitionGameSuccessResponseSerializer
    error_400 = BaseErrorResponseSerializer
    error_404 = BaseErrorResponseSerializer
    error_500 = BaseErrorResponseSerializer


class TranscriptionMatchingGameResponseSchemas:
    """Response schemas for transcription matching game endpoint."""

    success_200 = TranscriptionMatchingGameSuccessResponseSerializer
    error_400 = BaseErrorResponseSerializer
    error_404 = BaseErrorResponseSerializer
    error_500 = BaseErrorResponseSerializer

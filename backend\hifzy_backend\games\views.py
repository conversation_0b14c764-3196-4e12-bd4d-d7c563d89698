"""
Clean architecture views for the games app.

These views follow the best practices outlined in the article:
- Views only handle HTTP request/response concerns
- Business logic is delegated to the service layer
- Data access is handled through repositories
- Transport objects are used for API responses
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.exceptions import ValidationError, NotFound
from rest_framework.permissions import IsAuthenticated
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from progress.services import SurahProgressionService

from . import services
from . import serializers
from core.utils import create_success_response, create_error_response, serialize_dataclass
from quran.services import LanguageService
from .response_schemas import (
    ShufflingGameResponseSchemas,
    MatchingGameResponseSchemas,
    MemoryGameResponseSchemas,
    AudioGameResponseSchemas,
    GapsGameResponseSchemas,
    WordAudioResponseSchemas,
    AudioRecognitionGameRequestSerializer,
    TranscriptionMatchingGameRequestSerializer,
    AudioRecognitionGameResponseSchemas,
    TranscriptionMatchingGameResponseSchemas,
)


class AyahShuffleView(APIView):
    """Enhanced view for Ayah shuffling game with flexible ayah selection."""

    @swagger_auto_schema(
        operation_description="Generate a shuffling game for specified Ayah range with direct game data structure and multi-language support",
        request_body=serializers.GameRequestSerializer,
        manual_parameters=[
            openapi.Parameter(
                "Language-Code",
                openapi.IN_HEADER,
                description="Preferred language for translations and transliterations (e.g., 'en', 'id', 'ar'). Defaults to 'en' if not provided.",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response("Shuffling game generated successfully", ShufflingGameResponseSchemas.success_200),
            400: openapi.Response("Invalid request data", ShufflingGameResponseSchemas.error_400),
            403: openapi.Response("Surah not unlocked for user", ShufflingGameResponseSchemas.error_403),
            404: openapi.Response("Surah not found", ShufflingGameResponseSchemas.error_404),
            500: openapi.Response("Internal server error", ShufflingGameResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Generate enhanced shuffling game with flexible ayah selection."""
        serializer = serializers.GameRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]
            start_ayah_number = serializer.validated_data["start_ayah_number"]
            count = serializer.validated_data["count"]

            # Get language preference from headers
            requested_language = LanguageService.get_language_from_header(request)

            # Validate Surah access for authenticated users
            if hasattr(request, "user") and request.user.is_authenticated:
                if not SurahProgressionService.validate_surah_access(request.user.id, surah_id):
                    return Response(
                        create_error_response(f"Surah {surah_id} is not unlocked for this user"),
                        status=status.HTTP_403_FORBIDDEN,
                    )

            # Generate enhanced shuffling game with language support
            game_response, language_metadata = services.GameService.generate_shuffling_game_enhanced(
                surah_id=surah_id, start_ayah_number=start_ayah_number, count=count, language=requested_language
            )

            # Include language metadata in response
            response_data = serialize_dataclass(game_response)
            response_data["language_info"] = serialize_dataclass(language_metadata)

            return Response(
                create_success_response(response_data, "Shuffling game generated successfully"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to generate shuffling game: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AyahMatchingView(APIView):
    """Enhanced view for Ayah matching game with flexible ayah selection."""

    @swagger_auto_schema(
        operation_description="Generate a matching game for specified Ayah range with multi-language support",
        request_body=serializers.GameRequestSerializer,
        manual_parameters=[
            openapi.Parameter(
                "Language-Code",
                openapi.IN_HEADER,
                description="Preferred language for translations and transliterations (e.g., 'en', 'id', 'ar'). Defaults to 'en' if not provided.",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                "Matching game generated successfully",
                MatchingGameResponseSchemas.success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Matching game generated successfully",
                        "data": {
                            "metadata": {
                                "surah_id": 1,
                                "surah_name": "Al-Fatihah",
                                "start_ayah_number": 1,
                                "ayah_count": 2,
                                "total_ayahs_in_surah": 7,
                            },
                            "ayahs": [
                                {
                                    "ayah_number": 1,
                                    "arabic_text": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
                                    "translation": "In the name of Allah, the Most Gracious, the Most Merciful",
                                    "transliteration": "Bismillahir-Rahmanir-Raheem",
                                    "image_url": "https://example.com/images/001_001.png",
                                }
                            ],
                            "language_info": {
                                "requested_language": "en",
                                "used_language": "en",
                                "fallback_applied": False,
                                "available_languages": ["en", "id", "ar"],
                            },
                        },
                    }
                },
            ),
            400: openapi.Response("Invalid request data", MatchingGameResponseSchemas.error_400),
            403: openapi.Response("Surah not unlocked for user", MatchingGameResponseSchemas.error_403),
            404: openapi.Response("Surah not found", MatchingGameResponseSchemas.error_404),
            500: openapi.Response("Internal server error", MatchingGameResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Generate enhanced matching game with flexible ayah selection."""
        serializer = serializers.GameRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]
            start_ayah_number = serializer.validated_data["start_ayah_number"]
            count = serializer.validated_data["count"]

            # Get language preference from headers
            requested_language = LanguageService.get_language_from_header(request)

            # Validate Surah access for authenticated users
            if hasattr(request, "user") and request.user.is_authenticated:
                if not SurahProgressionService.validate_surah_access(request.user.id, surah_id):
                    return Response(
                        create_error_response(f"Surah {surah_id} is not unlocked for this user"),
                        status=status.HTTP_403_FORBIDDEN,
                    )

            # Generate enhanced matching game with language support
            game_response, language_metadata = services.GameService.generate_matching_game_enhanced(
                surah_id=surah_id, start_ayah_number=start_ayah_number, count=count, language=requested_language
            )

            # Include language metadata in response
            response_data = serialize_dataclass(game_response)
            response_data["language_info"] = serialize_dataclass(language_metadata)

            return Response(
                create_success_response(response_data, "Matching game generated successfully"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to generate matching game: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AyahMemoryView(APIView):
    """Enhanced view for Ayah memory game with flexible ayah selection."""

    @swagger_auto_schema(
        operation_description="Generate a memory game for specified Ayah range with multi-language support",
        request_body=serializers.GameRequestSerializer,
        manual_parameters=[
            openapi.Parameter(
                "Language-Code",
                openapi.IN_HEADER,
                description="Preferred language for translations and transliterations (e.g., 'en', 'id', 'ar'). Defaults to 'en' if not provided.",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                "Memory game generated successfully",
                MemoryGameResponseSchemas.success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Memory game generated successfully",
                        "data": {
                            "metadata": {
                                "surah_id": 1,
                                "surah_name": "Al-Fatihah",
                                "start_ayah_number": 1,
                                "ayah_count": 2,
                                "total_ayahs_in_surah": 7,
                            },
                            "ayahs": [
                                {
                                    "ayah_number": 1,
                                    "arabic_text": "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ",
                                    "translation": "In the name of Allah, the Most Gracious, the Most Merciful",
                                    "transliteration": "Bismillahir-Rahmanir-Raheem",
                                    "image_url": "https://example.com/images/001_001.png",
                                }
                            ],
                            "language_info": {
                                "requested_language": "en",
                                "used_language": "en",
                                "fallback_applied": False,
                                "available_languages": ["en", "id", "ar"],
                            },
                        },
                    }
                },
            ),
            400: openapi.Response("Invalid request data", MemoryGameResponseSchemas.error_400),
            403: openapi.Response("Surah not unlocked for user", MemoryGameResponseSchemas.error_403),
            404: openapi.Response("Surah not found", MemoryGameResponseSchemas.error_404),
            500: openapi.Response("Internal server error", MemoryGameResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Generate enhanced memory game with flexible ayah selection."""
        serializer = serializers.GameRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]
            start_ayah_number = serializer.validated_data["start_ayah_number"]
            count = serializer.validated_data["count"]

            # Get language preference from headers
            requested_language = LanguageService.get_language_from_header(request)

            # Validate Surah access for authenticated users
            if hasattr(request, "user") and request.user.is_authenticated:
                if not SurahProgressionService.validate_surah_access(request.user.id, surah_id):
                    return Response(
                        create_error_response(f"Surah {surah_id} is not unlocked for this user"),
                        status=status.HTTP_403_FORBIDDEN,
                    )

            # Generate enhanced memory game with language support
            game_response, language_metadata = services.GameService.generate_memory_game_enhanced(
                surah_id=surah_id, start_ayah_number=start_ayah_number, count=count, language=requested_language
            )

            # Include language metadata in response
            response_data = serialize_dataclass(game_response)
            response_data["language_info"] = serialize_dataclass(language_metadata)

            return Response(
                create_success_response(response_data, "Memory game generated successfully"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to generate memory game: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class AudioGameView(APIView):
    """Enhanced view for audio game with flexible ayah selection."""

    @swagger_auto_schema(
        operation_description="Generate an audio game for specified Ayah range with multi-language support",
        request_body=serializers.GameRequestSerializer,
        manual_parameters=[
            openapi.Parameter(
                "Language-Code",
                openapi.IN_HEADER,
                description="Preferred language for translations and transliterations (e.g., 'en', 'id', 'ar'). Defaults to 'en' if not provided.",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response("Audio game generated successfully", AudioGameResponseSchemas.success_200),
            400: openapi.Response("Invalid request data", AudioGameResponseSchemas.error_400),
            403: openapi.Response("Surah not unlocked for user", AudioGameResponseSchemas.error_403),
            404: openapi.Response("Surah not found", AudioGameResponseSchemas.error_404),
            500: openapi.Response("Internal server error", AudioGameResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Generate enhanced audio game with flexible ayah selection."""
        serializer = serializers.GameRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]
            start_ayah_number = serializer.validated_data["start_ayah_number"]
            count = serializer.validated_data["count"]

            # Get language preference from headers
            requested_language = LanguageService.get_language_from_header(request)

            # Validate Surah access for authenticated users
            if hasattr(request, "user") and request.user.is_authenticated:
                if not SurahProgressionService.validate_surah_access(request.user.id, surah_id):
                    return Response(
                        create_error_response(f"Surah {surah_id} is not unlocked for this user"),
                        status=status.HTTP_403_FORBIDDEN,
                    )

            # Generate enhanced audio game with language support
            game_response, language_metadata = services.GameService.generate_audio_game_enhanced(
                surah_id=surah_id, start_ayah_number=start_ayah_number, count=count, language=requested_language
            )

            # Include language metadata in response
            response_data = serialize_dataclass(game_response)
            response_data["language_info"] = serialize_dataclass(language_metadata)

            return Response(
                create_success_response(response_data, "Audio game generated successfully"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to generate audio game: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GapsGameView(APIView):
    """Enhanced view for Gaps game with flexible ayah selection and multi-level support."""

    @swagger_auto_schema(
        operation_description="Generate a word reconstruction game for specified Ayah range where users build complete ayahs from individual words with multi-language support",
        request_body=serializers.GameRequestSerializer,
        manual_parameters=[
            openapi.Parameter(
                "Language-Code",
                openapi.IN_HEADER,
                description="Preferred language for translations and transliterations (e.g., 'en', 'id', 'ar'). Defaults to 'en' if not provided.",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response("Gaps game generated successfully", GapsGameResponseSchemas.success_200),
            400: openapi.Response("Invalid request data", GapsGameResponseSchemas.error_400),
            403: openapi.Response("Surah not unlocked for user", GapsGameResponseSchemas.error_403),
            404: openapi.Response("Surah not found", GapsGameResponseSchemas.error_404),
            500: openapi.Response("Internal server error", GapsGameResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Generate word reconstruction game with flexible ayah selection and multi-level support."""
        serializer = serializers.GameRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid request data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_id = serializer.validated_data["surah_id"]
            start_ayah_number = serializer.validated_data["start_ayah_number"]
            count = serializer.validated_data["count"]

            # Get language preference from headers
            requested_language = LanguageService.get_language_from_header(request)

            # Validate Surah access for authenticated users
            if hasattr(request, "user") and request.user.is_authenticated:
                if not SurahProgressionService.validate_surah_access(request.user.id, surah_id):
                    return Response(
                        create_error_response(f"Surah {surah_id} is not unlocked for this user"),
                        status=status.HTTP_403_FORBIDDEN,
                    )

            # Generate enhanced gaps game with language support
            game_response, language_metadata = services.GameService.generate_gaps_game_enhanced(
                surah_id=surah_id, start_ayah_number=start_ayah_number, count=count, language=requested_language
            )

            # Include language metadata in response
            response_data = serialize_dataclass(game_response)
            response_data["language_info"] = serialize_dataclass(language_metadata)

            return Response(
                create_success_response(response_data, "Word reconstruction game generated successfully"),
                status=status.HTTP_200_OK,
            )
        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except NotFound as e:
            return Response(create_error_response(str(e)), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to generate word reconstruction game: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class WordAudioView(APIView):
    """View for retrieving the URL to a specific word's audio in the Quran."""

    @swagger_auto_schema(
        operation_description="Generate a URL to the audio file for a specific word in the Quran",
        request_body=serializers.WordAudioRequestSerializer,
        responses={
            200: openapi.Response("Successfully generated audio URL", WordAudioResponseSchemas.success_200),
            400: openapi.Response("Invalid request data", WordAudioResponseSchemas.error_400),
            500: openapi.Response("Internal server error", WordAudioResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Generate a URL to the audio file for a specific word in the Quran."""
        serializer = serializers.WordAudioRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                create_error_response("Invalid data", serializer.errors), status=status.HTTP_400_BAD_REQUEST
            )

        try:
            surah_number = serializer.validated_data.get("surah_number")
            ayah_number = serializer.validated_data.get("ayah_number")

            # Format the numbers with leading zeros
            # Surah numbers are 3 digits (001-114)
            # Ayah numbers are 3 digits (001-286 for the longest surah)
            # Word numbers are 3 digits (001-...)
            # get ayahs to cycle through them
            ayah = services.QuranService.get_ayah_text(surah_number, ayah_number)
            audio_urls = []
            for i in range(len(ayah.arabic_text.split())):
                surah_formatted = f"{surah_number:03d}"
                ayah_formatted = f"{ayah_number:03d}"
                word_formatted = f"{i + 1:03d}"

                # Construct the URL
                audio_urls.append(
                    f"https://audios.quranwbw.com/words/{surah_number}/{surah_formatted}_{ayah_formatted}_{word_formatted}.mp3"
                )
            return Response(
                create_success_response({"audio_urls": audio_urls}, "Audio URL generated successfully"),
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                create_error_response(f"Failed to generate audio URL: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ArabicLetterAudioRecognitionView(APIView):
    """View for Arabic letter audio recognition game."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Generate an audio recognition game for a specific Arabic letter",
        request_body=AudioRecognitionGameRequestSerializer,
        responses={
            200: openapi.Response(
                "Audio recognition game generated successfully",
                AudioRecognitionGameResponseSchemas.success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Audio recognition game generated successfully",
                        "data": {
                            "metadata": {
                                "letter_id": 1,
                                "letter_title": "أ",
                                "letter_transcription": "alif",
                                "game_type": "audio_recognition",
                                "total_options": 4,
                            },
                            "target_letter": "أ",
                            "target_audio_url": "https://example.com/audio/alif.mp3",
                            "transliteration_options": [
                                {
                                    "id": "option_1",
                                    "transliteration_text": "alif",
                                    "is_correct": True,
                                },
                                {
                                    "id": "option_2",
                                    "transliteration_text": "baa",
                                    "is_correct": False,
                                },
                                {
                                    "id": "option_3",
                                    "transliteration_text": "taa",
                                    "is_correct": False,
                                },
                            ],
                            "correct_transliteration": "alif",
                        },
                    }
                },
            ),
            400: openapi.Response("Invalid request data", AudioRecognitionGameResponseSchemas.error_400),
            404: openapi.Response("Arabic letter not found", AudioRecognitionGameResponseSchemas.error_404),
            500: openapi.Response("Internal server error", AudioRecognitionGameResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Generate an audio recognition game for a specific Arabic letter."""
        try:
            # Validate request data
            serializer = AudioRecognitionGameRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    create_error_response("Invalid request data", serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            letter_id = serializer.validated_data["letter_id"]

            # Generate game through service
            game_response = services.ArabicLetterGameService.generate_audio_recognition_game(letter_id)

            # Prepare response data
            response_data = serialize_dataclass(game_response)

            return Response(
                create_success_response(response_data, "Audio recognition game generated successfully"),
                status=status.HTTP_200_OK,
            )

        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to generate audio recognition game: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ArabicLetterTranscriptionMatchingView(APIView):
    """View for Arabic letter transcription matching game."""

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Generate a transcription matching game for a specific Arabic letter",
        request_body=TranscriptionMatchingGameRequestSerializer,
        responses={
            200: openapi.Response(
                "Transcription matching game generated successfully",
                TranscriptionMatchingGameResponseSchemas.success_200,
                examples={
                    "application/json": {
                        "success": True,
                        "message": "Transcription matching game generated successfully",
                        "data": {
                            "metadata": {
                                "letter_id": 1,
                                "letter_title": "أ",
                                "letter_transcription": "alif",
                                "game_type": "transcription_matching",
                                "total_options": 4,
                            },
                            "target_transcription": "alif",
                            "letter_options": [
                                {"id": "option_1", "letter_title": "أ", "letter_id": 1, "is_correct": True},
                                {"id": "option_2", "letter_title": "ب", "letter_id": 2, "is_correct": False},
                                {"id": "option_3", "letter_title": "ت", "letter_id": 3, "is_correct": False},
                            ],
                            "correct_letter_id": 1,
                        },
                    }
                },
            ),
            400: openapi.Response("Invalid request data", TranscriptionMatchingGameResponseSchemas.error_400),
            404: openapi.Response("Arabic letter not found", TranscriptionMatchingGameResponseSchemas.error_404),
            500: openapi.Response("Internal server error", TranscriptionMatchingGameResponseSchemas.error_500),
        },
    )
    def post(self, request):
        """Generate a transcription matching game for a specific Arabic letter."""
        try:
            # Validate request data
            serializer = TranscriptionMatchingGameRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    create_error_response("Invalid request data", serializer.errors),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            letter_id = serializer.validated_data["letter_id"]

            # Generate game through service
            game_response = services.ArabicLetterGameService.generate_transcription_matching_game(letter_id)

            # Prepare response data
            response_data = serialize_dataclass(game_response)

            return Response(
                create_success_response(response_data, "Transcription matching game generated successfully"),
                status=status.HTTP_200_OK,
            )

        except ValidationError as e:
            return Response(create_error_response(str(e)), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(
                create_error_response(f"Failed to generate transcription matching game: {str(e)}"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
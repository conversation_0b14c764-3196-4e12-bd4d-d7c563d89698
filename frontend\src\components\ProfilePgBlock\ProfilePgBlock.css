.my-pg-container {
  height: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.pg-block {
  background-color: #fbfbfb;
  filter: drop-shadow(6px 6px 15px rgba(134, 134, 134, 0.2));;
  border-radius: 30px;
  padding: 20px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.circular-pg {
  /* border: 10px solid #e4e4e4; */
  /* filter: drop-shadow(3px 3px 3px #949494); */
  filter: drop-shadow(6px 6px 15px rgba(150, 150, 150, 0.2));
  border-radius: 50%;
  width: 150px;
  height: 150px;
}

.circular-pg-done {
  position: relative;
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(#97cb9c 63%, #e4e4e4 0deg);
  display: flex;
  justify-content: center;
  align-items: center;

}

.circular-pg-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}


.inner-circular-pg {
  font-size: 18px;
  background-color: #fbfbfb;
  width: 125px;
  height: 125px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
import React from "react";
import Logo from "src/components/Logo";
import "src/pages/PickPath/PickPathPage.css";
import leftCard from "src/assets/card1.png";
import rightCard from "src/assets/card2.png";
import PickCard from "src/components/PickPathCard/PickPathCard.jsx";

import { Link } from "react-router-dom";

function PickPathPage() {
  return (
    <>
      <div className="body-container">
        <div className="logo-div">
          <Logo />
        </div>
        <div className="container-flex">
          <a className="main-title">Choose your path</a>
          <Link to="/levels">
            <PickCard
              imgSrc={leftCard}
              title="Custom path"
              text="Make your own study plan and choose your own surah to study"
            />
          </Link>
          <Link to="/levels">
            <PickCard
              imgSrc={rightCard}
              title="Hafidth’s path"
              text="Learn the Qur'an with our tailored learning plan"
            />
          </Link>
        </div>
      </div>
    </>
  );
}

export default PickPathPage;

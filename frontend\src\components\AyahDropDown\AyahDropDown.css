/* Container for dropdown and button */
.dropdown {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: fit-content;
  position: relative; /* Needed for the dropdown menu to be positioned absolutely */
  z-index: 1; /* To ensure this element is on top of others */
}

/* Button styles */
.dropdown-button {
  padding: 10px 20px;
  background-color: #F6F4F4;
  width: 210px; /* Fixed width to control size */
  display: flex;
  justify-content: center; /* Center text horizontally */
  align-items: center; /* Center text vertically */
  border: none;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
  cursor: pointer;
  filter: drop-shadow(5px 12px 5px #9c9c9c25);
  color: #828282;
  /* box-sizing: border-box; */
}

/* Dropdown menu */
.dropdown-menu {
  color: #828282;
  list-style: none;
  padding: 0;
  margin: 0;
  position: absolute; /* Absolute positioning to overlay on top of everything */
  top: 100%; /* Start right below the button */
  left: 0;
  width: 100%; /* Inherit width from the button */
  background-color: #F6F4F4;
  max-height: 0;
  overflow: hidden;
  z-index: 10; /* Higher z-index to ensure it appears on top */
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  transition: max-height 0.4s ease-out, opacity 0.3s ease-out;
  box-shadow: 0px 8px 15px rgba(0, 0, 0, 0.1);
  filter: drop-shadow(5px 12px 5px #9c9c9c25);
}

/* Allow scrolling if dropdown is too large */
.dropdown.open .dropdown-menu {
  max-height: 200px; /* Adjust max height of dropdown list */
  overflow-y: auto; /* Enables vertical scrolling */
  opacity: 1;
  transition: max-height 0.4s ease-out, opacity 0.3s ease-out;
}

/* Dropdown items */
.dropdown-item {
  padding: 10px 20px;
  cursor: pointer;
  background-color: #F6F4F4;
  border-bottom: 1px solid #ddd;
  transition: background-color 0.3s ease, transform 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dropdown-item:hover {
  background-color: #ecebeb;
}

/* Remove bottom border-radius from first dropdown item */
.dropdown-item:first-child {
  border-radius: 0;
}

/* Add bottom border-radius only to the last dropdown item */
.dropdown-item:last-child {
  border-bottom-left-radius: 15px;
  border-bottom-right-radius: 15px;
}

.progress-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.circle {
  width: 20px;
  height: 20px;
  margin: 0 5px;
  background-color: #ccc;
  border-radius: 50%;
  display: inline-block;
}

.circle.completed {
  background-color: #4caf50;
}

.sentence-text {
  font-size: 40px;
  padding: 5px;
}

.sentence-gap {
  min-width: 100px; /* Минимальная ширина для пропуска */
  height: 60px; /* Фиксированная высота, как в первой версии */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  border-radius: 20px;
  padding: 5px; /* Отступы для удобства */
  background-color: transparent; /* Пропуск остается прозрачным */
  transition: width 0.3s ease-in-out; /* Плавное изменение ширины */
  white-space: nowrap; /* Текст в одну строку */
}

.sentence-gap:hover{
  background-color: #a9a9a9;
}

.sentence-gap.filled {
  width: auto; /* Ширина пропуска будет подстраиваться под содержимое */
  max-width: 100%; /* Ограничение по ширине, чтобы не выходило за границы контейнера */
  background-color: transparent; /* Прозрачный фон для заполненных пропусков */
}

.phrase-card {
  padding: 5px 10px;
  font-size: 36px; /* Базовый размер текста */
  background-color: #ffffff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  cursor: grab;
  transition: background-color 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 170px;
  overflow: hidden; /* Обрезка текста в исходном состоянии */
  text-overflow: ellipsis; /* Троеточие для длинного текста в исходном состоянии */
  white-space: normal; /* Позволяем перенос текста на новую строку */
  word-wrap: break-word; /* Перенос длинных слов */
}

.phrase-card.in-gap {
  height: 60px; /* Фиксированная высота, как в пропуске */
  width: auto; /* Ширина карточки динамическая */
  background-color: transparent;
  box-shadow: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap; /* Текст вытягивается в одну строку */
  text-overflow: clip; /* Отключаем обрезку текста */
  overflow: visible; /* Позволяем тексту отображаться полностью */
}

.phrase-card.dragging {
  background-color: #d3f8d3;
  transition: background-color 0.2s ease-in-out;
  max-width: fit-content;
  min-width: 80px;
  height: 60px; /* Высота во время перетаскивания также фиксирована */
}

.submit-btn,
.next-btn {
  margin-top: 20px;
  padding: 10px;
  font-size: 18px;
  cursor: pointer;
}

.phrases-container {
  gap: 10px;
  overflow-y: hidden;
  overflow-x: auto;
  display: flex;
  justify-content: flex-start;
  position: sticky; /* Липкое позиционирование */
  bottom: 0; /* Фиксируем у нижнего края контейнера */
  background-color: #f6f6f6;
  padding: 20px;
  border-radius: 20px;
  height: 300px;
  width: 80vw;
}

.sentence-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0px;
  padding-left: 200px;
  padding-right: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}

.sentence-container::-webkit-scrollbar {
  width: 2px;
}

.sentence-container::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.sentence-container::-webkit-scrollbar-thumb {
  background-color: #97cb9c;
  outline: 1px solid slategrey;
  border-radius: 10px;
}

.phrases-container::-webkit-scrollbar {
  height: 2px;
}

.phrases-container::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.phrases-container::-webkit-scrollbar-thumb {
  background-color: #97cb9c;
  outline: 1px solid slategrey;
  border-radius: 10px;
}

.sentence-text,
.sentence-gap {
  font-size: 40px; /* Размер текста */
  padding: 5px; /* Отступы */
  line-height: 1.2; /* Межстрочный интервал */
  position: relative; /* Для зеленой линии под каждым элементом */
  display: inline-block; /* Позволяет им стоять в один ряд */
  height: 60px; /* Фиксированная высота */
}

.sentence-text::after,
.sentence-gap::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 2px; /* Толщина линии */
  background-color: #4caf50; /* Зеленая линия */
}

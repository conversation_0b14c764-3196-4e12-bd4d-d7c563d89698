// src/authStore.js
import { create } from "zustand";
import { persist } from "zustand/middleware";

const useAuthStore = create(
  persist(
    (set) => ({
      token: null,
      isAuthenticated: false,
      loading: true, // Add loading state

      // Initialize authentication
      initializeAuth: () => {
        const token = localStorage.getItem("authToken");
        if (token) {
          set({ token, isAuthenticated: true, loading: false });
        } else {
          set({ token: null, isAuthenticated: false, loading: false });
        }
      },

      // Set token after login
      setToken: (token) => {
        localStorage.setItem("authToken", token);
        set({ token, isAuthenticated: true });
      },

      // Logout function
      logout: () => {
        localStorage.removeItem("authToken");
        set({ token: null, isAuthenticated: false });
      },
    }),
    {
      name: "auth-store", // Name for local storage persistence
    }
  )
);

export default useAuthStore;

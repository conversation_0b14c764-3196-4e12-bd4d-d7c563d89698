"""
Service layer.

This module contains business logic for game-related operations.
Services handle game generation, validation, and coordination.
"""

from typing import Optional, List, Tuple
from django.contrib.auth import get_user_model
from django.db import transaction
from django.core.files.base import ContentFile
from rest_framework.exceptions import ValidationError, NotFound
from knox.models import AuthToken as KnoxAuthToken
import requests
import logging

from quran.models import Ayah, Surah, SurahTranslation
from progress.models import UserAyahMM
from progress.repositories import UserAyahRepository
from progress.transports import UserAyahRelation, EnhancedBookmark
from core.transports import LanguageMetadata
from quran.repositories import LanguageRepository
from quran.services import LanguageService
from . import transports
from . import repositories

logger = logging.getLogger(__name__)
User = get_user_model()


class UserService:
    """Service for user management operations."""

    @staticmethod
    def get_user_profile(user_id: int) -> Optional[transports.UserProfile]:
        """Get user profile by ID."""
        return repositories.Users.get_user_by_id(user_id)

    @staticmethod
    def get_enhanced_user_profile(
        user_id: int, social_limit: int = 10, bookmark_limit: int = 5
    ) -> Optional[transports.EnhancedUserProfile]:
        """Get enhanced user profile with social, league, and bookmark information."""
        # Get basic user profile
        basic_profile = repositories.Users.get_user_by_id(user_id)
        if not basic_profile:
            return None

        # Import here to avoid circular imports
        from social.services import FollowService

        # Get social summary
        followers_count, following_count, followers, following = FollowService.get_social_summary(user_id, social_limit)
        social_summary = transports.SocialSummary(
            followers_count=followers_count, following_count=following_count, followers=followers, following=following
        )

        # Get league position information
        league_position = transports.LeaguePosition(
            league=basic_profile.league,
            user_rank=None,  # Could be enhanced with actual league ranking logic
            total_participants=None,  # Could be enhanced with league participant count
        )

        # Get bookmark summary
        bookmarks_count = BookmarkService.get_bookmarks_count(user_id)
        recent_bookmarks = BookmarkService.get_recent_bookmarks(user_id, bookmark_limit)
        bookmark_summary = transports.BookmarkSummary(
            bookmarks_count=bookmarks_count, recent_bookmarks=recent_bookmarks
        )

        # Create enhanced profile
        enhanced_profile = transports.EnhancedUserProfile(
            # Basic profile fields
            id=basic_profile.id,
            username=basic_profile.username,
            first_name=basic_profile.first_name,
            last_name=basic_profile.last_name,
            email=basic_profile.email,
            profile_photo=basic_profile.profile_photo,
            date_joined=basic_profile.date_joined,
            total_xp=basic_profile.total_xp,
            current_level=basic_profile.current_level,
            day_streak=basic_profile.day_streak,
            gender=basic_profile.gender,
            # Enhanced fields
            social_summary=social_summary,
            league_position=league_position,
            bookmark_summary=bookmark_summary,
        )

        return enhanced_profile

    @staticmethod
    def update_user_profile(
        user_id: int,
        username: str = None,
        email: str = None,
        first_name: str = None,
        last_name: str = None,
        gender: str = None,
    ) -> transports.UserProfile:
        """Update user profile information."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        if username is not None:
            user.username = username
        if email is not None:
            user.email = email
        if first_name is not None:
            user.first_name = first_name
        if last_name is not None:
            user.last_name = last_name
        if gender is not None:
            user.gender = gender

        user.save()

        return repositories.Users._make_user_profile_transport(user)

    @staticmethod
    def update_profile_picture(user_id: int, image_data: bytes, filename: str) -> transports.UserProfile:
        """Update user profile picture."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        # Save the image
        image_file = ContentFile(image_data, name=filename)
        user.profile_photo.save(filename, image_file, save=True)

        return repositories.Users._make_user_profile_transport(user)

    @staticmethod
    def update_complete_profile(
        user_id: int,
        username: str = None,
        email: str = None,
        first_name: str = None,
        last_name: str = None,
        gender: str = None,
        profile_photo_data: bytes = None,
        profile_photo_filename: str = None,
    ) -> transports.UserProfile:
        """Update complete user profile information including profile picture."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        # Update text fields
        if username is not None:
            user.username = username
        if email is not None:
            user.email = email
        if first_name is not None:
            user.first_name = first_name
        if last_name is not None:
            user.last_name = last_name
        if gender is not None:
            user.gender = gender

        # Update profile picture if provided
        if profile_photo_data and profile_photo_filename:
            image_file = ContentFile(profile_photo_data, name=profile_photo_filename)
            user.profile_photo.save(profile_photo_filename, image_file, save=False)

        user.save()

        return repositories.Users._make_user_profile_transport(user)

    @staticmethod
    def download_and_save_profile_picture(user_id: int, image_url: str) -> transports.UserProfile:
        """Download and save profile picture from URL."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        try:
            response = requests.get(image_url, timeout=10)
            response.raise_for_status()

            # Extract filename from URL or use default
            filename = image_url.split("/")[-1] or f"profile_{user_id}.jpg"

            image_file = ContentFile(response.content, name=filename)
            user.profile_photo.save(filename, image_file, save=True)

            return repositories.Users._make_user_profile_transport(user)
        except requests.RequestException as e:
            logger.error(f"Failed to download profile picture: {e}")
            raise ValidationError("Failed to download profile picture")


class AuthenticationService:
    """Service for authentication operations."""

    @staticmethod
    def create_auth_token(user_id: int) -> transports.AuthToken:
        """Create authentication token for user."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        token = KnoxAuthToken.objects.create(user=user)[1]
        user_profile = repositories.Users._make_user_profile_transport(user)

        return transports.AuthToken(token=token, user=user_profile)

    @staticmethod
    def check_email_availability(email: str) -> transports.EmailAvailability:
        """Check if an email address is available for registration."""
        is_available = not User.objects.filter(email=email).exists()
        return transports.EmailAvailability(email=email, is_available=is_available)

    @staticmethod
    def request_email_verification_for_registration(email: str) -> transports.EmailVerificationRequest:
        """Request email verification for user registration."""
        # Check if email is already in use
        if User.objects.filter(email=email).exists():
            raise ValidationError("A user with this email already exists.")

        # In production, this would send an actual email with verification code
        # For now, we use a hardcoded verification code "0000"
        return transports.EmailVerificationRequest(
            email=email,
            verification_required=True,
            message="Verification code sent to your email. Please enter the code to continue registration.",
        )

    @staticmethod
    def verify_email_code_for_registration(email: str, verification_code: str) -> transports.EmailVerificationResult:
        """Verify email verification code for registration."""
        # Check if email is already in use
        if User.objects.filter(email=email).exists():
            raise ValidationError("A user with this email already exists.")

        # For development, use hardcoded verification code "0000"
        if verification_code == "0000":
            return transports.EmailVerificationResult(
                email=email,
                is_verified=True,
                message="Email verified successfully. You can now complete your registration.",
            )
        else:
            return transports.EmailVerificationResult(
                email=email, is_verified=False, message="Invalid verification code. Please try again."
            )

    @staticmethod
    def request_password_reset(email: str) -> transports.PasswordResetRequest:
        """Request password reset for existing user."""
        # Check if user exists
        try:
            User.objects.get(email=email)
        except User.DoesNotExist:
            raise ValidationError("No user found with this email address.")

        # In production, this would send an actual email with verification code
        # For now, we use a hardcoded verification code "0000"
        return transports.PasswordResetRequest(
            email=email,
            verification_required=True,
            message="Verification code sent to your email. Please enter the code to reset your password.",
        )

    @staticmethod
    def verify_password_reset_code(email: str, verification_code: str) -> transports.EmailVerificationResult:
        """Verify email verification code for password reset."""
        # Check if user exists
        try:
            User.objects.get(email=email)
        except User.DoesNotExist:
            raise ValidationError("No user found with this email address.")

        # For development, use hardcoded verification code "0000"
        if verification_code == "0000":
            return transports.EmailVerificationResult(
                email=email, is_verified=True, message="Email verified successfully. You can now reset your password."
            )
        else:
            return transports.EmailVerificationResult(
                email=email, is_verified=False, message="Invalid verification code. Please try again."
            )

    @staticmethod
    def reset_password_with_verification(
        email: str, verification_code: str, new_password: str
    ) -> transports.PasswordResetResult:
        """Reset password after email verification."""
        # First verify the code
        verification_result = AuthenticationService.verify_password_reset_code(email, verification_code)

        if not verification_result.is_verified:
            return transports.PasswordResetResult(
                email=email, is_reset=False, message="Invalid verification code. Password reset failed."
            )

        # Reset the password
        try:
            user = User.objects.get(email=email)
            user.set_password(new_password)
            user.save()

            return transports.PasswordResetResult(email=email, is_reset=True, message="Password reset successfully.")
        except User.DoesNotExist:
            raise ValidationError("No user found with this email address.")

    @staticmethod
    def _generate_username_from_email(email: str) -> str:
        """Generate a unique username from email address."""
        import re

        # Extract the local part of the email (before @)
        local_part = email.split("@")[0]

        # Remove non-alphanumeric characters and replace with underscores
        # Keep only letters, numbers, and underscores
        base_username = re.sub(r"[^a-zA-Z0-9_]", "_", local_part)

        # Remove consecutive underscores
        base_username = re.sub(r"_+", "_", base_username)

        # Remove leading/trailing underscores
        base_username = base_username.strip("_")

        # Ensure minimum length of 3 characters
        if len(base_username) < 3:
            base_username = f"user_{base_username}"

        # Ensure maximum length of 30 characters
        if len(base_username) > 30:
            base_username = base_username[:30]

        # Remove trailing underscore if truncation created one
        base_username = base_username.rstrip("_")

        # Check uniqueness and add number suffix if needed
        username = base_username
        counter = 1

        while User.objects.filter(username=username).exists():
            # Calculate available space for counter
            counter_str = str(counter)
            max_base_length = 30 - len(counter_str) - 1  # -1 for underscore

            if len(base_username) > max_base_length:
                truncated_base = base_username[:max_base_length].rstrip("_")
                username = f"{truncated_base}_{counter}"
            else:
                username = f"{base_username}_{counter}"

            counter += 1

            # Safety check to prevent infinite loop
            if counter > 9999:
                import uuid

                username = f"user_{uuid.uuid4().hex[:8]}"
                break

        return username

    @staticmethod
    def register_user(
        email: str, password: str, username: str = None, first_name: str = "", last_name: str = "", gender: str = None
    ) -> transports.AuthToken:
        """Register a new user and create authentication token."""
        # Check if user already exists
        if User.objects.filter(email=email).exists():
            raise ValidationError("A user with this email already exists.")

        # Generate username if not provided
        if not username:
            username = AuthenticationService._generate_username_from_email(email)
        else:
            # Check username uniqueness only if explicitly provided
            if User.objects.filter(username=username).exists():
                raise ValidationError("A user with this username already exists.")

        # Create new user
        user = User.objects.create_user(
            username=username, email=email, password=password, first_name=first_name, last_name=last_name
        )

        # Set gender if provided
        if gender is not None:
            user.gender = gender
            user.save()

        # Create and return authentication token
        return AuthenticationService.create_auth_token(user.id)

    @staticmethod
    def authenticate_user(login: str, password: str) -> transports.AuthToken:
        """Authenticate user with email/username and password."""
        user = None

        # Try to find user by email or username
        if "@" in login:
            # Login with email
            try:
                user = User.objects.get(email=login)
            except User.DoesNotExist:
                pass
        else:
            # Login with username
            try:
                user = User.objects.get(username=login)
            except User.DoesNotExist:
                pass

        if user and user.check_password(password):
            if not user.is_active:
                raise ValidationError("User account is disabled.")

            # Create and return authentication token
            return AuthenticationService.create_auth_token(user.id)

        raise ValidationError("Invalid login credentials.")

    @staticmethod
    def change_password(user_id: int, current_password: str, new_password: str) -> bool:
        """Change user password after validating current password."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        # Validate current password
        if not user.check_password(current_password):
            raise ValidationError("Current password is incorrect.")

        # Set new password
        user.set_password(new_password)
        user.save()

        return True

    @staticmethod
    def get_or_create_user_from_social(
        email: str, username: str, first_name: str = "", last_name: str = "", gender: str = None
    ) -> Tuple[transports.UserProfile, bool]:
        """Get or create user from social authentication data."""
        defaults = {
            "username": username,
            "first_name": first_name,
            "last_name": last_name,
        }
        if gender is not None:
            defaults["gender"] = gender

        user, created = User.objects.get_or_create(
            email=email,
            defaults=defaults,
        )

        user_profile = repositories.Users._make_user_profile_transport(user)
        return user_profile, created


class AyahInteractionService:
    """Service for ayah interaction operations (likes, dislikes, reports)."""

    @staticmethod
    def react_to_ayah(
        user_id: int, surah_id: int, ayah_number: int, reaction_type: str
    ) -> transports.AyahReactionResult:
        """Add or update user reaction to an ayah."""
        from .models import AyahReaction

        # Validate user exists
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        # Validate ayah exists
        try:
            surah = Surah.objects.get(surah_number=surah_id)
            ayah = Ayah.objects.get(surah=surah, ayah_number=ayah_number)
        except (Surah.DoesNotExist, Ayah.DoesNotExist):
            raise ValidationError(f"Ayah {ayah_number} in Surah {surah_id} does not exist.")

        # Validate reaction type
        if reaction_type not in ["like", "dislike"]:
            raise ValidationError("Reaction type must be 'like' or 'dislike'.")

        # Get or create reaction
        reaction, created = AyahReaction.objects.get_or_create(
            user=user, ayah=ayah, defaults={"reaction_type": reaction_type}
        )

        # Update reaction if it already exists
        if not created:
            reaction.reaction_type = reaction_type
            reaction.save()

        # Get reaction counts
        like_count = AyahReaction.objects.filter(ayah=ayah, reaction_type="like").count()
        dislike_count = AyahReaction.objects.filter(ayah=ayah, reaction_type="dislike").count()

        message = f"Ayah {reaction_type}d successfully" if created else f"Ayah reaction updated to {reaction_type}"

        return transports.AyahReactionResult(
            surah_id=surah_id,
            ayah_number=ayah_number,
            user_reaction=reaction_type,
            like_count=like_count,
            dislike_count=dislike_count,
            message=message,
        )

    @staticmethod
    def remove_reaction_from_ayah(user_id: int, surah_id: int, ayah_number: int) -> transports.AyahReactionResult:
        """Remove user reaction from an ayah."""
        from .models import AyahReaction

        # Validate user exists
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        # Validate ayah exists
        try:
            surah = Surah.objects.get(surah_number=surah_id)
            ayah = Ayah.objects.get(surah=surah, ayah_number=ayah_number)
        except (Surah.DoesNotExist, Ayah.DoesNotExist):
            raise ValidationError(f"Ayah {ayah_number} in Surah {surah_id} does not exist.")

        # Remove reaction if exists
        deleted_count, _ = AyahReaction.objects.filter(user=user, ayah=ayah).delete()

        # Get updated reaction counts
        like_count = AyahReaction.objects.filter(ayah=ayah, reaction_type="like").count()
        dislike_count = AyahReaction.objects.filter(ayah=ayah, reaction_type="dislike").count()

        message = "Reaction removed successfully" if deleted_count > 0 else "No reaction found to remove"

        return transports.AyahReactionResult(
            surah_id=surah_id,
            ayah_number=ayah_number,
            user_reaction=None,
            like_count=like_count,
            dislike_count=dislike_count,
            message=message,
        )

    @staticmethod
    def report_ayah(
        user_id: int, surah_id: int, ayah_number: int, complaint_type: str, description: str = None
    ) -> transports.AyahReportResult:
        """Report an ayah with a complaint."""
        from .models import AyahReport

        # Validate user exists
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        # Validate ayah exists
        try:
            surah = Surah.objects.get(surah_number=surah_id)
            ayah = Ayah.objects.get(surah=surah, ayah_number=ayah_number)
        except (Surah.DoesNotExist, Ayah.DoesNotExist):
            raise ValidationError(f"Ayah {ayah_number} in Surah {surah_id} does not exist.")

        # Validate complaint type
        valid_complaints = [choice[0] for choice in AyahReport.COMPLAINT_CHOICES]
        if complaint_type not in valid_complaints:
            raise ValidationError(f"Invalid complaint type. Must be one of: {', '.join(valid_complaints)}")

        # Check if user already reported this ayah
        if AyahReport.objects.filter(user=user, ayah=ayah).exists():
            raise ValidationError("You have already reported this ayah.")

        # Create report
        report = AyahReport.objects.create(user=user, ayah=ayah, complaint_type=complaint_type, description=description)

        return transports.AyahReportResult(
            surah_id=surah_id,
            ayah_number=ayah_number,
            complaint_type=complaint_type,
            is_reported=True,
            message="Ayah reported successfully",
        )

    @staticmethod
    def get_ayah_reaction_status(user_id: int, surah_id: int, ayah_number: int) -> transports.AyahReactionResult:
        """Get current reaction status for an ayah."""
        from .models import AyahReaction

        # Validate user exists
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        # Validate ayah exists
        try:
            surah = Surah.objects.get(surah_number=surah_id)
            ayah = Ayah.objects.get(surah=surah, ayah_number=ayah_number)
        except (Surah.DoesNotExist, Ayah.DoesNotExist):
            raise ValidationError(f"Ayah {ayah_number} in Surah {surah_id} does not exist.")

        # Get user's current reaction
        user_reaction = None
        try:
            reaction = AyahReaction.objects.get(user=user, ayah=ayah)
            user_reaction = reaction.reaction_type
        except AyahReaction.DoesNotExist:
            pass

        # Get reaction counts
        like_count = AyahReaction.objects.filter(ayah=ayah, reaction_type="like").count()
        dislike_count = AyahReaction.objects.filter(ayah=ayah, reaction_type="dislike").count()

        return transports.AyahReactionResult(
            surah_id=surah_id,
            ayah_number=ayah_number,
            user_reaction=user_reaction,
            like_count=like_count,
            dislike_count=dislike_count,
            message="Reaction status retrieved successfully",
        )


class BookmarkService:
    """Service for bookmark operations using UserAyahMM model."""

    @staticmethod
    @transaction.atomic
    def add_bookmark(user_id: int, surah_id: str, ayah_number: int) -> Optional[UserAyahRelation]:
        """Add a bookmark for an Ayah."""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise NotFound("User not found")

        # Get or create the Ayah
        surah = Surah.objects.get(surah_number=surah_id)
        try:
            ayah = Ayah.objects.get(surah=surah, ayah_number=ayah_number)
        except Ayah.DoesNotExist:
            raise NotFound(f"Ayah {ayah_number} in Surah {surah_id} not found")

        # Get or create UserAyahMM relationship
        user_ayah, created = UserAyahMM.objects.get_or_create(user=user, ayah=ayah, defaults={"is_bookmarked": True})

        if not created and not user_ayah.is_bookmarked:
            user_ayah.is_bookmarked = True
            user_ayah.save()

        return UserAyahRepository._make_user_ayah_transport(user_ayah)

    @staticmethod
    @transaction.atomic
    def remove_bookmark(user_id: int, surah_id: str, ayah_number: int) -> bool:
        """Remove a bookmark for an Ayah."""
        try:
            surah = Surah.objects.get(surah_number=surah_id)
            ayah = Ayah.objects.get(surah=surah, ayah_number=ayah_number)
            user_ayah = UserAyahMM.objects.get(user_id=user_id, ayah=ayah)
            user_ayah.is_bookmarked = False
            user_ayah.save()
            return True
        except (Ayah.DoesNotExist, UserAyahMM.DoesNotExist):
            return False

    @staticmethod
    def get_user_bookmarks(user_id: int) -> List[UserAyahRelation]:
        """Get all bookmarks for a user."""
        return UserAyahRepository.get_user_bookmarks(user_id)

    @staticmethod
    def get_user_bookmarks_enhanced(
        user_id: int, language: str = "en"
    ) -> Tuple[List[EnhancedBookmark], LanguageMetadata]:
        """Get all bookmarks for a user with enhanced multi-language content."""
        # Get basic bookmarks
        user_bookmarks = UserAyahRepository.get_user_bookmarks(user_id)

        if not user_bookmarks:
            return [], LanguageMetadata(requested_language=language, used_language="en", fallback_applied=False)

        enhanced_bookmarks = []
        used_language = language
        fallback_applied = False

        for bookmark in user_bookmarks:
            ayah = bookmark.ayah

            # Get Arabic text (always available)
            arabic_text = LanguageRepository.get_ayah_translation_by_locale(ayah.ayah_number, "ar")

            # Get translation in requested language
            translation = LanguageRepository.get_ayah_translation_by_locale(ayah.ayah_number, language)

            # Fallback to English if requested language not available
            if translation is None and language != "en":
                translation = LanguageRepository.get_ayah_translation_by_locale(ayah.ayah_number, "en")
                if translation is not None:
                    used_language = "en"
                    fallback_applied = True

            # Get transliteration in requested language
            transliteration = LanguageRepository.get_ayah_transliteration_by_locale(ayah.ayah_number, language)

            # Fallback to English transliteration if requested language not available
            if transliteration is None and language != "en":
                transliteration = LanguageRepository.get_ayah_transliteration_by_locale(ayah.ayah_number, "en")
                if transliteration is not None and not fallback_applied:
                    used_language = "en"
                    fallback_applied = True

            # Get surah title
            try:
                surah_translation = SurahTranslation.objects.filter(surah_id=ayah.surah_id, locale=language).first()
                if not surah_translation and language != "en":
                    surah_translation = SurahTranslation.objects.filter(surah_id=ayah.surah_id, locale="en").first()
                    if surah_translation and not fallback_applied:
                        used_language = "en"
                        fallback_applied = True

                surah_title = surah_translation.title if surah_translation else None
            except Exception:
                surah_title = None

            # Create enhanced bookmark
            enhanced_bookmark = EnhancedBookmark(
                user_id=bookmark.user_id,
                ayah=ayah,
                surah_title=surah_title,
                arabic_text=arabic_text,
                translation=translation,
                transliteration=transliteration,
                image_url=ayah.image,  # From the ayah transport object
                is_completed=bookmark.is_completed,
                is_bookmarked=bookmark.is_bookmarked,
                created_at=bookmark.created_at,
            )

            enhanced_bookmarks.append(enhanced_bookmark)

        # Create language metadata
        language_metadata = LanguageMetadata(
            requested_language=language, used_language=used_language, fallback_applied=fallback_applied
        )

        return enhanced_bookmarks, language_metadata

    @staticmethod
    def get_bookmarks_count(user_id: int) -> int:
        """Get the total count of bookmarks for a user."""
        return UserAyahMM.objects.filter(user_id=user_id, is_bookmarked=True).count()

    @staticmethod
    def get_recent_bookmarks(user_id: int, limit: int = 5) -> List[transports.RecentBookmark]:
        """Get recent bookmarks for a user with basic information."""
        # Get recent bookmarked ayahs
        recent_user_ayahs = (
            UserAyahMM.objects.filter(user_id=user_id, is_bookmarked=True)
            .select_related("ayah__surah")
            .order_by("-created_at")[:limit]
        )

        recent_bookmarks = []
        for user_ayah in recent_user_ayahs:
            ayah = user_ayah.ayah

            # Get surah title (default to English)
            try:
                surah_translation = SurahTranslation.objects.filter(surah=ayah.surah, locale="en").first()
                surah_title = surah_translation.title if surah_translation else f"Surah {ayah.surah.surah_number}"
            except Exception:
                surah_title = f"Surah {ayah.surah.surah_number}"

            # Get Arabic text
            arabic_text = LanguageRepository.get_ayah_translation_by_locale(ayah.ayah_number, "ar")

            recent_bookmark = transports.RecentBookmark(
                id=user_ayah.ayah.ayah_number,
                surah_id=ayah.surah.surah_number,
                ayah_number=ayah.ayah_number,
                surah_title=surah_title,
                arabic_text=arabic_text,
                created_at=user_ayah.created_at,
            )
            recent_bookmarks.append(recent_bookmark)

        return recent_bookmarks

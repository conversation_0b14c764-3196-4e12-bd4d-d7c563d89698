import React, { useState, useEffect } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import LoadingSpinner from "src/components/LoadingSpinner";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "src/components/Shuffling/Shuffling.css"; // Импортируем стили
import axiosInstance from "src/utils/axios";
import { useParams } from "react-router-dom";
import GameCompletedBar from "src/components/GameCompletedBar/GameCompletedBar";

const Shuffling = ({ selectedAyah, updateProgress, backToLearn }) => {
  const [loading, setLoading] = useState(true);
  const [levels, setLevels] = useState([]);
  const [currentLevel, setCurrentLevel] = useState(0);
  const [phrases, setPhrases] = useState([]);
  const [sentence, setSentence] = useState([]);
  const [isCorrect, setIsCorrect] = useState(false);
  const { id, ayahId } = useParams();

  useEffect(() => {
    const fetchTestData = async () => {
      const response = await axiosInstance.post("/games/shuffling/", {
        id,
        ayahId,
        selectedAyah,
      });
      const testData = response.data.levels;
      setLevels(testData);
      setSentence(testData[0].sentence);
      setPhrases(testData[0].phrases); // Здесь будут полные аяты для перетаскивания
      setLoading(false);
    };

    fetchTestData();
  }, [selectedAyah]);

  const onDragEnd = (result) => {
    const { source, destination } = result;

    if (!destination) return;

    const updatedSentence = [...sentence];
    const updatedPhrases = [...phrases];

    if (
      source.droppableId.startsWith("gap-") &&
      destination.droppableId.startsWith("gap-")
    ) {
      // Перемещение аятов между пропусками
      const sourceGapIndex = updatedSentence.findIndex(
        (part) => part.id === source.droppableId
      );
      const destinationGapIndex = updatedSentence.findIndex(
        (part) => part.id === destination.droppableId
      );

      const temp = updatedSentence[destinationGapIndex].phrase;
      updatedSentence[destinationGapIndex].phrase =
        updatedSentence[sourceGapIndex].phrase;
      updatedSentence[sourceGapIndex].phrase = temp;

      setSentence(updatedSentence);
    } else if (
      source.droppableId === "phrases" &&
      destination.droppableId.startsWith("gap-")
    ) {
      // Перемещение из списка в пропуск (обновлено)
      const gapIndex = updatedSentence.findIndex(
        (part) => part.id === destination.droppableId
      );

      const [movedPhrase] = updatedPhrases.splice(source.index, 1);

      if (updatedSentence[gapIndex].phrase) {
        // Если в пропуске уже есть аят, возвращаем его в список
        const previousPhrase = updatedSentence[gapIndex].phrase;
        updatedPhrases.splice(source.index, 0, previousPhrase);
      }

      updatedSentence[gapIndex].phrase = movedPhrase;

      setPhrases(updatedPhrases);
      setSentence(updatedSentence);
    } else if (
      source.droppableId.startsWith("gap-") &&
      destination.droppableId === "phrases"
    ) {
      // Возвращение аята из пропуска в список
      const gapIndex = updatedSentence.findIndex(
        (part) => part.id === source.droppableId
      );
      const movedPhrase = updatedSentence[gapIndex].phrase;

      if (movedPhrase) {
        updatedSentence[gapIndex].phrase = null;

        updatedPhrases.splice(destination.index, 0, movedPhrase);
        setPhrases(updatedPhrases);
        setSentence(updatedSentence);
      }
    }

    checkSentence();
  };

  const checkSentence = () => {
    const userSequence = sentence
      .filter((part) => part.type === "gap")
      .map((gap) => gap.phrase?.id);
    const correct =
      JSON.stringify(userSequence) ===
      JSON.stringify(levels[currentLevel].correctSequence);
    setIsCorrect(correct);
    if (correct) {
      // toast.success("Correct Sentence!");
      updateProgress().catch((error) =>
        console.error("Error updating progress", error)
      );
    }
  };

  if (loading) return <LoadingSpinner />;

  return (
    <>
      <ToastContainer
        position="top-center"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />

      <DragDropContext onDragEnd={onDragEnd}>
        <div className="sentence-container" dir="rtl">
          {sentence.map((part, index) => {
            if (part.type === "text") {
              return (
                <span key={index} className="sentence-text">
                  {part.content}
                </span>
              );
            } else {
              return (
                <Droppable key={part.id} droppableId={part.id}>
                  {(provided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`sentence-gap ${part.phrase ? "filled" : ""}`} // Проверка на наличие фразы
                    >
                      {part.phrase ? (
                        <Draggable
                          key={part.phrase.id}
                          draggableId={part.phrase.id}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className={`phrase-card ${
                                part.phrase ? "in-gap" : ""
                              } ${snapshot.isDragging ? "dragging" : ""}`}
                            >
                              {part.phrase.text}
                            </div>
                          )}
                        </Draggable>
                      ) : null}{" "}
                      {/* Пропуск, если пуст */}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              );
            }
          })}
        </div>
        {isCorrect ? (
          <GameCompletedBar result={isCorrect} backToLearn={backToLearn} />
        ) : (
          <Droppable droppableId="phrases" direction="horizontal">
            {(provided) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className="phrases-container"
              >
                {phrases.map((phrase, index) => (
                  <Draggable
                    key={phrase.id}
                    draggableId={phrase.id}
                    index={index}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className={`phrase-card ${
                          snapshot.isDragging ? "dragging" : ""
                        }`} // Добавили стиль карточки
                      >
                        {phrase.text}{" "}
                        {/* Отображение текста аята для перетаскивания */}
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        )}
      </DragDropContext>
    </>
  );
};

export default Shuffling;
